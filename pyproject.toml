[project]
name = "repl-nix-workspace"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.11"
dependencies = [
    "email-validator>=2.2.0",
    "flask-login>=0.6.3",
    "flask>=3.1.0",
    "flask-sqlalchemy>=3.1.1",
    "gunicorn>=23.0.0",
    "psycopg2-binary>=2.9.10",
    "firebase-admin>=6.8.0",
    "pyjwt>=2.10.1",
    "pillow>=11.2.1",
    "opencv-python>=*********",
    "pytesseract>=0.3.13",
    "numpy>=2.2.5",
    "werkzeug>=3.1.3",
]
