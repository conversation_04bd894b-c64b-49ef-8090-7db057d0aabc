{"name": "Expo/React Native + Python Development", "image": "mcr.microsoft.com/devcontainers/javascript-node:20-bullseye", "forwardPorts": [3000, 8000, 19000, 19001, 19002, 4000, 8080], "containerEnv": {"PNPM_HOME": "/usr/local/share/pnpm", "PATH": "${PNPM_HOME}:${PATH}"}, "features": {"ghcr.io/devcontainers/features/python:1": {"version": "3.11", "installTools": true}, "ghcr.io/devcontainers-contrib/features/firebase-cli:2": {"version": "latest"}, "ghcr.io/devcontainers-contrib/features/google-cloud-cli:1": {"version": "latest"}, "ghcr.io/devcontainers-contrib/features/pnpm:2": {"version": "latest"}}, "customizations": {"vscode": {"extensions": ["dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "ms-python.python", "ms-python.vscode-pylance", "ms-azuretools.vscode-docker", "ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "dsznajder.es7-react-js-snippets", "msjsdiag.vscode-react-native", "expo.vscode-expo-tools", "GitHub.copilot", "GitHub.copilot-chat", "eamodio.gitlens", "streetsidesoftware.code-spell-checker", "yoavbls.pretty-ts-errors", "formulahendry.auto-rename-tag", "naumovs.color-highlight", "csstools.postcss", "mikestead.dotenv", "ms-vsliveshare.vsliveshare"], "settings": {"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "editor.defaultFormatter": "esbenp.prettier-vscode", "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "python.formatting.provider": "black", "python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.analysis.typeCheckingMode": "basic", "terminal.integrated.scrollback": 10000, "terminal.integrated.defaultProfile.linux": "bash", "files.associations": {"*.env.*": "dotenv"}}}}, "postCreateCommand": "pnpm setup && pnpm install -g expo-cli turbo typescript && pip install --upgrade pip && pip install fastapi uvicorn black pylint pytest", "postStartCommand": "git config --global core.editor 'code --wait' && git config --global pull.rebase true", "remoteUser": "node", "updateContentCommand": "pnpm install"}