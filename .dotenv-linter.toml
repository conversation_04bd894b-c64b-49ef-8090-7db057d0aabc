# .dotenv-linter.toml
# Configuration for dotenv-linter to enforce best practices for .env files

# General configuration
[general]
# Exclude certain files from linting
exclude_files = ["node_modules/**", "dist/**", ".git/**"]
# Include specific files for linting (including sample files)
include_files = [".env*"]

# Variable naming rules
[naming]
# Enforce uppercase variable names with underscores
pattern = "^[A-Z][A-Z0-9_]*$"
# Allow certain exceptions (if needed)
exceptions = []

# Formatting rules
[format]
# Require comments for all variables
require_comments = true
# Minimum comment length (characters)
min_comment_length = 10
# Require a space after the comment marker
space_after_comment = true
# Require values for all variables (can be empty strings)
require_values = true
# Maximum line length
max_line_length = 120
# Disallow trailing whitespace
no_trailing_whitespace = true
# Require consistent spacing around equals sign
consistent_spacing = true
# Number of spaces before comment (when on same line as variable)
spaces_before_comment = 2

# Section rules
[sections]
# Require section headers (comments starting with #--- or # ---)
require_section_headers = true
# Pattern for valid section headers
section_header_pattern = "^#\\s*[-]{3}\\s+[A-Za-z0-9 ]+\\s+[-]{3,}$"
# Require empty line after section headers
empty_line_after_section = true

# Value rules
[values]
# Require quotes around values with spaces
quote_values_with_spaces = true
# Allowed quote characters
allowed_quote_chars = ["\"", "'"]
# Disallow hardcoded credentials (placeholder validation)
disallow_hardcoded_credentials = true
# Patterns indicating placeholder values (acceptable)
placeholder_patterns = ["<.*>", "your-.*", "placeholder", "changeme", "xxxxx"]

# Order rules
[order]
# Group variables by prefix
group_by_prefix = true
# Sort alphabetically within groups
alphabetical = true
