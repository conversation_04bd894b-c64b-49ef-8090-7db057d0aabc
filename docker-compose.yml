version: '3.8'

services:
  # Backend service
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app/backend
      - ./pyproject.toml:/app/pyproject.toml
    environment:
      - MONGO_URI=mongodb://mongo:27017
      - M<PERSON><PERSON><PERSON>_DB_NAME=aiclearbill
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
    depends_on:
      - mongo
      - redis
    networks:
      - app-network
    restart: unless-stopped

  # Frontend service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
    depends_on:
      - backend
    networks:
      - app-network
    restart: unless-stopped

  # MongoDB service
  mongo:
    image: mongo:4.4
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    networks:
      - app-network
    restart: unless-stopped

  # Redis service
  redis:
    image: redis:6
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - app-network
    restart: unless-stopped

  # Celery worker
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile
    command: celery -A backend.workers.celery_app worker --loglevel=info
    volumes:
      - ./backend:/app/backend
      - ./pyproject.toml:/app/pyproject.toml
    environment:
      - MONGO_URI=mongodb://mongo:27017
      - MONGO_DB_NAME=aiclearbill
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
    depends_on:
      - mongo
      - redis
      - backend
    networks:
      - app-network
    restart: unless-stopped

networks:
  app-network:
    driver: bridge

volumes:
  mongo-data:
  redis-data:
