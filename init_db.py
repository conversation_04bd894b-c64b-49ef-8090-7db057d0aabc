"""
Database initialization script for AIClearBill.
This script creates sample data for development/testing.
"""

import datetime
import sys

from api.models.invoice import Invoice, VATLine
from api.models.tenant import Tenant
from api.models.user import User
from api.models.vendor import Vendor
from app import app, db


def create_sample_data():
    """Create sample data for testing"""
    print("Creating sample data for AIClearBill...")

    # Create a test tenant
    tenant = Tenant(
        name="Test Company",
        default_currency="USD",
        default_language="en-US",
        timezone="UTC",
    )
    db.session.add(tenant)
    db.session.commit()
    print(f"Created tenant: {tenant.name} (ID: {tenant.id})")

    # Create a test admin user
    admin_user = User(
        username="admin",
        email="<EMAIL>",
        firebase_uid="admin123",  # This would be a real Firebase UID in production
        first_name="Admin",
        last_name="User",
        roles="admin,user",
        tenant_id=tenant.id,
        last_login=datetime.datetime.utcnow(),
    )
    db.session.add(admin_user)
    db.session.commit()
    print(f"Created admin user: {admin_user.email} (ID: {admin_user.id})")

    # Create sample vendors
    vendors = [
        {
            "name": "Office Supplies Co",
            "email": "<EMAIL>",
            "phone": "(*************",
            "address": "123 Main St, Suite 101, Anytown, CA 90210",
            "website": "https://officesupplies.example.com",
            "default_account": "Office Expenses",
            "default_tax_code": "VAT10",
        },
        {
            "name": "Web Hosting Services",
            "email": "<EMAIL>",
            "phone": "(*************",
            "address": "456 Tech Blvd, Server Room 5, Techville, CA 90211",
            "website": "https://webhosting.example.com",
            "default_account": "IT Expenses",
            "default_tax_code": "VAT20",
        },
        {
            "name": "Marketing Agency Inc",
            "email": "<EMAIL>",
            "phone": "(*************",
            "address": "789 Creative Ave, Marketland, CA 90212",
            "website": "https://marketing.example.com",
            "default_account": "Marketing Expenses",
            "default_tax_code": "VAT20",
        },
    ]

    created_vendors = []
    for vendor_data in vendors:
        vendor = Vendor(
            name=vendor_data["name"],
            email=vendor_data["email"],
            phone=vendor_data["phone"],
            address=vendor_data["address"],
            website=vendor_data["website"],
            default_account=vendor_data["default_account"],
            default_tax_code=vendor_data["default_tax_code"],
            tenant_id=tenant.id,
            created_by=admin_user.id,
        )
        db.session.add(vendor)
        created_vendors.append(vendor)

    db.session.commit()
    print(f"Created {len(created_vendors)} vendors")

    # Create sample invoices
    invoices = [
        {
            "file_name": "invoice_office_supplies.pdf",
            "file_type": "application/pdf",
            "status": "READY",
            "invoice_number": "INV-10025",
            "invoice_date": datetime.date(2025, 4, 28),
            "total_amount": 245.50,
            "memo": "Office supplies for Q2",
            "validated": True,
            "vendor_id": created_vendors[0].id,  # Office Supplies Co
            "vat_lines": [{"amount": 22.32, "tax_code": "VAT10", "tax_rate": 10.0}],
        },
        {
            "file_name": "invoice_web_hosting.pdf",
            "file_type": "application/pdf",
            "status": "READY",
            "invoice_number": "INV-10024",
            "invoice_date": datetime.date(2025, 4, 25),
            "total_amount": 89.95,
            "memo": "Monthly web hosting",
            "validated": True,
            "vendor_id": created_vendors[1].id,  # Web Hosting Services
            "vat_lines": [{"amount": 15.00, "tax_code": "VAT20", "tax_rate": 20.0}],
        },
        {
            "file_name": "invoice_marketing.pdf",
            "file_type": "application/pdf",
            "status": "PENDING",
            "invoice_number": "INV-10023",
            "invoice_date": datetime.date(2025, 4, 22),
            "total_amount": 1250.00,
            "memo": "Social media campaign",
            "validated": False,
            "vendor_id": created_vendors[2].id,  # Marketing Agency Inc
            "vat_lines": [{"amount": 208.33, "tax_code": "VAT20", "tax_rate": 20.0}],
        },
    ]

    created_invoices = []
    for invoice_data in invoices:
        invoice = Invoice(
            file_url=f"/uploads/{tenant.id}/{invoice_data['file_name']}",
            file_name=invoice_data["file_name"],
            file_type=invoice_data["file_type"],
            file_size=1024,  # Placeholder value
            status=invoice_data["status"],
            invoice_number=invoice_data["invoice_number"],
            invoice_date=invoice_data["invoice_date"],
            total_amount=invoice_data["total_amount"],
            memo=invoice_data["memo"],
            validated=invoice_data["validated"],
            currency="USD",
            tenant_id=tenant.id,
            vendor_id=invoice_data["vendor_id"],
            created_by=admin_user.id,
            created_at=datetime.datetime.utcnow()
            - datetime.timedelta(days=len(created_invoices)),
            processed_at=(
                datetime.datetime.utcnow()
                if invoice_data["status"] == "READY"
                else None
            ),
        )
        db.session.add(invoice)
        db.session.flush()  # Flush to get invoice ID

        # Add VAT lines
        for vat_data in invoice_data["vat_lines"]:
            vat_line = VATLine(
                amount=vat_data["amount"],
                tax_code=vat_data["tax_code"],
                tax_rate=vat_data["tax_rate"],
                invoice_id=invoice.id,
            )
            db.session.add(vat_line)

        created_invoices.append(invoice)

    db.session.commit()
    print(f"Created {len(created_invoices)} invoices")

    print("Sample data creation complete!")


if __name__ == "__main__":
    with app.app_context():
        # Check if we should reset the database first
        if len(sys.argv) > 1 and sys.argv[1] == "--reset":
            print("Dropping all tables...")
            db.drop_all()
            print("Creating all tables...")
            db.create_all()

        # Create sample data
        create_sample_data()
