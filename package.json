{"name": "billsnapp", "version": "0.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "npm run lint:env && turbo run lint", "lint:env": "node scripts/check-env-vars.js", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "type-check": "turbo run type-check", "test": "turbo run test", "prepare": "husky install"}, "dependencies": {"dotenv": "^16.3.1"}, "devDependencies": {"@commitlint/cli": "^18.6.0", "@commitlint/config-conventional": "^18.6.0", "eslint": "^8.56.0", "husky": "^9.0.7", "lint-staged": "^15.2.0", "prettier": "^3.0.3", "turbo": "latest"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.2", "lint-staged": {"apps/**/*.{js,ts,jsx,tsx}": ["eslint --fix"], "packages/**/*.{js,ts,jsx,tsx}": ["eslint --fix"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}}