# API Reference

_A quick-start guide to calling AiClearBill’s back-end services._

---

## 1 · Dual-API Architecture

| Runtime | Location | Purpose | Typical Port |
|---------|----------|---------|--------------|
| **TypeScript tRPC API** | `apps/api` | Core business logic, OCR orchestration, accounting integration | `:3001` |
| **Python FastAPI** | `backend/` | Legacy endpoints & Celery task hooks (migrating to TS) | `:8000` |

Both APIs sit behind Firebase Auth; a valid ID-token is required in the `Authorization: Bearer <token>` header for every request.

---

## 2 · Base URLs (local dev)

```
# TypeScript tRPC
http://localhost:3001/trpc

# Python FastAPI
http://localhost:8000/api/v1
```

> In production these are served behind a single gateway / Cloud Run service—see deployment configs.

---

## 3 · tRPC Overview

tRPC gives you **end-to-end type-safety**: the same Zod-validated input/output types are available in the React client via the generated `trpc.tsx` hook package—no Swagger required.

Client usage:

```ts
// Example: fetch current user
const { data: me } = trpc.auth.me.useQuery();
```

---

## 4 · Main Routers & Responsibilities

| Router (file) | Path prefix | Key Procedures |
|---------------|-------------|----------------|
| `auth.router.ts` | `auth.*` | `login`, `logout`, `me`, token refresh |
| `accounting.ts` | `accounting.*` | Connect QBO, bulk push, status, account list |
| `customers.router.ts` | `customers.*` | CRUD & sync with QBO |
| `invoices.router.ts` | `invoices.*` | List, create, update, bulk actions, push to accounting |
| `ocr.router.ts` | `ocr.*` | Generate upload URL, check image quality, processing status |
| `tenants.router.ts` | `tenants.*` | Tenant CRUD, invite users |
| `users.router.ts` | `users.*` | User management, role updates |
| `vendors.router.ts` | `vendors.*` | CRUD & sync vendors |
| `webhooks.router.ts` | `webhooks.*` | QuickBooks webhook receiver (internal) |

Full procedure signatures are discoverable in code (`apps/api/src/routes/*`) and via TypeScript intellisense when importing the generated client.

---

## 5 · Authentication Flow

1. **Frontend** signs-in with Firebase Auth (Email Link / Google / Apple / Intuit).  
2. Retrieve the Firebase ID token (`getIdToken()`).
3. Include the token in every request header:

```
Authorization: Bearer <firebase-id-token>
```

tRPC client sets this automatically via `AuthContext`.

---

## 6 · OCR Endpoints

The OCR tiered pipeline (Gemma → Gemini → Document AI) is exposed via `ocr.*` procedures.  
Detailed design, escalation logic, and test instructions are documented in **`apps/api/README.md`**—refer there for deep dives.

---

## 7 · Python FastAPI Surface (legacy)

| Endpoint | Verb | Notes |
|----------|------|-------|
| `/api/v1/auth/login` | POST | Temporary support for early mobile build |
| `/api/v1/invoices` | CRUD | Mirrors the new TS routes |
| `/api/v1/qbo/*` | misc | OAuth callback helper |

All new development happens in the TypeScript API; Python routes will be deprecated once migration is complete.

---

## 8 · Error Model

Both APIs return JSON with a consistent shape:

```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Missing or invalid token"
  }
}
```

tRPC errors map to gRPC-style codes; see `apps/api/src/trpc` for helpers.

---

## 9 · Next Steps for Developers

1. Run `pnpm dev` (or `docker-compose up`)  
2. Use the generated `trpc` React hooks or call `/trpc` endpoints directly.  
3. Need more detail?  
   * OCR internals → **apps/api/README.md**  
   * Accounting connector → **docs/accounting-connector-guide.md**  
   * RBAC & custom claims → **RBAC.md**

Happy hacking 👋
