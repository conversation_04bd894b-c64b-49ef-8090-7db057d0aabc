# Environment Variables Guide

_Comprehensive reference for configuring **AiClearBill / Billsnapp** across every service and environment._

---

## 1 · Overview

AiClearBill is a **poly-repo** that runs three independent runtimes:

| Service | Location | Runtime | Default Port |
| ------- | -------- | ------- | ------------ |
| TypeScript API (tRPC) | `apps/api` | Node 18 | `3001` |
| Web PWA | `apps/web` | Vite / React | `5173` (dev) |
| Python Backend (legacy OCR & tasks) | `backend` | Python 3.12 / Uvicorn | `8000` |

Each service loads its own `.env` file at runtime (generated by `setup.sh` on first run).  
Variables are **namespaced** per service to avoid collisions yet stay familiar.

---

## 2 · Quick Start

```bash
# 1 · Clone & bootstrap – creates skeleton .env files with placeholders
./setup.sh           # idempotent

# 2 · Open the new .env files and fill in the 🔑 required values
apps/api/.env
apps/web/.env
backend/.env  (only needed if you plan to run the Python stack)

# 3 · Run
pnpm dev             # Node API + Web
docker-compose up    # optional: <PERSON><PERSON>, <PERSON><PERSON>, Celery workers
```

`setup.sh` **pre-populates**:

* Sensible defaults for local emulators (ports, local buckets)
* Place-holders for _secrets_ you **must** add (`***_KEY`, `***_ID`, etc.)

---

## 3 · Variable Matrix

Legend: **(R)** Required   |   _(O)_ Optional   |   *(auto)* populated by `setup.sh`

### 3.1 Firebase

| Var | Service(s) | R/O | Description |
| --- | ---------- | --- | ----------- |
| `FIREBASE_PROJECT_ID` | API · Web · Python | **R** | Firebase project / emulator ID |
| `FIREBASE_PRIVATE_KEY_ID` | API · Python | **R** | Admin SDK key id |
| `FIREBASE_PRIVATE_KEY` | API · Python | **R** | **Multiline** private key. Escape newlines (`\n`) in `.env` |
| `FIREBASE_CLIENT_EMAIL` | API · Python | **R** | Admin service account e-mail |
| `FIREBASE_CLIENT_ID` | API · Python | _(O)_ | Optional but recommended |
| `VITE_FIREBASE_API_KEY` | Web | **R** | JS SDK key (prefix with `VITE_`) |
| `VITE_FIREBASE_AUTH_DOMAIN` | Web | **R** | `project-id.firebaseapp.com` |
| `VITE_FIREBASE_STORAGE_BUCKET` | Web | _(auto)_ | From `setup.sh` |
| `VITE_FIREBASE_APP_ID` | Web | **R** | App ID from Firebase console |
| other `VITE_FIREBASE_*` vars | Web | _(O)_ | Messaging, analytics, etc. |

### 3.2 Google Cloud / OCR

| Var | Service(s) | R/O | Notes |
| --- | ---------- | --- | ----- |
| `GOOGLE_PROJECT_ID` | API | **R** | GCP project that hosts Document AI / PubSub |
| `GOOGLE_APPLICATION_CREDENTIALS` | API | **R** | Path to service-account JSON (relative or absolute) |
| `GOOGLE_STORAGE_BUCKET` | API | **R** | GCS bucket for raw invoice images |
| `DOCUMENT_AI_PROCESSOR_ID` | API | _(O)_ | Vertex Document AI processor for Tier 3 |
| `VERTEX_AI_LOCATION` | API | **R** | e.g. `us-west1` |
| `VERTEX_AI_GEMINI_MODEL` | API | _(auto)_ `gemini-2.0-flash-001` |
| `GEMINI_API_KEY` | Python | **R** | When Python backend calls Gemini directly |
| `GEMINI_MODEL` | Python | _(auto)_ | Default model name |

### 3.3 Pub/Sub & Cloud Tasks

| Var | Service(s) | R/O | Purpose |
| --- | ---------- | --- | ------- |
| `GOOGLE_PUBSUB_INVOICE_PROCESSING_TOPIC` | API | **R** | Async invoice topic |
| `GOOGLE_PUBSUB_INVOICE_PROCESSING_SUBSCRIPTION` | API | _(auto)_ | Local sub name |

### 3.4 Database

| Var | Service(s) | R/O | Description |
| --- | ---------- | --- | ----------- |
| `MONGO_URI` | API · Python | **R** | Connection string (use MongoDB Atlas or `mongodb://mongo:27017`) |
| `MONGO_DBNAME`/`MONGO_DB_NAME` | API · Python | **R** | DB name (default `aiclearbill`) |
| `REDIS_HOST` | Python (Celery) | _(O)_ | Queue broker host (default `redis`) |
| `REDIS_PORT` | Python | _(auto)_ | 6379 |
| `REDIS_DB`   | Python | _(auto)_ | 0 |

### 3.5 QuickBooks Online (Accounting Connector)

| Var | Service(s) | R/O | Description |
| --- | ---------- | --- | ----------- |
| `QBO_CLIENT_ID` | API · Python | **R** (prod) | OAuth 2 client id |
| `QBO_CLIENT_SECRET` | API · Python | **R** | OAuth 2 secret |
| `QBO_REDIRECT_URI` | API · Python | **R** | Must match Intuit console |
| `QBO_ENVIRONMENT` | API · Python | _(auto)_ | `sandbox` / `production` |

### 3.6 Server & Runtime

| Var | Service | Default | Purpose |
| --- | ------- | ------- | ------- |
| `NODE_ENV` | API | `development` | Node environment |
| `PORT` | API | `3001` | API port |
| `VITE_API_URL` | Web | `http://localhost:3001` | URL to tRPC gateway |
| `DEBUG` | Python | `true` | FastAPI debug flag |
| `LOG_LEVEL` | all | `debug` | Pino / Uvicorn log level |

### 3.7 Misc / 3rd-Party

Add as required:

| Var | Example Use |
| --- | ----------- |
| `SENTRY_DSN` | Error tracking |
| `STRIPE_SECRET_KEY` | Billing module (future) |

---

## 4 · setup.sh Auto-Generation

`setup.sh` performs:

1. **Dependency install** (`pnpm install` + shared-types build)  
2. **TensorFlow models download** for client-side quality gate  
3. **Creation of**  
   * `apps/api/.env`  
   * `apps/web/.env`  
   * _Optional_ messages for `backend/.env`  
4. Fills defaults (`VERTEX_AI_LOCATION`, local ports, feature toggles)  
5. Leaves **blank placeholders** (`FIREBASE_PRIVATE_KEY`, `QBO_CLIENT_SECRET`, …) you _must_ edit.

You can safely re-run the script; it will **not overwrite** existing `.env` files.

---

## 5 · Local vs Production Tips

| Concern | Local Dev | Production |
| ------- | --------- | ---------- |
| Firebase | Use **emulators** (`firebase emulators:start`) – no extra vars | Service-account vars must be real and secrets stored in Secret Manager |
| GCP Credentials | JSON key on disk, path via `GOOGLE_APPLICATION_CREDENTIALS` | Use Workload Identity or Secret Manager, not plaintext |
| Mongo / Redis | `docker-compose up` to spin local instances | Fully-managed Atlas / Redis Cloud |
| JWT Secrets | N/A (Firebase) | Rotate service account keys regularly |
| .env files | Committed as `*.example` only | Never commit populated `.env` – use CI secrets |

---

## 6 · Example `.env` Snippets

### apps/api/.env (local)

```
NODE_ENV=development
PORT=3001

# Firebase Admin
FIREBASE_PROJECT_ID=billsnapp-dev
FIREBASE_PRIVATE_KEY_ID=xxxxxxxxxxxx
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nABC...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Google Cloud
GOOGLE_PROJECT_ID=billsnapp-dev
GOOGLE_APPLICATION_CREDENTIALS=./service-account.json
GOOGLE_STORAGE_BUCKET=billsnapp-invoices-dev
VERTEX_AI_LOCATION=us-west1
VERTEX_AI_GEMINI_MODEL=gemini-2.0-flash-001

# Database
MONGO_URI=mongodb://localhost:27017
MONGO_DBNAME=aiclearbill
```

### apps/web/.env (local)

```
VITE_API_URL=http://localhost:3001

VITE_FIREBASE_API_KEY=AIza...
VITE_FIREBASE_AUTH_DOMAIN=billsnapp-dev.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=billsnapp-dev
VITE_FIREBASE_STORAGE_BUCKET=billsnapp-dev.appspot.com
VITE_FIREBASE_APP_ID=1:**********:web:abcdef
```

---

## 7 · Security Best Practices

1. **Never commit populated `.env` files** – only `*.example`.  
2. Encrypt secrets at rest (GCP Secret Manager, GitHub Actions secrets).  
3. Restrict OAuth credentials to allowed redirect URIs.  
4. Use **Google KMS** (already implemented in API) to encrypt 3rd-party tokens at rest.  
5. Rotate keys periodically and automate revocation via CI.

---

## 8 · Further Reading

* Project Roadmap – [`ROADMAP.md`](../ROADMAP.md)  
* Stack Overview – [`STACK.md`](../STACK.md)  
* RBAC Details – [`RBAC.md`](../RBAC.md)  
* Accounting Connector Guide – [`docs/accounting-connector-guide.md`](./accounting-connector-guide.md)  
* `setup.sh` source – root script for reference

---

_Questions or missing variables? Create an issue or ping @Jpkay!_
