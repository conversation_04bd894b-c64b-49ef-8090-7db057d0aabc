sequenceDiagram
    participant <PERSON><PERSON> as Frontend (React PWA)
    participant TS as TypeScript API<br>(Fastify + tRPC)
    participant P<PERSON> as Python Backend<br>(FastAPI OCR)
    participant GPU as GPU Service<br>(Gemma 3-4B)
    participant Gemini as Vertex AI<br>(Gemini 2.0)
    participant <PERSON><PERSON><PERSON> as Document AI<br>(Invoice Parser)
    participant DB as Firestore/MongoDB

    Note over PWA,DB: Current Dual-Backend Architecture

    %% Initial invoice upload flow
    PWA->>TS: Upload invoice image
    activate TS
    TS->>TS: Authenticate user
    TS->>TS: Validate tenant access
    TS->>DB: Store invoice metadata
    TS->>TS: Generate signed URL
    TS-->>PWA: Return upload URL
    deactivate TS

    PWA->>PWA: Upload to Cloud Storage
    PWA->>TS: Confirm upload & request OCR
    activate TS
    TS->>DB: Update invoice status (PROCESSING)
    
    %% OCR Processing Path 1 - TypeScript orchestrator
    rect rgb(240, 240, 255)
    Note over TS,DB: Path 1: TypeScript OCR Orchestration
    TS->>TS: Create OCR job
    TS->>TS: Publish to Pub/Sub topic
    TS-->>PWA: Return job ID
    deactivate TS
    
    Note over TS: OCR Orchestrator picks up job
    activate TS
    TS->>TS: Determine starting tier
    
    %% Tier 1 - GPU processing
    TS->>GPU: Process with Tier 1 (Gemma)
    activate GPU
    GPU-->>TS: Return extraction results
    deactivate GPU
    
    TS->>TS: Quality check results
    
    alt Quality check failed
        %% Tier 2 - Gemini processing
        TS->>Gemini: Escalate to Tier 2
        activate Gemini
        Gemini-->>TS: Return extraction results
        deactivate Gemini
        
        TS->>TS: Quality check results
        
        alt Quality check failed
            %% Tier 3 - Document AI
            TS->>DocAI: Escalate to Tier 3
            activate DocAI
            DocAI-->>TS: Return extraction results
            deactivate DocAI
        end
    end
    
    TS->>DB: Store final extraction results
    TS->>TS: Publish completion event
    deactivate TS
    end
    
    %% OCR Processing Path 2 - Python backend
    rect rgb(255, 240, 240)
    Note over TS,DB: Path 2: Legacy Python OCR Processing
    TS->>PY: Request OCR processing
    activate PY
    PY->>PY: Log request
    
    PY->>PY: Process file (PDF/image)
    
    alt PDF File
        PY->>PY: Convert PDF to image
    end
    
    %% Python OCR processing
    PY->>Gemini: Process with Gemini
    activate Gemini
    Gemini-->>PY: Return OCR results
    deactivate Gemini
    
    alt Gemini fails
        PY->>PY: Fallback to Tesseract OCR
    end
    
    PY->>PY: Normalize invoice data
    PY->>PY: Error handling
    PY->>DB: Update invoice with results
    PY-->>TS: Return processing results
    deactivate PY
    end
    
    %% Frontend polls for results
    PWA->>TS: Poll for OCR status
    activate TS
    TS->>DB: Query invoice status
    DB-->>TS: Return current status
    TS-->>PWA: Return processing status
    deactivate TS
    
    %% Completion
    PWA->>PWA: Display extracted data
    
    %% Highlight duplicated concerns
    Note over TS,PY: Duplicated Concerns:<br>• Logging systems<br>• Error handling<br>• Authentication<br>• Firestore access<br>• OCR processing logic
