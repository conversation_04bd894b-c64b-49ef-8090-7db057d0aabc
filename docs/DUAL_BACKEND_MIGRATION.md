# Dual-Backend Migration Strategy (P1-05)

_Last updated: 2025-05-30_

## 1. Executive Summary

<PERSON>nap<PERSON> currently runs **two independent backends**:

1. **TypeScript API** – `apps/api/` (Fastify + tRPC)  
   • Handles auth/RBAC, Firestore, QuickBooks, and already contains a full `OcrOrchestrator` with tiered Gemma ➜ Gemini ➜ Document AI logic.

2. **Python OCR Orchestrator** – `backend/` (FastAPI)  
   • Legacy service that duplicates OCR orchestration and exposes a subset of now-redundant endpoints.

Maintaining both increases cognitive load, duplicates logging/error-handling, and doubles cold-start/runtime cost.  
**Decision:** _Fully migrate OCR orchestration into the TypeScript service and deprecate the Python backend._  
This document is the single source-of-truth plan to achieve that, unblock Roadmap items **CE-01** and **CE-02**, and remove ⚠ warnings.

---

## 2. Current-State Analysis

See `docs/img/current-architecture.svg` for the full sequence.  Key points:

| Layer | Tech | Responsibility | Notes |
|-------|------|----------------|-------|
| Frontend (PWA) | React + Vite | Capture & upload invoices | Calls `/trpc/ocr.*` |
| **TypeScript API** | Fastify + tRPC | Auth, RBAC, Firestore, QuickBooks, **_partial_** OCR | Owns `OcrOrchestrator` but still proxies to Python in some modes |
| **Python Backend** | FastAPI | OCR orchestration, Tesseract/Gemini helpers | Duplicates quality checks, logging, Pub/Sub handling |
| GPU Service | Docker (Gemma 3-4B) | Tier-1 extraction | Lang-agnostic HTTP |
| Vertex AI & Document AI | Managed | Tier-2 & Tier-3 extraction | Called by both backends |
| Data Stores | Firestore / MongoDB | Invoice + metadata | Both backends write |

Duplicated concerns:
* Logging configuration (`logger` vs `logging`)
* Error translation & HTTP codes
* Firestore writes / schema evolution
* Pub/Sub subscription logic

---

## 3. Migration Options

| Option | Pros | Cons | Impact on OCR Tiers | CapEx / OpEx* |
|--------|------|------|---------------------|---------------|
| **A. Full TS Migration (Deprecate Python)** | • Single runtime & codebase<br>• End-to-end type-safety (Zod + tRPC)<br>• Re-use `OcrOrchestrator` & shared models<br>• Fewer cold starts; lower latency | • One-time porting effort<br>• Re-implement minor Python-only helpers | **None** – tiers already abstracted behind HTTP/Vertex AI; only orchestrator moves | ★★★ (dev) / **−37 %** (run) |
| B. Long-Term Hybrid | • Zero porting today<br>• Keep Python ML tooling comfort | • Permanent duplication & higher latency<br>• Complex CI/CD & monitoring<br>• Two sets of IAM, secrets, logs | Continues to proxy Tier calls through Python | ★ (dev) / **+28 %** (run) |
| C. All-Python (Drop TS) | • Single language (Python) | • Throw away months of TS work<br>• Lose tRPC, typed clients<br>• Larger container images; slower cold-starts | N/A – would need to port **all** business logic to Python | ★★★★★ (dev) / **+18 %** (run) |

_*OpEx numbers are rough based on Cloud Run billable-time simulations._

---

## 4. Recommendation

**Choose Option A – Full TypeScript Migration**

Layman terms  
• One backend is cheaper to run and easier to debug.  
• Engineers only need Node.js to contribute; no dual skillset.  

Technical terms  
• Type inference from upload ➜ `InvoiceOcrResult` all the way to React saves bugs.  
• Fastify + V8 isolates start faster than CPython.wsgi on Cloud Run.  
• The existing `OcrOrchestrator`, Pub/Sub handlers, and Zod schemas already replicate 90 % of Python logic.

---

## 5. Phased Timeline

| Phase | Owner (placeholder) | Tasks | Exit Criteria |
|-------|--------------------|-------|---------------|
| **0 – Freeze Python** _(DONE)_ | PM | • Label `backend/` as **frozen** in README | No new Python PRs after 2025-05-30 |
| **1 – Port Orchestration** | TS team | • Move remaining logic from `backend/services/ocr.py` & `invoice_processor.py` into `apps/api/src/services/ocrService.ts`<br>• Add missing image→PDF conversion helper (sharp/pdf-lib) | `npm test` passes; Python no longer called from `ocr.router.ts` |
| **2 – Shared Schema** | API team | • Create `packages/shared-types/src/dtos/InvoiceOcrResult.ts` (Zod)<br>• Replace ad-hoc interfaces in both web & api<br>• Update Vitest and frontend tests | All OCR procedures return validated `InvoiceOcrResult` |
| **3 – CI & Infra Cleanup** | Dev Ops | • Remove `pytest` job from `monorepo-ci.yml`<br>• Delete `backend` Cloud Run service & terraform blocks<br>• Drop Pub/Sub subscription `invoice-processing-subscription` if unused | Green CI; Terraform plan shows zero Python resources |
| **4 – Stabilization Window** | QA | • Run k6 contract tests against `/trpc/ocr.*`<br>• Monitor error budget for 30 days | < 0.1 % OCR error rate delta vs baseline |
| **5 – Delete `backend/` Tree** | Repo Maintainer | • `git rm -r backend/`<br>• Purge docs & references | Repo no longer contains Python backend |

---

## 6. Risk Assessment & Mitigation

| Risk | Likelihood | Impact | Mitigation |
|------|-----------|--------|-----------|
| Hidden Python-only endpoints consumed by integrations | M | H | Run **k6** contract suite against staging; grep Cloud Run logs for `/v1/` hits |
| Confidence scoring discrepancies after port | M | M | Golden-dataset comparison (100 invoices) before Phase 3 |
| Pub/Sub dead-letters spike after cut-over | L | M | Temporary dual subscription with DLQ alerting for 1 week |
| Devs missing Python utilities (e.g., Tesseract fallback) | L | L | Port critical helpers; document alternatives |

---

## 7. Deployment Architecture Decision

Target stack: **Google Cloud Run containers + Firebase Hosting** for PWA.  
Why not Cloud Functions?  
* Cold-start-sensitive OCR workloads need always-on min instances.  
* Container images with TensorFlow addons exceed Cloud Functions limits.  
* Existing Terraform modules (`google_cloud_run_v2_service`) already parameterised for GPU workloads.

---

## 8. Acceptance Criteria (P1-05)

1. `docs/DUAL_BACKEND_MIGRATION.md` merged to `main`.
2. Architecture diagram referenced & stored at `docs/img/current-architecture.svg`.
3. Roadmap flags **P1-05**, **CE-01**, **CE-02** switched from ⚠ to ✅.
4. `apps/api` handles _all_ OCR paths; `backend/` no longer deployed.
5. CI passes without PyTest; Terraform plan contains **zero** Python resources.
6. k6 contract suite & golden dataset show no regression (> 99 % parity).

---

### Next Action

Open pull request titled **`docs: dual-backend migration plan (P1-05)`** and assign reviewers:

* @ts-lead for orchestration port
* @devops for Terraform updates
* @qa-lead for stabilization window

_This document is authoritative; any future architecture deviations require an ADR._
