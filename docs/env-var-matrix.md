# Environment Variable Matrix

A single, authoritative reference for **all** `AiClearBill` configuration knobs detected across the monorepo.  
If you introduce a new variable, update this matrix **and** `.env.sample`.

Legend | Symbol
-------|-------
**✔**  | Required in production (no sane default)
✱      | Optional – a sensible default exists or the feature is optional

| Service / Scope | Variable | Description | R/O |
|-----------------|----------|-------------|-----|
| **Core API (TypeScript – `apps/api`)** |
|  | HOST | Host interface to bind Fastify | ✱ |
|  | LOG_LEVEL | Pino log level | ✱ |
|  | NODE_ENV | Runtime environment flag | ✱ |
|  | PORT | API listen port | ✱ |
|  | SERVICE_VERSION | Version string stamped in logs | ✱ |
|  | AI_TIER_1_ENDPOINT | Tier 1 AI gateway (GPU) | ✱ |
|  | AI_TIER_2_ENDPOINT | Tier 2 AI gateway | ✱ |
|  | FIREBASE_SERVICE_ACCOUNT_PATH | JSON key file for Firebase Admin | ✔ |
|  | FIREBASE_STORAGE_BUCKET | Firebase Storage bucket name | ✔ |
|  | GOOGLE_APPLICATION_CREDENTIALS | GCP SA JSON for Cloud SDKs | ✔ |
|  | GOOGLE_CLOUD_PROJECT | Google Cloud project id | ✔ |
|  | PUBSUB_INVOICE_EXTRACTION_COMPLETE_TOPIC | Pub/Sub topic – extraction done | ✱ |
|  | PUBSUB_INVOICE_PROCESS_TOPIC | Pub/Sub topic – enqueue invoice | ✔ |
|  | PUBSUB_INVOICE_PUSH_TOPIC | Pub/Sub topic – push to accounting | ✱ |
|  | QBO_CLIENT_ID | QuickBooks Online OAuth client id | ✔ |
|  | QBO_CLIENT_SECRET | QuickBooks Online OAuth secret | ✔ |
|  | XERO_CLIENT_ID | Xero OAuth client id | ✔ |
|  | XERO_CLIENT_SECRET | Xero OAuth secret | ✔ |
| **Python Backend (legacy OCR – `backend`)** |
|  | CACHE_TTL_SECONDS | Response-cache TTL | ✱ |
|  | RATE_LIMIT_PER_MINUTE | API rate limit | ✱ |
|  | MONGO_URI | Mongo connection string | ✱ |
|  | MONGO_DB_NAME | Database name | ✱ |
|  | GEMINI_API_KEY | Google Gemini API key | ✔ |
|  | GEMINI_MODEL | Gemini model identifier | ✱ |
| **Web Front-end (Vite React – `apps/web` / `frontend`)** |
|  | VITE_API_URL | URL to tRPC gateway | ✱ |
|  | VITE_FIREBASE_API_KEY | Firebase JS-SDK key | ✔ |
|  | VITE_FIREBASE_AUTH_DOMAIN | Firebase auth domain | ✔ |
|  | VITE_FIREBASE_PROJECT_ID | Firebase project id | ✔ |
|  | VITE_FIREBASE_STORAGE_BUCKET | Firebase storage bucket | ✔ |
|  | VITE_FIREBASE_MESSAGING_SENDER_ID | FCM sender id | ✔ |
|  | VITE_FIREBASE_APP_ID | Firebase app id | ✔ |
|  | VITE_FIREBASE_MEASUREMENT_ID | Firebase analytics id | ✱ |
| **CI / GitHub Actions (`.github/workflows`)** |
|  | FIREBASE_PROJECT_ID | Emulator project id for tests | ✔ |
|  | DEPLOY_TOKEN | Token used in prod deploy step | ✔ |
| **Terraform / Infra (`terraform`)** |
|  | TF_VAR_project_id | GCP project id | ✔ |
|  | TF_VAR_region | GCP region for resources | ✱ |
|  | TF_VAR_artifact_registry_location | Artifact Registry location | ✱ |
|  | TF_VAR_artifact_registry_repository | Registry repository name | ✱ |
|  | TF_VAR_invoices_storage_bucket | GCS bucket for raw invoices | ✱ |
|  | TF_VAR_environment | Deployment environment tag | ✱ |
|  | TF_VAR_service_account_email | Cloud Run SA email | ✱ |
|  | TF_VAR_enable_gpu | Flag to enable GPU workloads | ✱ |
| **Terraform – Cloud Run env vars (rendered in `ocr-processor.tf`)** |
|  | NODE_ENV | Fixed to `production` in container | ✱ |
|  | PROJECT_ID | Injected from var.project_id | ✔ |
|  | PUBSUB_INVOICE_PROCESSING_SUBSCRIPTION | Subscription name bound to container | ✔ |
|  | USE_GPU | Flag for GPU service variant | ✱ |
| **Shared / Misc** |
|  | SENTRY_DSN | Error-tracking DSN | ✱ |
|  | STRIPE_SECRET_KEY | Stripe secret key | ✱ |

---

## Updating the Matrix

`pnpm lint:env` (CI job `check-env-vars`) ensures every `process.env.*`, `os.getenv()`, `import.meta.env.*`, `var.*` reference has a corresponding entry **and** placeholder in `.env.sample`.  
Fail-fast prevents “_Missing API_KEY_” surprises in local dev or production.
