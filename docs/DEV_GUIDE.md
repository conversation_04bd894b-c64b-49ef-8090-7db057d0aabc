<facThinking>
Need to create new file docs/DEV_GUIDE.md containing:
- Quick-start (Prereqs, clone, pnpm install, cp .env.sample .env, turbo run dev). ≤10min
- Dev URLs list.
- One-liner commands list.
- Troubleshooting matrix (≥8 issues; reuse some from TROUBLESHOOTING.md condensed).
- Hot-reload & debug incl VS Code launch snippets for web, api, backend.
- Contributing workflow (branch naming feat/<scope>, commit lint, CI expectations).
- References: link to docs/TROUBLESHOOTING.md, ENVIRONMENT_VARIABLES.md, API_REFERENCE.md, ROOT README dev bullet.

Include actionable commands.

Let's craft markdown.
