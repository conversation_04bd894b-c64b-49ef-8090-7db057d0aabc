# Troubleshooting Guide

Practical fixes for the most common problems you may hit while developing or running **AiClearBill (Billsnapp)**.  
If something is not covered here, search the issue tracker or open a new ticket.

---

## 1 · Setup & Environment

| Symptom | Likely Cause | Fix |
| ------- | ------------ | --- |
| `pnpm: command not found` | pnpm not installed | `npm i -g pnpm@8` |
| `error: Your Node version is X.Y.Z` | Node < 18 | `nvm install 18 && nvm use 18` |
| Firebase emulators won’t start (`EADDRINUSE 8080`) | Port already taken | Edit `firebase.json` → change port or stop conflicting service `lsof -i:8080` |
| `setup.sh` exits with “pnpm is not installed” | prerequisites missing | Install pnpm, re-run `./setup.sh` (idempotent) |

---

## 2 · Build & Compilation

### 2.1 TypeScript errors

```
Cannot find module '@billsnapp/shared-types'
```

*Cause:* workspace link missing.  
*Fix:* `pnpm install --filter @billsnapp/shared-types && pnpm --filter @billsnapp/shared-types build`.

### 2.2 Turbo cache confusion

```
ERR  TypeScript project references not found
```

*Fix sequence*

```bash
pnpm clean        # turbo clean targets
pnpm dev          # rebuild everything
```

If cache corruption persists: `rm -rf .turbo && pnpm dev`.

---

## 3 · Runtime (API / Auth / DB)

| Error message | Resolution |
| ------------- | ---------- |
| `401 UNAUTHORIZED` calling `/trpc/*` | Frontend token missing/expired → re-login or add `Authorization: Bearer <idToken>` header. |
| `MongoNetworkError: failed to connect to server` | Ensure Mongo is running (`docker ps` shows *mongo*). Otherwise change `MONGO_URI` in `.env`. |
| `permission-denied at /tenants/…` from Firestore | Custom claims not set → run `generate_test_token.py` or ensure Firebase Auth emulator is on. |

Enable verbose logs: `export LOG_LEVEL=debug` before running `pnpm dev`.

---

## 4 · OCR Pipeline

| Symptom | Cause | Fix |
| ------- | ----- | --- |
| `Error: GOOGLE_APPLICATION_CREDENTIALS not set` | Missing GCP key | Place service-account JSON in repo root and set path in `apps/api/.env`. |
| `403 PERMISSION_DENIED: Document AI processor not found` | Wrong `DOCUMENT_AI_PROCESSOR_ID` or region | Double-check ID & `VERTEX_AI_LOCATION`. |
| Uploaded image stuck in “processing” | Pub/Sub emulator not running OR topic mis-named | Verify `gcloud pubsub topics list` or local emulator logs. |

---

## 5 · Accounting Integration (QuickBooks)

### 5.1 OAuth

```
AuthenticationException: invalid_client
```

*Checklist*

1. Intuit dashboard → App → **Keys & OAuth**: redirect URI **exactly** matches `QBO_REDIRECT_URI`.
2. `QBO_CLIENT_ID / SECRET` set in `apps/api/.env`.
3. Sandbox vs production mismatch → update `QBO_ENVIRONMENT`.

### 5.2 Webhooks not firing

*Troubleshoot*

1. Confirm webhook URL in Intuit dev portal points to `/api/accounting/:tenantId/webhook`.
2. Inspect API logs (`apps/api`) for signature failures.  
   *Enable*: `LOG_LEVEL=debug`.
3. Ensure HTTPS publicly reachable endpoint when testing live. For local dev use `ngrok http 3001`.

---

## 6 · Frontend (React / Vite / PWA)

| Problem | Fix |
| ------- | --- |
| White screen, console shows `webSocket connection failed` | Vite HMR port blocked → open 5173 or set `--port` flag. |
| `workbox` 404 on refresh | Missing `service-worker` manifest after build → run `pnpm --filter @billsnapp/web build` and deploy `frontend/dist`. |
| Images not loading offline | Verify IndexedDB populated (`Application → IndexedDB → invoice_files`). If empty, service-worker registration failed. Clear site data and reload. |

---

## 7 · Docker & Compose

| Issue | Solution |
| ----- | -------- |
| `manifest for node:18-alpine not found` | `docker pull node:18-alpine` before compose OR update `docker-compose.yml` to latest tag. |
| Containers restart in loop | Check `.env` values injected via `env_file:`—empty required secrets cause crash → set them or remove `restart: always` during dev. |
| Cannot bind to port 3001 | Another host process running (`lsof -i:3001`) → stop or change `PORT` env. |

---

## 8 · Common Errors Quick-Table

Message | Quick Fix
------- | ---------
`TypeError: superjson.parse is not a function` | Version mismatch – `pnpm up superjson && pnpm dev`
`EACCES: permission denied, mkdir '/app/node_modules'` in Docker | Remove shared volume or add `node_modules` to `.dockerignore`
`auth/invalid-api-key` in frontend | Wrong `VITE_FIREBASE_API_KEY` or using production key with emulator – generate Web API key in Firebase console
`CELERY BROKER ERROR Connection refused` | Redis not running – `docker-compose up redis` or point to correct `REDIS_HOST`

---

## 9 · Enable Debug Logging

| Service | How |
| ------- | --- |
| **tRPC API** | `LOG_LEVEL=debug pnpm --filter @billsnapp/api dev` |
| **Python FastAPI** | `DEBUG=true` in `backend/.env` |
| **Frontend** | `localStorage.debug="billsnapp:*"` then refresh (uses `pino-browser`) |

---

## 10 · Where are the Logs?

| Component | Location | Notes |
| --------- | -------- | ----- |
| Node API | stdout / `apps/api/src/config/logger.ts` (pino) | Pretty mode in dev, JSON in prod |
| Python backend | stdout (`uvicorn`) | Add `--log-level debug` |
| Firebase emulators | `~/.cache/firebase/emulators` | Each emulator logs separately |
| Docker containers | `docker logs <container>` | Combine with `--follow` for live tail |
| Cloud Run / GCP | Cloud Logging Explorer | Filter by `resource.labels.service_name="api"` |

---

### Interpreting OCR Logs

Look for:

```
tier=1 confidence=0.91
tier=1 fail missing_field=invoiceNumber
tier escalate_to=2
```

Escalation path shows why an image moved to Gemini / Document AI.

---

## Still Stuck?

* Run `pnpm doctor` to diagnose workspace health.  
* Delete `node_modules`, `.turbo`, `pnpm store prune`, then `pnpm install`.  
* Ask in the project Slack or create an issue with **exact** error output & steps to reproduce.

Happy debugging 🛠️
