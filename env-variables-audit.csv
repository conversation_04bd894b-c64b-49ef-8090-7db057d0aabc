service,file,VAR_NAME,line,default,notes
api,apps/api/src/config/env.ts,PORT,10,y,Default: 4000
api,apps/api/src/config/env.ts,HOST,11,y,Default: 0.0.0.0
api,apps/api/src/config/env.ts,NODE_ENV,12,y,Default: development
api,apps/api/src/config/env.ts,FIREBASE_SERVICE_ACCOUNT_PATH,15,n,Path to Firebase service account JSON
api,apps/api/src/config/env.ts,FIREBASE_STORAGE_BUCKET,16,n,Firebase Storage bucket name
api,apps/api/src/config/env.ts,GOOGLE_APPLICATION_CREDENTIALS,19,n,Path to Google Cloud service account JSON
api,apps/api/src/config/env.ts,GOOGLE_CLOUD_PROJECT,20,n,Google Cloud project ID
api,apps/api/src/config/env.ts,AI_TIER_1_ENDPOINT,23,n,Endpoint for Tier 1 AI processing
api,apps/api/src/config/env.ts,AI_TIER_2_ENDPOINT,24,n,Endpoint for Tier 2 AI processing
api,apps/api/src/config/env.ts,PUBSUB_INVOICE_PROCESS_TOPIC,27,n,Pub/Sub topic for invoice processing
api,apps/api/src/config/env.ts,PUBSUB_INVOICE_PUSH_TOPIC,28,n,Pub/Sub topic for invoice pushing
api,apps/api/src/config/env.ts,PUBSUB_INVOICE_EXTRACTION_COMPLETE_TOPIC,29,n,Pub/Sub topic for extraction completion
api,apps/api/src/config/env.ts,QBO_CLIENT_ID,32,n,QuickBooks Online OAuth client ID
api,apps/api/src/config/env.ts,QBO_CLIENT_SECRET,33,n,QuickBooks Online OAuth client secret
api,apps/api/src/config/env.ts,XERO_CLIENT_ID,34,n,Xero OAuth client ID
api,apps/api/src/config/env.ts,XERO_CLIENT_SECRET,35,n,Xero OAuth client secret
api,apps/api/src/config/logger.ts,LOG_LEVEL,7,y,Default: debug in dev, info in prod
api,apps/api/src/config/logger.ts,SERVICE_VERSION,25,y,Default: 1.0.0
api,apps/api/src/config/firebase.ts,NODE_ENV,12,n,Used to determine Firebase initialization method
api,apps/api/src/config/firebase.ts,GOOGLE_APPLICATION_CREDENTIALS,18,n,Used in production environment
api,apps/api/src/config/firebase.ts,FIREBASE_SERVICE_ACCOUNT_PATH,23,y,Default: firebase-credentials.json in cwd
api,apps/api/src/config/firebase.ts,FIREBASE_STORAGE_BUCKET,33,n,Used for Firebase Storage initialization
python,backend/main.py,RATE_LIMIT_PER_MINUTE,51,y,Default: 60
python,backend/main.py,CACHE_TTL_SECONDS,55,y,Default: 60
python,backend/models/db.py,MONGO_URI,19,y,Default: mongodb://localhost:27017
python,backend/models/db.py,MONGO_DB_NAME,20,y,Default: aiclearbill
python,backend/services/ocr.py,GEMINI_API_KEY,14,y,Default: empty string
web,apps/web/src/lib/trpc.tsx,VITE_API_URL,16,y,Default: http://localhost:4000
web,apps/web/src/lib/trpc.tsx,VITE_API_URL,43,y,Default: http://localhost:4000
frontend,frontend/src/lib/firebase.ts,VITE_FIREBASE_API_KEY,7,n,Firebase JS SDK API key
frontend,frontend/src/lib/firebase.ts,VITE_FIREBASE_AUTH_DOMAIN,8,n,Firebase auth domain
frontend,frontend/src/lib/firebase.ts,VITE_FIREBASE_PROJECT_ID,9,n,Firebase project ID
frontend,frontend/src/lib/firebase.ts,VITE_FIREBASE_STORAGE_BUCKET,10,n,Firebase storage bucket
frontend,frontend/src/lib/firebase.ts,VITE_FIREBASE_MESSAGING_SENDER_ID,11,n,Firebase messaging sender ID
frontend,frontend/src/lib/firebase.ts,VITE_FIREBASE_APP_ID,12,n,Firebase app ID
frontend,frontend/src/lib/firebase.ts,VITE_FIREBASE_MEASUREMENT_ID,13,n,Firebase measurement ID
frontend,frontend/src/api/client.ts,VITE_API_BASE_URL,5,n,Commented out but referenced
ci,.github/workflows/ci.yml,FIREBASE_PROJECT_ID,40,n,Used for testing in CI
ci,.github/workflows/ci.yml,DEPLOY_TOKEN,128,n,Used for production deployment
terraform,terraform/variables.tf,TF_VAR_project_id,6,n,Google Cloud project ID
terraform,terraform/variables.tf,TF_VAR_region,11,y,Default: us-west1
terraform,terraform/variables.tf,TF_VAR_artifact_registry_location,17,y,Default: us
terraform,terraform/variables.tf,TF_VAR_artifact_registry_repository,23,y,Default: billsnapp
terraform,terraform/variables.tf,TF_VAR_invoices_storage_bucket,29,y,Default: billsnapp-invoices
terraform,terraform/variables.tf,TF_VAR_environment,35,y,Default: dev
terraform,terraform/variables.tf,TF_VAR_service_account_email,41,y,Default: empty string
terraform,terraform/variables.tf,TF_VAR_enable_gpu,47,y,Default: true
terraform,terraform/ocr-processor.tf,NODE_ENV,82,n,Set to production in Cloud Run service
terraform,terraform/ocr-processor.tf,PROJECT_ID,87,n,Set from var.project_id
terraform,terraform/ocr-processor.tf,PUBSUB_INVOICE_PROCESSING_SUBSCRIPTION,92,n,Set from subscription name
terraform,terraform/ocr-processor.tf,NODE_ENV,126,n,Set to production in GPU service
terraform,terraform/ocr-processor.tf,PROJECT_ID,131,n,Set from var.project_id
terraform,terraform/ocr-processor.tf,USE_GPU,136,n,Set to true for GPU service
terraform,terraform/ocr-processor.tf,PUBSUB_INVOICE_PROCESSING_SUBSCRIPTION,141,n,Set to invoice-processing-gpu-subscription
