import os
from datetime import timedelta


class Config:
    """Base configuration class"""

    # Security
    SECRET_KEY = os.environ.get("SESSION_SECRET", os.urandom(24))
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = "Lax"
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)

    # File uploads
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max upload size
    UPLOAD_FOLDER = os.path.join(os.getcwd(), "uploads")
    ALLOWED_EXTENSIONS = {"pdf", "png", "jpg", "jpeg", "svg"}

    # Database
    SQLALCHEMY_DATABASE_URI = os.environ.get("DATABASE_URL")
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        "pool_recycle": 300,
        "pool_pre_ping": True,
    }

    # Firebase
    FIREBASE_PROJECT_ID = os.environ.get("FIREBASE_PROJECT_ID")
    FIREBASE_API_KEY = os.environ.get("FIREBASE_API_KEY")
    FIREBASE_APP_ID = os.environ.get("FIREBASE_APP_ID")

    # OCR
    GEMINI_API_KEY = os.environ.get("GEMINI_API_KEY")


class DevelopmentConfig(Config):
    """Development configuration"""

    DEBUG = True
    SESSION_COOKIE_SECURE = False


class TestingConfig(Config):
    """Testing configuration"""

    TESTING = True
    SESSION_COOKIE_SECURE = False
    SQLALCHEMY_DATABASE_URI = "sqlite:///:memory:"


class ProductionConfig(Config):
    """Production configuration"""

    SESSION_COOKIE_SECURE = True

    # In production, add a stronger CSP (Content Security Policy)
    # and other security headers


# Configuration dictionary
config = {
    "development": DevelopmentConfig,
    "testing": TestingConfig,
    "production": ProductionConfig,
    "default": DevelopmentConfig,
}


def get_config():
    """Get the configuration based on the environment"""
    env = os.environ.get("FLASK_ENV", "default")
    return config.get(env, config["default"])
