#!/usr/bin/env python3
import os
import re

def update_imports(file_path):
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Update imports from api.v1 to backend.api.v1
    content = re.sub(r'from api\.v1', r'from backend.api.v1', content)
    
    # Update imports from utils to backend.utils
    content = re.sub(r'from utils', r'from backend.utils', content)
    
    # Update imports from workers to backend.workers
    content = re.sub(r'from workers', r'from backend.workers', content)
    
    # Update imports from models to backend.models
    content = re.sub(r'from models', r'from backend.models', content)
    
    # Update imports from dal to backend.dal
    content = re.sub(r'from dal', r'from backend.dal', content)
    
    # Update imports from middleware to backend.middleware
    content = re.sub(r'from middleware', r'from backend.middleware', content)
    
    # Update imports from pydantic_models to backend.pydantic_models
    content = re.sub(r'from pydantic_models', r'from backend.pydantic_models', content)
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"Updated imports in {file_path}")

def process_directory(directory):
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                update_imports(file_path)

if __name__ == "__main__":
    process_directory('backend')
