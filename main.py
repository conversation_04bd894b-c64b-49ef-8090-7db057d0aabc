import logging
import os

from flask import (
    abort,
    flash,
    jsonify,
    redirect,
    render_template,
    request,
    session,
    url_for,
)
from flask_login import current_user, login_required, login_user, logout_user
from werkzeug.security import generate_password_hash

from api.models.invoice import Invoice
from api.models.system_metrics import SuperAdminUser, UserActivity
from api.models.tenant import Tenant
from api.models.user import User
from api.models.vendor import Vendor
from app import app, db
from services.auth import initialize_firebase

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Initialize Firebase on startup
initialize_firebase()

# Register blueprints
if not any(bp.name == "superadmin" for bp in app.blueprints.values()):
    from api.routes.superadmin_routes import superadmin_api_bp, superadmin_bp

    app.register_blueprint(superadmin_bp)
    app.register_blueprint(superadmin_api_bp)


@app.before_request
def before_request():
    """Run before each request to set up context"""
    # Set up user context if logged in via session
    if "user_id" in session and not current_user.is_authenticated:
        user = User.query.get(session["user_id"])
        if user:
            login_user(user)


@app.route("/health")
def health_check():
    """Health check endpoint for monitoring"""
    return jsonify({"status": "healthy"})


@app.route("/")
def home():
    """Landing page"""
    if current_user.is_authenticated:
        return redirect(url_for("dashboard"))

    return render_template(
        "index.html",
        firebase_api_key=os.environ.get("FIREBASE_API_KEY"),
        firebase_project_id=os.environ.get("FIREBASE_PROJECT_ID"),
        firebase_app_id=os.environ.get("FIREBASE_APP_ID"),
    )


@app.route("/login", methods=["GET", "POST"])
def login():
    """Login page"""
    if current_user.is_authenticated:
        return redirect(url_for("dashboard"))

    return render_template(
        "auth/login.html",
        firebase_api_key=os.environ.get("FIREBASE_API_KEY"),
        firebase_project_id=os.environ.get("FIREBASE_PROJECT_ID"),
        firebase_app_id=os.environ.get("FIREBASE_APP_ID"),
    )


@app.route("/logout", methods=["GET", "POST"])
@login_required
def logout():
    """Logout user"""
    logout_user()
    session.clear()
    flash("You have been logged out successfully.", "success")
    return redirect(url_for("home"))


@app.route("/select-tenant")
@login_required
def select_tenant():
    """Select tenant page"""
    tenants = Tenant.query.all()
    return render_template("auth/select_tenant.html", tenants=tenants)


@app.route("/dashboard")
@login_required
def dashboard():
    """User dashboard"""
    # Get recent invoices for dashboard
    tenant_id = session.get("current_tenant_id")
    recent_invoices = []
    invoice_stats = {"pending": 0, "processed": 0, "exported": 0}
    vendor_count = 0

    if tenant_id:
        # Get recent invoices
        recent_invoices = (
            Invoice.query.filter_by(tenant_id=tenant_id)
            .order_by(Invoice.created_at.desc())
            .limit(5)
            .all()
        )

        # Count invoices by status
        invoice_stats["pending"] = Invoice.query.filter_by(
            tenant_id=tenant_id, status="PENDING"
        ).count()
        invoice_stats["processed"] = Invoice.query.filter_by(
            tenant_id=tenant_id, status="READY"
        ).count()
        invoice_stats["exported"] = Invoice.query.filter_by(
            tenant_id=tenant_id, status="PUSHED"
        ).count()

        # Count vendors
        vendor_count = Vendor.query.filter_by(tenant_id=tenant_id).count()

    return render_template(
        "dashboard/index.html",
        recent_invoices=recent_invoices,
        invoice_stats=invoice_stats,
        vendor_count=vendor_count,
    )


@app.route("/invoices")
@login_required
def invoices():
    """Invoices page"""
    tenant_id = session.get("current_tenant_id")
    vendors = []

    if tenant_id:
        vendors = Vendor.query.filter_by(tenant_id=tenant_id).all()

    return render_template("invoices/index.html", vendors=vendors)


@app.route("/invoices/<int:invoice_id>")
@login_required
def invoice_detail(invoice_id):
    """Invoice detail page"""
    tenant_id = session.get("current_tenant_id")

    if not tenant_id:
        flash("Please select a tenant first", "warning")
        return redirect(url_for("select_tenant"))

    invoice = Invoice.query.filter_by(id=invoice_id, tenant_id=tenant_id).first()

    if not invoice:
        abort(404)

    vendors = Vendor.query.filter_by(tenant_id=tenant_id).all()

    return render_template("invoices/detail.html", invoice=invoice, vendors=vendors)


@app.route("/vendors")
@login_required
def vendors():
    """Vendors page"""
    tenant_id = session.get("current_tenant_id")
    vendor_list = []

    if tenant_id:
        vendor_list = Vendor.query.filter_by(tenant_id=tenant_id).all()

    return render_template("vendors/index.html", vendors=vendor_list)


@app.route("/vendors/<int:vendor_id>")
@login_required
def vendor_detail(vendor_id):
    """Vendor detail page"""
    tenant_id = session.get("current_tenant_id")

    if not tenant_id:
        flash("Please select a tenant first", "warning")
        return redirect(url_for("select_tenant"))

    vendor = Vendor.query.filter_by(id=vendor_id, tenant_id=tenant_id).first()

    if not vendor:
        abort(404)

    # Get vendor's invoices
    invoices = Invoice.query.filter_by(vendor_id=vendor_id, tenant_id=tenant_id).all()

    return render_template("vendors/detail.html", vendor=vendor, invoices=invoices)


@app.errorhandler(404)
def page_not_found(e):
    """Handle 404 errors"""
    return render_template("404.html"), 404


@app.errorhandler(500)
def internal_server_error(e):
    """Handle 500 errors"""
    logger.error(f"Server error: {str(e)}")
    return render_template("500.html"), 500


# SuperAdmin Routes
@app.route("/loginadmin/setup", methods=["GET", "POST"])
def superadmin_setup():
    """Initial SuperAdmin setup - only available when no superadmin exists"""
    # Check if any superadmin exists
    if SuperAdminUser.query.count() > 0:
        return redirect(url_for("superadmin.superadmin_login"))

    if request.method == "POST":
        # Create first superadmin account
        admin = SuperAdminUser(
            username=request.form.get("username"),
            email=request.form.get("email"),
            password_hash=generate_password_hash(request.form.get("password")),
            first_name=request.form.get("first_name"),
            last_name=request.form.get("last_name"),
        )
        db.session.add(admin)
        db.session.commit()

        flash("SuperAdmin account created successfully. You can now log in.", "success")
        return redirect(url_for("superadmin.superadmin_login"))

    return render_template("superadmin/setup.html")


@app.route("/loginadmin")
def superadmin_redirect():
    """Redirect to superadmin login"""
    return redirect(url_for("superadmin.superadmin_login"))


# Record user activity middleware
@app.after_request
def record_user_activity(response):
    """Record user activity for analytics"""
    if hasattr(request, "user_id") and request.endpoint and request.method == "POST":
        # Skip certain endpoints
        skip_endpoints = ["static", "health_check"]
        if not any(endpoint in request.endpoint for endpoint in skip_endpoints):
            try:
                activity_type = request.endpoint
                tenant_id = session.get("current_tenant_id")
                user_id = session.get("user_id")

                if user_id and tenant_id:
                    # Create activity record
                    activity = UserActivity(
                        user_id=user_id,
                        tenant_id=tenant_id,
                        activity_type=activity_type,
                        ip_address=request.remote_addr,
                        user_agent=(
                            request.user_agent.string if request.user_agent else None
                        ),
                    )
                    db.session.add(activity)
                    db.session.commit()
            except Exception as e:
                # Log but don't break the request
                logger.error(f"Error recording user activity: {str(e)}")

    return response


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5000, debug=True)
