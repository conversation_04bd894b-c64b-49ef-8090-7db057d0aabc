name: Billsnapp CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

env:
  REGISTRY: gcr.io
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  NODE_VERSION: '18'

jobs:
  lint:
    name: Lint
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Install dotenv
        run: npm install dotenv
      
      - name: Check environment variables
        run: node scripts/check-env-vars.js
      
      - name: Run eslint
        run: pnpm lint
      
      - name: Run type checking
        run: pnpm type-check

  test:
    name: Test
    runs-on: ubuntu-latest
    needs: lint
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build shared packages
        run: pnpm --filter "@billsnapp/shared-types" build
      
      - name: Run tests
        run: pnpm test
      
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}

  infrastructure:
    name: Deploy Infrastructure
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.5.7
      
      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
      
      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
      
      - name: Initialize Terraform
        run: terraform init
        working-directory: ./terraform
      
      - name: Terraform Validate
        run: terraform validate
        working-directory: ./terraform
      
      - name: Terraform Plan
        run: terraform plan -out=tfplan
        working-directory: ./terraform
        env:
          TF_VAR_project_id: ${{ env.PROJECT_ID }}
          TF_VAR_region: us-west1
          TF_VAR_environment: ${{ github.ref == 'refs/heads/main' && 'prod' || 'dev' }}
      
      - name: Terraform Apply
        if: github.ref == 'refs/heads/main'
        run: terraform apply -auto-approve tfplan
        working-directory: ./terraform

  build-api:
    name: Build and Deploy API
    runs-on: ubuntu-latest
    needs: [test, infrastructure]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      
      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
      
      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
      
      - name: Build and push API image
        uses: google-github-actions/build-push-gcr@v1
        with:
          registry: ${{ env.REGISTRY }}
          project_id: ${{ env.PROJECT_ID }}
          image_name: billsnapp-api
          image_suffix: ${{ github.sha }}
          dockerfile: ./apps/api/Dockerfile
          context: .
          push: true
      
      - name: Deploy API to Cloud Run
        uses: google-github-actions/deploy-cloudrun@v1
        with:
          service: billsnapp-api
          image: ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/billsnapp-api:${{ github.sha }}
          region: us-west1
          env_vars: |
            NODE_ENV=production
            GOOGLE_PROJECT_ID=${{ env.PROJECT_ID }}

  build-ocr-processor:
    name: Build and Deploy OCR Processor
    runs-on: ubuntu-latest
    needs: [test, infrastructure]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      
      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
      
      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
      
      - name: Build and push OCR Processor image
        uses: google-github-actions/build-push-gcr@v1
        with:
          registry: ${{ env.REGISTRY }}
          project_id: ${{ env.PROJECT_ID }}
          image_name: billsnapp-ocr-processor
          image_suffix: ${{ github.sha }}
          dockerfile: ./apps/api/Dockerfile.ocr-processor
          context: .
          push: true
      
      - name: Deploy OCR Processor to Cloud Run
        uses: google-github-actions/deploy-cloudrun@v1
        with:
          service: billsnapp-ocr-processor
          image: ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/billsnapp-ocr-processor:${{ github.sha }}
          region: us-west1
          env_vars: |
            NODE_ENV=production
            GOOGLE_PROJECT_ID=${{ env.PROJECT_ID }}
            PUBSUB_INVOICE_PROCESSING_SUBSCRIPTION=invoice-processing-subscription

  build-ocr-processor-gpu:
    name: Build and Deploy OCR Processor (GPU)
    runs-on: ubuntu-latest
    needs: [test, infrastructure]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      
      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
      
      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
      
      - name: Modify Dockerfile for GPU support
        run: |
          cat > ./apps/api/Dockerfile.ocr-processor-gpu << EOL
          FROM nvidia/cuda:12.2.0-runtime-ubuntu22.04 as base

          # Install Node.js
          RUN apt-get update && apt-get install -y curl
          RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
          RUN apt-get install -y nodejs

          # Install dependencies for Canvas
          RUN apt-get update && apt-get install -y \
              build-essential \
              libcairo2-dev \
              libpango1.0-dev \
              libjpeg-dev \
              libgif-dev \
              librsvg2-dev \
              python3

          WORKDIR /app

          # Copy package files
          COPY package.json ./
          COPY pnpm-lock.yaml ./
          COPY turbo.json ./
          COPY tsconfig.json ./

          # Copy workspace packages
          COPY packages/ ./packages/

          # Copy the API application
          COPY apps/api/ ./apps/api/

          # Install dependencies
          RUN npm install -g pnpm
          RUN pnpm install --frozen-lockfile

          # Build the project
          RUN pnpm build

          # Set environment variables
          ENV NODE_ENV=production
          ENV PORT=8080
          ENV USE_GPU=true

          # Run the OCR processor service
          CMD ["node", "apps/api/dist/services/pubsub/server.js"]

          # Expose the port
          EXPOSE 8080
          EOL
      
      - name: Build and push GPU OCR Processor image
        uses: google-github-actions/build-push-gcr@v1
        with:
          registry: ${{ env.REGISTRY }}
          project_id: ${{ env.PROJECT_ID }}
          image_name: billsnapp-ocr-processor-gpu
          image_suffix: ${{ github.sha }}
          dockerfile: ./apps/api/Dockerfile.ocr-processor-gpu
          context: .
          push: true
      
      - name: Deploy GPU OCR Processor to Cloud Run
        uses: google-github-actions/deploy-cloudrun@v1
        with:
          service: billsnapp-ocr-processor-gpu
          image: ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/billsnapp-ocr-processor-gpu:${{ github.sha }}
          region: us-west1
          env_vars: |
            NODE_ENV=production
            GOOGLE_PROJECT_ID=${{ env.PROJECT_ID }}
            USE_GPU=true
            PUBSUB_INVOICE_PROCESSING_SUBSCRIPTION=invoice-processing-gpu-subscription
          gpu: A100

  build-web:
    name: Build and Deploy Web
    runs-on: ubuntu-latest
    needs: [test, build-api]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build web app
        run: pnpm --filter "@billsnapp/web" build
        env:
          VITE_API_URL: https://billsnapp-api-abcxyz.run.app
          VITE_FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}
          VITE_FIREBASE_AUTH_DOMAIN: ${{ secrets.FIREBASE_AUTH_DOMAIN }}
          VITE_FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
          VITE_FIREBASE_STORAGE_BUCKET: ${{ secrets.FIREBASE_STORAGE_BUCKET }}
          VITE_FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.FIREBASE_MESSAGING_SENDER_ID }}
          VITE_FIREBASE_APP_ID: ${{ secrets.FIREBASE_APP_ID }}
          VITE_ENABLE_TENSORFLOW: true
      
      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
      
      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
      
      - name: Upload web app to Firebase Hosting
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: ${{ secrets.GITHUB_TOKEN }}
          firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
          channelId: live
          projectId: ${{ secrets.FIREBASE_PROJECT_ID }}
          entryPoint: ./apps/web
