name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.12'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black
        if [ -f backend/requirements.txt ]; then
          pip install -r backend/requirements.txt
        fi

    - name: Set up Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'

    - name: Install frontend dependencies
      run: |
        cd frontend
        npm install

    - name: Lint Python code
      run: |
        flake8 backend --count --select=E9,F63,F7,F82 --show-source --statistics
        black --check backend

    - name: Lint and TypeScript check (Frontend)
      run: |
        cd frontend
        npm run lint
        npx tsc --noEmit

  test-backend:
    name: Test Backend
    runs-on: ubuntu-latest
    needs: lint

    steps:
    - uses: actions/checkout@v2

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v1

    - name: Build and run tests with Docker Compose
      run: |
        docker-compose -f docker-compose.yml -f docker-compose.test.yml up --build --exit-code-from backend-test
      env:
        FIREBASE_PROJECT_ID: test-project

    - name: Copy coverage report
      run: |
        docker cp $(docker ps -aqf "name=backend-test"):/app/coverage.xml ./backend/coverage.xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v1
      with:
        file: ./backend/coverage.xml
        flags: backend

  test-frontend:
    name: Test Frontend
    runs-on: ubuntu-latest
    needs: lint

    steps:
    - uses: actions/checkout@v2

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v1

    - name: Build and run tests with Docker Compose
      run: |
        docker-compose -f docker-compose.yml -f docker-compose.test.yml up --build --exit-code-from frontend-test

    - name: Copy coverage report
      run: |
        docker cp $(docker ps -aqf "name=frontend-test"):/app/coverage/coverage-final.json ./frontend/coverage-final.json

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v1
      with:
        file: ./frontend/coverage-final.json
        flags: frontend

  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend]

    steps:
    - uses: actions/checkout@v2

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v1

    - name: Build and run E2E tests with Docker Compose
      run: |
        docker-compose -f docker-compose.yml -f docker-compose.test.yml up --build --exit-code-from e2e-test
      env:
        FIREBASE_PROJECT_ID: test-project

  build:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend, e2e-tests]
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v2

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v1

    - name: Login to GitHub Container Registry
      uses: docker/login-action@v1
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Build and push backend image
      uses: docker/build-push-action@v2
      with:
        context: .
        file: ./Dockerfile
        push: true
        tags: ghcr.io/${{ github.repository }}/backend:${{ github.sha }},ghcr.io/${{ github.repository }}/backend:latest

    - name: Build and push frontend image
      uses: docker/build-push-action@v2
      with:
        context: ./frontend
        file: ./frontend/Dockerfile
        push: true
        tags: ghcr.io/${{ github.repository }}/frontend:${{ github.sha }},ghcr.io/${{ github.repository }}/frontend:latest

  deploy:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v2

    - name: Deploy to production
      run: |
        echo "Deploying to production..."
        # Add your deployment commands here
      env:
        DEPLOY_TOKEN: ${{ secrets.DEPLOY_TOKEN }}
