name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.12'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black
        if [ -f backend/requirements.txt ]; then
          pip install -r backend/requirements.txt
        fi
    
    - name: Set up Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        npm install
    
    - name: Lint Python code
      run: |
        flake8 backend --count --select=E9,F63,F7,F82 --show-source --statistics
        black --check backend

    - name: Lint and TypeScript check (Frontend)
      run: |
        cd frontend
        npm run lint
        npx tsc --noEmit

  test-backend:
    name: Test Backend
    runs-on: ubuntu-latest
    needs: lint
    
    services:
      mongodb:
        image: mongo:4.4
        ports:
          - 27017:27017
      
      redis:
        image: redis:6
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.12'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        if [ -f backend/requirements.txt ]; then
          pip install -r backend/requirements.txt
        fi
        pip install pytest pytest-asyncio pytest-cov
    
    - name: Run tests
      run: |
        cd backend
        pytest tests/ --cov=. --cov-report=xml
      env:
        MONGO_URI: mongodb://localhost:27017
        MONGO_DB_NAME: test_aiclearbill
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        FIREBASE_PROJECT_ID: test-project
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v1
      with:
        file: ./backend/coverage.xml
        flags: backend

  test-frontend:
    name: Test Frontend
    runs-on: ubuntu-latest
    needs: lint
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
    
    - name: Install dependencies
      run: |
        cd frontend
        npm install
    
    - name: Run tests
      run: |
        cd frontend
        npm test -- --coverage
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v1
      with:
        file: ./frontend/coverage/coverage-final.json
        flags: frontend

  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend]
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
    
    - name: Install Playwright
      run: |
        cd frontend
        npm install
        npx playwright install --with-deps
    
    - name: Build frontend
      run: |
        cd frontend
        npm run build
    
    - name: Start backend server
      run: |
        cd backend
        pip install -r requirements.txt
        python -m uvicorn main:app --host 0.0.0.0 --port 8000 &
        sleep 5  # Wait for server to start
      env:
        MONGO_URI: mongodb://localhost:27017
        MONGO_DB_NAME: test_aiclearbill
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        FIREBASE_PROJECT_ID: test-project
    
    - name: Run E2E tests
      run: |
        cd frontend
        npx playwright test

  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend]
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Build Docker image
      run: |
        docker build -t aiclearbill:${{ github.sha }} .
    
    - name: Test Docker image
      run: |
        docker run --rm aiclearbill:${{ github.sha }} python -c "print('Docker image works!')"

  deploy:
    name: Deploy to Replit
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Install Replit CLI
      run: |
        curl -fsSL https://replit-cli.com/install.sh | sh
    
    - name: Deploy to Replit
      run: |
        replit deploy --image aiclearbill:${{ github.sha }}
      env:
        REPLIT_TOKEN: ${{ secrets.REPLIT_TOKEN }}
