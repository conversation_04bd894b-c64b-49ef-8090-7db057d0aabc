name: API CI/CD Pipeline

on:
  push:
    branches: [main]
    paths:
      - 'apps/api/**'
      - 'packages/**'
      - '.github/workflows/api-ci.yml'
      - 'openapi.json'
  pull_request:
    branches: [main]
    paths:
      - 'apps/api/**'
      - 'packages/**'
      - '.github/workflows/api-ci.yml'
      - 'openapi.json'
  workflow_dispatch: # Allow manual triggering

jobs:
  setup:
    name: Setup & Dependencies
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build shared packages
        run: pnpm --filter "@billsnapp/shared-types" build

      - name: Cache node_modules
        uses: actions/cache@v4
        id: node-modules-cache
        with:
          path: |
            node_modules
            apps/api/node_modules
            packages/*/node_modules
          key: ${{ runner.os }}-modules-${{ matrix.node-version }}-${{ hashFiles('**/pnpm-lock.yaml') }}

  lint-and-typecheck:
    name: Lint & Type Check
    needs: setup
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: false

      - name: Restore node_modules
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            apps/api/node_modules
            packages/*/node_modules
          key: ${{ runner.os }}-modules-${{ matrix.node-version }}-${{ hashFiles('**/pnpm-lock.yaml') }}

      - name: Lint API code
        run: pnpm --filter "@billsnapp/api" lint

      - name: Type check API code
        run: pnpm --filter "@billsnapp/api" type-check

  openapi-generation:
    name: OpenAPI Documentation
    needs: setup
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [20] # Use Node.js 20 for OpenAPI generation
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: false

      - name: Restore node_modules
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            apps/api/node_modules
            packages/*/node_modules
          key: ${{ runner.os }}-modules-${{ matrix.node-version }}-${{ hashFiles('**/pnpm-lock.yaml') }}

      - name: Generate OpenAPI documentation
        run: pnpm --filter "@billsnapp/api" generate:openapi

      - name: Run OpenAPI validation tests
        run: pnpm --filter "@billsnapp/api" test:openapi

      - name: Check if OpenAPI spec changed
        run: |
          if git diff --exit-code openapi.json; then
            echo "✅ OpenAPI spec is up-to-date with the committed version"
          else
            echo "❌ ERROR: Generated OpenAPI spec differs from the committed version"
            echo "Please run 'pnpm --filter \"@billsnapp/api\" generate:openapi' and commit the changes"
            exit 1
          fi

      - name: Upload OpenAPI spec artifact
        uses: actions/upload-artifact@v4
        with:
          name: openapi-spec
          path: openapi.json
          if-no-files-found: error

  test:
    name: Run Tests
    needs: setup
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: false

      - name: Restore node_modules
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            apps/api/node_modules
            packages/*/node_modules
          key: ${{ runner.os }}-modules-${{ matrix.node-version }}-${{ hashFiles('**/pnpm-lock.yaml') }}

      - name: Run API tests
        run: pnpm --filter "@billsnapp/api" test

  build:
    name: Build API
    needs: [lint-and-typecheck, openapi-generation, test]
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [20] # Use Node.js 20 for production build
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: false

      - name: Restore node_modules
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            apps/api/node_modules
            packages/*/node_modules
          key: ${{ runner.os }}-modules-${{ matrix.node-version }}-${{ hashFiles('**/pnpm-lock.yaml') }}

      - name: Copy OpenAPI spec to public directory
        run: |
          mkdir -p apps/api/public
          cp openapi.json apps/api/public/

      - name: Build API for production
        run: pnpm --filter "@billsnapp/api" build

      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: api-build
          path: apps/api/dist
          if-no-files-found: error

  deploy:
    name: Deploy to Cloud Run
    needs: [build]
    if: github.ref == 'refs/heads/main' # Only deploy on push to main
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Download build artifact
        uses: actions/download-artifact@v4
        with:
          name: api-build
          path: apps/api/dist

      - name: Download OpenAPI spec
        uses: actions/download-artifact@v4
        with:
          name: openapi-spec
          path: ./

      - name: Copy OpenAPI spec to public directory
        run: |
          mkdir -p apps/api/public
          cp openapi.json apps/api/public/

      - name: Set up Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ secrets.GCP_PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          export_default_credentials: true

      - name: Authorize Docker push
        run: gcloud auth configure-docker

      - name: Build and push Docker image
        working-directory: apps/api
        run: |
          docker build -t gcr.io/${{ secrets.GCP_PROJECT_ID }}/api:${{ github.sha }} .
          docker push gcr.io/${{ secrets.GCP_PROJECT_ID }}/api:${{ github.sha }}

      - name: Deploy to Cloud Run
        id: deploy
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: api
          image: gcr.io/${{ secrets.GCP_PROJECT_ID }}/api:${{ github.sha }}
          region: ${{ secrets.GCP_REGION }}
          flags: --allow-unauthenticated

      - name: Show Output
        run: echo ${{ steps.deploy.outputs.url }}
