# Billsnapp DB Stack Overview

This document describes the data and authentication stack for the Billsnapp project. It covers authentication, real-time data, and main database usage, along with integration patterns and environment variable requirements.

---

## 1. 🔐 Firebase Auth
- **Purpose:** Handles all user authentication.
- **Usage:**
  - Provides UID (User ID) for access control and data partitioning.
  - Used for both Firestore Native and MongoDB authorization references.
- **Integration:**
  - Backend uses Firebase Admin SDK to verify tokens and extract UID/roles.
  - All API endpoints must validate Firebase tokens and extract UID.

---

## 2. 🧾 MongoDB Atlas (Primary Database)
- **Purpose:** Main operational database for complex, large, or structured data.
- **Data Examples:**
  - Invoice documents (OCR output)
  - Vendor matching rules
  - Categorization logic
  - LLM annotations, summaries
  - Multi-country handling logic
- **Integration:**
  - Use `motor` async driver in backend.
  - Connection details are provided in `.env` (`MONGO_URI`, `MONGO_DBNAME`).
  - Data access is always scoped by Firebase UID for security and multi-tenancy.

---

## 3. 📄 Firestore Native (Secondary Database)
- **Purpose:** Stores lightweight, real-time, and user-specific data.
- **Data Examples:**
  - `/users/{uid}/preferences`
  - FCM tokens for push notifications
  - Feature toggles, language, onboarding flags
- **Integration:**
  - Use Google Cloud Firestore SDK in backend/frontend for these features.
  - Data is always partitioned by Firebase UID.

---

## 4. 🔑 Environment Variables
All required secrets and connection strings are stored in `.env`. Key variables:
- `MONGO_URI`: MongoDB Atlas URI
- `MONGO_DBNAME`: Main database name
- Firebase Admin SDK variables: `FIREBASE_PROJECT_ID`, `FIREBASE_PRIVATE_KEY_ID`, `FIREBASE_PRIVATE_KEY`, `FIREBASE_CLIENT_EMAIL`, etc.

---

## 5. 🔒 Security & Best Practices
- All access is authenticated via Firebase Auth.
- Role-based access and tenant isolation enforced at API and DB layers.
- JWT claims are minimal and contain only necessary information (see JWT section).
- Never store sensitive data in JWT claims.
- Use HTTPS-only cookies for tokens on the client.
- All API endpoints must validate tokens and roles before accessing data.

---

## 6. 🔁 Data Partitioning & Access Patterns
- **MongoDB:** Partitioned by UID/tenant for all business-critical documents.
- **Firestore:** Partitioned by UID for real-time, user-specific data.
- **APIs:** Always require authentication and use UID from token as the primary filter.

---

## 7. 🛠️ Example Environment Variables
```env
MONGO_URI=mongodb+srv://<username>:<password>@<cluster-address>/<dbname>?retryWrites=true&w=majority
MONGO_DBNAME=billsnapp
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY=your-private-key
FIREBASE_CLIENT_EMAIL=your-client-email
FIREBASE_CLIENT_ID=your-client-id
# ...other Firebase variables
```

---

## 8. 📚 References
- [Google Firestore MongoDB Compatibility](https://cloud.google.com/firestore/docs/mongodb-compatibility)
- [Firebase Admin SDK Docs](https://firebase.google.com/docs/admin/setup)
- [MongoDB Python Driver (motor)](https://motor.readthedocs.io/en/stable/)

---

For questions or updates, edit this file or contact the project maintainers.
