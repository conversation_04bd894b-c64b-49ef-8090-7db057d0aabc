# Billsnapp Web (React + Vite PWA)

Frontend for **AiClearBill / Billsnapp** – an installable Progressive-Web-App built with **React 18, Vite, Tailwind CSS and tRPC**.  
Runs on desktop & mobile, works offline via service-worker caching and IndexedDB.

---

## 1 · Prerequisites

| Tool | Version | Notes |
|------|---------|-------|
| Node | ≥ 18.x LTS | Use [`nvm`](https://github.com/nvm-sh/nvm) or `fnm` to install |
| pnpm | ≥ 8 | Monorepo package manager |
| Firebase CLI | *(optional)* | For local emulator suite & hosting |
| Git | ≥ 2.30 | |

---

## 2 · Getting Started

```bash
# inside repo root (one-time)
pnpm install            # installs workspace deps

# dev server with hot-reload
pnpm --filter @billsnapp/web dev     # or cd apps/web && pnpm dev

# open the app
http://localhost:3000
```

> The web app uses **tRPC** to talk to the API (`apps/api`). Make sure the API is running (`pnpm --filter @billsnapp/api dev`) or point `VITE_API_URL` to a remote instance.

### Build for Production

```bash
pnpm --filter @billsnapp/web build   # output → apps/web/dist
```

Built artifacts can be served via any static host (Firebase Hosting, Cloud Run, Netlify, …).

### Execute Tests

```bash
pnpm --filter @billsnapp/web test    # Vitest + React Testing Library
```

---

## 3 · Vite Environment Variables 🚦

Vite only exposes variables that **start with `VITE_`** to your client-side bundle.  
Anything else (e.g. secrets) is stripped at build time.

| Variable | Required | Description |
|----------|----------|-------------|
| `VITE_API_URL` | ✱ | Base URL to the tRPC gateway (defaults to `http://localhost:4000`) |
| `VITE_FIREBASE_API_KEY` | ✔ | Firebase JS SDK key |
| `VITE_FIREBASE_AUTH_DOMAIN` | ✔ | Firebase auth domain |
| `VITE_FIREBASE_PROJECT_ID` | ✔ | Firebase project id |
| `VITE_FIREBASE_STORAGE_BUCKET` | ✔ | Firebase storage bucket |
| `VITE_FIREBASE_MESSAGING_SENDER_ID` | ✔ | Firebase messaging sender id |
| `VITE_FIREBASE_APP_ID` | ✔ | Firebase app id |
| `VITE_FIREBASE_MEASUREMENT_ID` | ✱ | Firebase analytics id (optional) |

**Rule of thumb**

```
# correct – will be injected during build
VITE_MY_PUBLIC_FLAG=true

# wrong – will be undefined in the browser
MY_SECRET_KEY=shh...
```

The full authoritative list (for **all** services) lives in [`../../.env.sample`](../../.env.sample) and is explained in [`docs/env-var-matrix.md`](../../docs/env-var-matrix.md).

---

## 4 · PWA Notes

* Service worker & manifest generated via `vite-plugin-pwa` (auto-update strategy).  
* Icons in `/public/icons/`; update manifest in `vite.config.ts` if you change them.  
* App can be **installed** on iOS/Android/desktop; works offline for cached routes & assets.

To test:

```bash
pnpm preview           # serves production build locally
```

Open Chrome DevTools → Application → Service Workers → check “Offline”, reload.

---

## 5 · Project Structure

```
apps/web/
├─ public/           # static assets (favicons, icons, robots.txt…)
├─ src/
│  ├─ components/    # shared UI components
│  ├─ contexts/      # React Context providers
│  ├─ hooks/         # reusable hooks
│  ├─ lib/           # tRPC client, Firebase init, utilities
│  ├─ pages/         # route-level components
│  └─ styles/        # Tailwind base & globals
├─ index.html        # Vite HTML entry
├─ vite.config.ts    # Vite + PWA plugin config
└─ tailwind.config.js
```

---

## 6 · Linting & Formatting

| Task | Command |
|------|---------|
| ESLint + Prettier | `pnpm lint` |
| Type check        | `pnpm type-check` |
| Format fix        | `pnpm format` |

Runs automatically on commit via Husky & `lint-staged`.

---

## 7 · Deployment

The CI pipeline builds the web app in `build-web` job and deploys to **Firebase Hosting** (see `.github/workflows/monorepo-ci.yml`).  
Set the Firebase SDK variables as **GitHub secrets** (`FIREBASE_API_KEY`, `FIREBASE_PROJECT_ID`, …).  
Change `VITE_API_URL` to point at your Cloud Run API URL via build-time environment variable override.

---

## 8 · Further Reading

* [Vite Documentation](https://vitejs.dev/guide/)  
* [React Query](https://tanstack.com/query/latest/docs/react/overview)  
* [tRPC](https://trpc.io/docs/react/v10)  
* [vite-plugin-pwa](https://vite-plugin-pwa.netlify.app/)  
* Root project README – [`../../README.md`](../../README.md)

Enjoy hacking on Billsnapp Web! 🎉
