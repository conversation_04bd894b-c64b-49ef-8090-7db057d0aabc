#!/usr/bin/env node
/**
 * @file generate-openapi-static.ts
 * @description Static OpenAPI 3.1 documentation generator for tRPC API
 * 
 * This script generates OpenAPI documentation without requiring runtime dependencies
 * like Firebase. It manually constructs the OpenAPI specification based on the
 * known API structure and writes it to both the repository root and public directory.
 * 
 * Usage: pnpm tsx scripts/generate-openapi-static.ts
 */

import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import OpenAPISchemaValidator from 'openapi-schema-validator';

// Path constants
const ROOT_DIR = path.resolve(__dirname, '../../../');
const PUBLIC_DIR = path.resolve(__dirname, '../public');
const ROOT_OUTPUT_PATH = path.join(ROOT_DIR, 'openapi.json');
const PUBLIC_OUTPUT_PATH = path.join(PUBLIC_DIR, 'openapi.json');

// API metadata constants
const API_TITLE = 'AiClearBill API';
const API_DESCRIPTION = 'API for AiClearBill invoice processing platform';
const API_VERSION = '1.0.0';
const API_BASE_URL = process.env.API_URL || 'http://localhost:4000';

/**
 * Generate the OpenAPI specification document
 * This manually constructs the spec based on our API structure
 */
function generateOpenApiDocument() {
  // Define the OpenAPI document structure
  const openApiDocument = {
    openapi: '3.1.0',
    info: {
      title: API_TITLE,
      description: API_DESCRIPTION,
      version: API_VERSION,
    },
    servers: [
      {
        url: API_BASE_URL,
        description: 'API server',
      },
    ],
    paths: {},
    components: {
      securitySchemes: {
        BearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT Bearer token authentication',
        },
      },
      schemas: {},
    },
    tags: [
      { name: 'Auth', description: 'Authentication operations' },
      { name: 'Users', description: 'User management operations' },
      { name: 'Tenants', description: 'Tenant management operations' },
      { name: 'Invoices', description: 'Invoice operations' },
      { name: 'Vendors', description: 'Vendor management operations' },
      { name: 'OCR', description: 'Document processing operations' },
    ],
  };

  // Add component schemas
  openApiDocument.components.schemas = {
    // Common schemas
    Error: {
      type: 'object',
      properties: {
        code: { type: 'string' },
        message: { type: 'string' },
        data: { type: 'object', additionalProperties: true },
      },
      required: ['code', 'message'],
    },
    
    // Auth schemas
    User: {
      type: 'object',
      properties: {
        uid: { type: 'string' },
        email: { type: 'string', format: 'email' },
        displayName: { type: 'string' },
        photoURL: { type: 'string', nullable: true },
        role: { 
          type: 'string', 
          enum: ['invoice_capturer', 'approver', 'accountant', 'tenant_admin', 'system_admin', 'snapper', 'checker'] 
        },
        tenantId: { type: 'string' },
        status: { type: 'string', enum: ['active', 'disabled', 'pending', 'invited'] },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
      },
      required: ['uid', 'email', 'role', 'tenantId'],
    },
    
    // Tenant schemas
    Tenant: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        displayName: { type: 'string' },
        industry: { type: 'string', nullable: true },
        country: { type: 'string', nullable: true },
        timezone: { type: 'string', nullable: true },
        status: { type: 'string', enum: ['active', 'disabled', 'pending', 'deleted'] },
        plan: { type: 'string' },
        settings: { type: 'object', additionalProperties: true },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
      },
      required: ['id', 'name', 'displayName', 'status'],
    },
    
    // Invoice schemas
    Invoice: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        tenantId: { type: 'string' },
        vendorId: { type: 'string' },
        invoiceNumber: { type: 'string' },
        amount: { type: 'number' },
        currency: { type: 'string' },
        invoiceDate: { type: 'string', format: 'date-time' },
        dueDate: { type: 'string', format: 'date-time', nullable: true },
        description: { type: 'string', nullable: true },
        status: { type: 'string', enum: ['draft', 'pending', 'approved', 'rejected', 'processing', 'paid'] },
        lineItems: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              description: { type: 'string' },
              quantity: { type: 'number' },
              unitPrice: { type: 'number' },
              amount: { type: 'number' },
              taxRate: { type: 'number', nullable: true },
              taxAmount: { type: 'number', nullable: true },
            },
            required: ['description', 'quantity', 'unitPrice', 'amount'],
          },
        },
        attachmentUrl: { type: 'string', nullable: true },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        createdBy: { type: 'string' },
      },
      required: ['id', 'tenantId', 'vendorId', 'invoiceNumber', 'amount', 'currency', 'invoiceDate', 'status'],
    },
    
    // Vendor schemas
    Vendor: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        tenantId: { type: 'string' },
        name: { type: 'string' },
        email: { type: 'string', format: 'email', nullable: true },
        phone: { type: 'string', nullable: true },
        address: {
          type: 'object',
          nullable: true,
          properties: {
            street: { type: 'string', nullable: true },
            city: { type: 'string', nullable: true },
            state: { type: 'string', nullable: true },
            postalCode: { type: 'string', nullable: true },
            country: { type: 'string', nullable: true },
          },
        },
        taxId: { type: 'string', nullable: true },
        active: { type: 'boolean' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
      },
      required: ['id', 'tenantId', 'name'],
    },
    
    // OCR schemas
    OcrResult: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        invoiceId: { type: 'string' },
        tier: { type: 'string', enum: ['tier1', 'tier2', 'tier3'] },
        status: { type: 'string', enum: ['pending', 'processing', 'completed', 'failed'] },
        fields: { type: 'object', additionalProperties: true },
        confidence: { type: 'number' },
        createdAt: { type: 'string', format: 'date-time' },
        completedAt: { type: 'string', format: 'date-time', nullable: true },
        processingTimeMs: { type: 'number', nullable: true },
        errorMessage: { type: 'string', nullable: true },
      },
      required: ['id', 'invoiceId', 'tier', 'status'],
    },
  };

  // Define all API paths
  const paths = {
    // Auth Router
    '/auth/user': {
      get: {
        summary: 'Get authenticated user profile',
        description: 'Retrieves the current authenticated user profile or a specific user if UID is provided and user has permission',
        tags: ['Auth'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'uid',
            in: 'query',
            description: 'Optional user ID to retrieve a specific user profile',
            required: false,
            schema: { type: 'string' },
          },
        ],
        responses: {
          '200': {
            description: 'User profile retrieved successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/User' },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'User not found' },
        },
      },
    },
    '/auth/verify': {
      post: {
        summary: 'Verify JWT token',
        description: 'Verifies the validity of a JWT token and returns user information',
        tags: ['Auth'],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  token: { type: 'string', description: 'JWT token to verify' },
                },
                required: ['token'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Token verified successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    uid: { type: 'string' },
                    email: { type: 'string', format: 'email' },
                    emailVerified: { type: 'boolean' },
                    displayName: { type: 'string' },
                    photoURL: { type: 'string', nullable: true },
                    role: { type: 'string' },
                    tenantId: { type: 'string' },
                    status: { type: 'string' },
                    tokenExpiration: { type: 'string', format: 'date-time' },
                  },
                },
              },
            },
          },
          '401': { description: 'Token verification failed' },
        },
      },
    },

    // Users Router
    '/users': {
      get: {
        summary: 'List users within a tenant',
        description: 'Retrieves a paginated list of users within the specified tenant. Tenant admins can see all users in their tenant, while regular users can only see their own profile.',
        tags: ['Users'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'tenantId',
            in: 'query',
            description: 'ID of the tenant to list users for',
            required: true,
            schema: { type: 'string' },
          },
          {
            name: 'limit',
            in: 'query',
            description: 'Maximum number of users to return',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 50 },
          },
          {
            name: 'cursor',
            in: 'query',
            description: 'Cursor for pagination',
            required: false,
            schema: { type: 'string' },
          },
          {
            name: 'status',
            in: 'query',
            description: 'Filter by user status',
            required: false,
            schema: { type: 'string', enum: ['active', 'disabled', 'pending', 'invited'] },
          },
          {
            name: 'role',
            in: 'query',
            description: 'Filter by user role',
            required: false,
            schema: { 
              type: 'string', 
              enum: ['invoice_capturer', 'approver', 'accountant', 'tenant_admin', 'system_admin', 'snapper', 'checker'] 
            },
          },
        ],
        responses: {
          '200': {
            description: 'Users retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    users: {
                      type: 'array',
                      items: { $ref: '#/components/schemas/User' },
                    },
                    nextCursor: { type: 'string', nullable: true },
                  },
                },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
        },
      },
    },
    '/users/invite': {
      post: {
        summary: 'Invite a new user',
        description: 'Creates a new user account and sends an invitation email. Only tenant admins and system admins can invite users.',
        tags: ['Users'],
        security: [{ BearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  tenantId: { type: 'string', description: 'ID of the tenant to invite the user to' },
                  email: { type: 'string', format: 'email', description: 'Email address of the user to invite' },
                  role: { 
                    type: 'string', 
                    enum: ['invoice_capturer', 'approver', 'accountant', 'tenant_admin', 'snapper', 'checker'], 
                    description: 'Role to assign to the user' 
                  },
                  displayName: { type: 'string', description: 'Display name for the user' },
                },
                required: ['tenantId', 'email', 'role'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'User invited successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/User' },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '409': { description: 'User already exists' },
        },
      },
    },
    '/users/{userId}': {
      put: {
        summary: 'Update user',
        description: 'Updates user information such as role, status, and display name. Only tenant admins can update users in their tenant, and system admins can update any user.',
        tags: ['Users'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'userId',
            in: 'path',
            description: 'ID of the user to update',
            required: true,
            schema: { type: 'string' },
          },
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  tenantId: { type: 'string', description: 'ID of the tenant the user belongs to' },
                  role: { 
                    type: 'string', 
                    enum: ['invoice_capturer', 'approver', 'accountant', 'tenant_admin', 'system_admin', 'snapper', 'checker'], 
                    description: 'Updated role for the user' 
                  },
                  status: { 
                    type: 'string', 
                    enum: ['active', 'disabled', 'pending', 'invited'], 
                    description: 'Updated status for the user' 
                  },
                  displayName: { type: 'string', description: 'Updated display name for the user' },
                },
                required: ['tenantId'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'User updated successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/User' },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'User not found' },
        },
      },
      delete: {
        summary: 'Delete user',
        description: 'Removes a user from the system. This operation is permanent. Only tenant admins can delete users in their tenant, and system admins can delete any user.',
        tags: ['Users'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'userId',
            in: 'path',
            description: 'ID of the user to delete',
            required: true,
            schema: { type: 'string' },
          },
          {
            name: 'tenantId',
            in: 'query',
            description: 'ID of the tenant the user belongs to',
            required: true,
            schema: { type: 'string' },
          },
        ],
        responses: {
          '200': {
            description: 'User deleted successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    success: { type: 'boolean' },
                    message: { type: 'string' },
                  },
                  required: ['success']
                },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'User not found' },
        },
      },
    },

    // Tenants Router
    '/tenants': {
      get: {
        summary: 'List all tenants',
        description: 'Retrieves a paginated list of all tenants in the system. Only system administrators can access this endpoint.',
        tags: ['Tenants'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'limit',
            in: 'query',
            description: 'Maximum number of tenants to return',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 50 },
          },
          {
            name: 'cursor',
            in: 'query',
            description: 'Cursor for pagination',
            required: false,
            schema: { type: 'string' },
          },
          {
            name: 'status',
            in: 'query',
            description: 'Filter by tenant status',
            required: false,
            schema: { type: 'string', enum: ['active', 'disabled', 'pending', 'deleted'] },
          },
        ],
        responses: {
          '200': {
            description: 'Tenants retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    tenants: {
                      type: 'array',
                      items: { $ref: '#/components/schemas/Tenant' },
                    },
                    nextCursor: { type: 'string', nullable: true },
                  },
                  required: ['tenants']
                },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
        },
      },
      post: {
        summary: 'Create new tenant',
        description: 'Creates a new tenant organization in the system. Only system administrators can create new tenants.',
        tags: ['Tenants'],
        security: [{ BearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  name: { type: 'string', description: 'Unique identifier name for the tenant', minLength: 3, maxLength: 50 },
                  displayName: { type: 'string', description: 'Human-readable name for the tenant', minLength: 1, maxLength: 100 },
                  industry: { type: 'string', description: 'Industry classification' },
                  country: { type: 'string', description: 'Country code' },
                  timezone: { type: 'string', description: 'Timezone' },
                  plan: { type: 'string', description: 'Subscription plan' },
                  settings: { type: 'object', description: 'Tenant-specific settings', additionalProperties: true },
                },
                required: ['name', 'displayName'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Tenant created successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Tenant' },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '409': { description: 'Tenant with this name already exists' },
        },
      },
    },
    '/tenants/{tenantId}': {
      get: {
        summary: 'Get tenant details',
        description: 'Retrieves detailed information about a specific tenant. Users can only view their own tenant, while system admins can view any tenant.',
        tags: ['Tenants'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'tenantId',
            in: 'path',
            description: 'ID of the tenant to retrieve',
            required: true,
            schema: { type: 'string' },
          },
        ],
        responses: {
          '200': {
            description: 'Tenant retrieved successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Tenant' },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'Tenant not found' },
        },
      },
      put: {
        summary: 'Update tenant',
        description: 'Updates an existing tenant organization. System admins can update any tenant, while tenant admins can only update their own tenant.',
        tags: ['Tenants'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'tenantId',
            in: 'path',
            description: 'ID of the tenant to update',
            required: true,
            schema: { type: 'string' },
          },
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  displayName: { type: 'string', description: 'Updated human-readable name', minLength: 1, maxLength: 100 },
                  industry: { type: 'string', description: 'Updated industry classification' },
                  country: { type: 'string', description: 'Updated country code' },
                  timezone: { type: 'string', description: 'Updated timezone' },
                  status: { type: 'string', enum: ['active', 'disabled', 'pending', 'deleted'], description: 'Updated tenant status' },
                  plan: { type: 'string', description: 'Updated subscription plan' },
                  settings: { type: 'object', description: 'Updated tenant-specific settings', additionalProperties: true },
                },
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Tenant updated successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Tenant' },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'Tenant not found' },
        },
      },
      delete: {
        summary: 'Delete tenant',
        description: 'Permanently deletes a tenant and all its associated data. Only system administrators can perform this operation.',
        tags: ['Tenants'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'tenantId',
            in: 'path',
            description: 'ID of the tenant to delete',
            required: true,
            schema: { type: 'string' },
          },
        ],
        responses: {
          '200': {
            description: 'Tenant marked for deletion',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    success: { type: 'boolean' },
                    message: { type: 'string' },
                  },
                  required: ['success']
                },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'Tenant not found' },
        },
      },
    },

    // Invoices Router
    '/invoices': {
      get: {
        summary: 'List invoices',
        description: 'Retrieves a paginated list of invoices for the specified tenant with optional filtering by status, vendor, and date range.',
        tags: ['Invoices'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'tenantId',
            in: 'query',
            description: 'ID of the tenant to get invoices for',
            required: true,
            schema: { type: 'string' },
          },
          {
            name: 'status',
            in: 'query',
            description: 'Filter by invoice status',
            required: false,
            schema: { type: 'string', enum: ['draft', 'pending', 'approved', 'rejected', 'processing', 'paid'] },
          },
          {
            name: 'vendorId',
            in: 'query',
            description: 'Filter by vendor ID',
            required: false,
            schema: { type: 'string' },
          },
          {
            name: 'dateFrom',
            in: 'query',
            description: 'Filter by date range (start)',
            required: false,
            schema: { type: 'string', format: 'date' },
          },
          {
            name: 'dateTo',
            in: 'query',
            description: 'Filter by date range (end)',
            required: false,
            schema: { type: 'string', format: 'date' },
          },
          {
            name: 'limit',
            in: 'query',
            description: 'Maximum number of invoices to return',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 50 },
          },
          {
            name: 'cursor',
            in: 'query',
            description: 'Cursor for pagination',
            required: false,
            schema: { type: 'string' },
          },
        ],
        responses: {
          '200': {
            description: 'Invoices retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    invoices: {
                      type: 'array',
                      items: { $ref: '#/components/schemas/Invoice' },
                    },
                    nextCursor: { type: 'string', nullable: true },
                  },
                  required: ['invoices']
                },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
        },
      },
      post: {
        summary: 'Create new invoice',
        description: 'Creates a new invoice record in the system with the provided details.',
        tags: ['Invoices'],
        security: [{ BearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  tenantId: { type: 'string', description: 'ID of the tenant to create the invoice for' },
                  vendorId: { type: 'string', description: 'ID of the vendor for this invoice' },
                  invoiceNumber: { type: 'string', description: 'Invoice number from the vendor' },
                  amount: { type: 'number', description: 'Total amount of the invoice', minimum: 0 },
                  currency: { type: 'string', description: 'Currency code (e.g., USD, EUR)', minLength: 3, maxLength: 3 },
                  invoiceDate: { type: 'string', format: 'date', description: 'Date the invoice was issued' },
                  dueDate: { type: 'string', format: 'date', description: 'Date the invoice is due' },
                  description: { type: 'string', description: 'Description of the invoice' },
                  lineItems: {
                    type: 'array',
                    description: 'Line items breakdown',
                    items: {
                      type: 'object',
                      properties: {
                        description: { type: 'string' },
                        quantity: { type: 'number', minimum: 0 },
                        unitPrice: { type: 'number', minimum: 0 },
                        amount: { type: 'number', minimum: 0 },
                        taxRate: { type: 'number' },
                        taxAmount: { type: 'number' },
                      },
                      required: ['description', 'quantity', 'unitPrice', 'amount'],
                    },
                  },
                  attachmentUrl: { type: 'string', format: 'uri', description: 'URL to the invoice attachment' },
                },
                required: ['tenantId', 'vendorId', 'invoiceNumber', 'amount', 'currency', 'invoiceDate'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Invoice created successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Invoice' },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'Vendor not found' },
          '409': { description: 'Invoice with this number already exists for this vendor' },
        },
      },
    },
    '/invoices/{invoiceId}': {
      get: {
        summary: 'Get invoice details',
        description: 'Retrieves detailed information about a specific invoice. Users can only view invoices in their tenant.',
        tags: ['Invoices'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'invoiceId',
            in: 'path',
            description: 'ID of the invoice to retrieve',
            required: true,
            schema: { type: 'string' },
          },
        ],
        responses: {
          '200': {
            description: 'Invoice retrieved successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Invoice' },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'Invoice not found' },
        },
      },
      put: {
        summary: 'Update invoice',
        description: 'Updates an existing invoice with new information. Only draft invoices can be updated.',
        tags: ['Invoices'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'invoiceId',
            in: 'path',
            description: 'ID of the invoice to update',
            required: true,
            schema: { type: 'string' },
          },
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  tenantId: { type: 'string', description: 'ID of the tenant the invoice belongs to' },
                  vendorId: { type: 'string', description: 'Updated vendor ID' },
                  invoiceNumber: { type: 'string', description: 'Updated invoice number' },
                  amount: { type: 'number', description: 'Updated total amount', minimum: 0 },
                  currency: { type: 'string', description: 'Updated currency code', minLength: 3, maxLength: 3 },
                  invoiceDate: { type: 'string', format: 'date', description: 'Updated invoice date' },
                  dueDate: { type: 'string', format: 'date', description: 'Updated due date' },
                  description: { type: 'string', description: 'Updated description' },
                  lineItems: {
                    type: 'array',
                    description: 'Updated line items',
                    items: {
                      type: 'object',
                      properties: {
                        description: { type: 'string' },
                        quantity: { type: 'number', minimum: 0 },
                        unitPrice: { type: 'number', minimum: 0 },
                        amount: { type: 'number', minimum: 0 },
                        taxRate: { type: 'number' },
                        taxAmount: { type: 'number' },
                      },
                      required: ['description', 'quantity', 'unitPrice', 'amount'],
                    },
                  },
                  attachmentUrl: { type: 'string', format: 'uri', description: 'Updated attachment URL' },
                },
                required: ['tenantId'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Invoice updated successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Invoice' },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden - Only draft invoices can be updated' },
          '404': { description: 'Invoice not found' },
          '409': { description: 'Invoice with this number already exists for this vendor' },
        },
      },
      delete: {
        summary: 'Delete invoice',
        description: 'Permanently removes an invoice from the system. Only draft invoices can be deleted.',
        tags: ['Invoices'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'invoiceId',
            in: 'path',
            description: 'ID of the invoice to delete',
            required: true,
            schema: { type: 'string' },
          },
          {
            name: 'tenantId',
            in: 'query',
            description: 'ID of the tenant the invoice belongs to',
            required: true,
            schema: { type: 'string' },
          },
        ],
        responses: {
          '200': {
            description: 'Invoice deleted successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    success: { type: 'boolean' },
                    message: { type: 'string' },
                  },
                  required: ['success']
                },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden - Only draft invoices can be deleted' },
          '404': { description: 'Invoice not found' },
        },
      },
    },
    '/invoices/{invoiceId}/approve': {
      post: {
        summary: 'Approve invoice',
        description: 'Changes the status of an invoice to approved. Only users with approver role can approve invoices.',
        tags: ['Invoices'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'invoiceId',
            in: 'path',
            description: 'ID of the invoice to approve',
            required: true,
            schema: { type: 'string' },
          },
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  tenantId: { type: 'string', description: 'ID of the tenant the invoice belongs to' },
                  comments: { type: 'string', description: 'Approval comments' },
                },
                required: ['tenantId'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Invoice approved successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Invoice' },
              },
            },
          },
          '400': { description: 'Bad Request - Only pending invoices can be approved' },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'Invoice not found' },
        },
      },
    },
    '/invoices/{invoiceId}/reject': {
      post: {
        summary: 'Reject invoice',
        description: 'Changes the status of an invoice to rejected. Only users with approver role can reject invoices.',
        tags: ['Invoices'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'invoiceId',
            in: 'path',
            description: 'ID of the invoice to reject',
            required: true,
            schema: { type: 'string' },
          },
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  tenantId: { type: 'string', description: 'ID of the tenant the invoice belongs to' },
                  reason: { type: 'string', description: 'Reason for rejection', minLength: 1 },
                },
                required: ['tenantId', 'reason'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Invoice rejected successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Invoice' },
              },
            },
          },
          '400': { description: 'Bad Request - Only pending invoices can be rejected' },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'Invoice not found' },
        },
      },
    },
    '/invoices/{invoiceId}/pdf': {
      get: {
        summary: 'Download invoice PDF',
        description: 'Generates or retrieves a PDF version of the invoice for download.',
        tags: ['Invoices'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'invoiceId',
            in: 'path',
            description: 'ID of the invoice to download',
            required: true,
            schema: { type: 'string' },
          },
          {
            name: 'tenantId',
            in: 'query',
            description: 'ID of the tenant the invoice belongs to',
            required: true,
            schema: { type: 'string' },
          },
        ],
        responses: {
          '200': {
            description: 'PDF URL retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    pdfUrl: { type: 'string', format: 'uri' },
                    generated: { type: 'boolean' },
                  },
                  required: ['pdfUrl', 'generated']
                },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'Invoice not found' },
          '501': { description: 'Not Implemented - PDF generation is not implemented yet' },
        },
      },
    },

    // Vendors Router
    '/vendors': {
      get: {
        summary: 'List vendors',
        description: 'Retrieves a paginated list of vendors for the specified tenant with optional filtering',
        tags: ['Vendors'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'tenantId',
            in: 'query',
            description: 'ID of the tenant to get vendors for',
            required: true,
            schema: { type: 'string' },
          },
          {
            name: 'active',
            in: 'query',
            description: 'Filter for active/inactive vendors',
            required: false,
            schema: { type: 'boolean' },
          },
          {
            name: 'limit',
            in: 'query',
            description: 'Maximum number of vendors to return',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 50 },
          },
          {
            name: 'cursor',
            in: 'query',
            description: 'Cursor for pagination',
            required: false,
            schema: { type: 'string' },
          },
          {
            name: 'search',
            in: 'query',
            description: 'Search term to filter vendors by name',
            required: false,
            schema: { type: 'string' },
          },
        ],
        responses: {
          '200': {
            description: 'Vendors retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    vendors: {
                      type: 'array',
                      items: { $ref: '#/components/schemas/Vendor' },
                    },
                    nextCursor: { type: 'string', nullable: true },
                  },
                  required: ['vendors']
                },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
        },
      },
      post: {
        summary: 'Create new vendor',
        description: 'Creates a new vendor record for the specified tenant',
        tags: ['Vendors'],
        security: [{ BearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  tenantId: { type: 'string', description: 'ID of the tenant to create the vendor for' },
                  name: { type: 'string', description: 'Name of the vendor', minLength: 1, maxLength: 100 },
                  email: { type: 'string', format: 'email', description: 'Email address for the vendor' },
                  phone: { type: 'string', description: 'Phone number for the vendor' },
                  address: {
                    type: 'object',
                    description: 'Address information',
                    properties: {
                      street: { type: 'string' },
                      city: { type: 'string' },
                      state: { type: 'string' },
                      postalCode: { type: 'string' },
                      country: { type: 'string' },
                    },
                  },
                  taxId: { type: 'string', description: 'Tax identification number' },
                  active: { type: 'boolean', description: 'Whether the vendor is active', default: true },
                },
                required: ['tenantId', 'name'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Vendor created successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Vendor' },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
        },
      },
    },
    '/vendors/{vendorId}': {
      get: {
        summary: 'Get vendor details',
        description: 'Retrieves detailed information about a specific vendor',
        tags: ['Vendors'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'vendorId',
            in: 'path',
            description: 'ID of the vendor to retrieve',
            required: true,
            schema: { type: 'string' },
          },
          {
            name: 'tenantId',
            in: 'query',
            description: 'ID of the tenant the vendor belongs to',
            required: true,
            schema: { type: 'string' },
          },
        ],
        responses: {
          '200': {
            description: 'Vendor retrieved successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Vendor' },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'Vendor not found' },
        },
      },
      put: {
        summary: 'Update vendor',
        description: 'Updates the information for an existing vendor',
        tags: ['Vendors'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'vendorId',
            in: 'path',
            description: 'ID of the vendor to update',
            required: true,
            schema: { type: 'string' },
          },
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  tenantId: { type: 'string', description: 'ID of the tenant the vendor belongs to' },
                  name: { type: 'string', description: 'Updated name of the vendor', minLength: 1, maxLength: 100 },
                  email: { type: 'string', format: 'email', description: 'Updated email address for the vendor' },
                  phone: { type: 'string', description: 'Updated phone number for the vendor' },
                  address: {
                    type: 'object',
                    description: 'Updated address information',
                    properties: {
                      street: { type: 'string' },
                      city: { type: 'string' },
                      state: { type: 'string' },
                      postalCode: { type: 'string' },
                      country: { type: 'string' },
                    },
                  },
                  taxId: { type: 'string', description: 'Updated tax identification number' },
                  active: { type: 'boolean', description: 'Updated active status' },
                },
                required: ['tenantId'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Vendor updated successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Vendor' },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'Vendor not found' },
        },
      },
      delete: {
        summary: 'Delete vendor',
        description: 'Permanently deletes a vendor from the system',
        tags: ['Vendors'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'vendorId',
            in: 'path',
            description: 'ID of the vendor to delete',
            required: true,
            schema: { type: 'string' },
          },
          {
            name: 'tenantId',
            in: 'query',
            description: 'ID of the tenant the vendor belongs to',
            required: true,
            schema: { type: 'string' },
          },
        ],
        responses: {
          '200': {
            description: 'Vendor deleted successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    success: { type: 'boolean' },
                    message: { type: 'string' },
                  },
                  required: ['success']
                },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'Vendor not found' },
          '409': { description: 'Conflict - Cannot delete vendor that is used in invoices' },
        },
      },
    },

    // OCR Router
    '/ocr/process': {
      post: {
        summary: 'Process invoice with OCR',
        description: 'Submits an invoice for OCR processing using the tiered approach (Gemma → Gemini → Document AI) to extract data efficiently.',
        tags: ['OCR'],
        security: [{ BearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  invoiceId: { type: 'string', description: 'ID of the invoice to process' },
                  fileUrl: { type: 'string', format: 'uri', description: 'URL of the invoice file to process' },
                  initialTier: { type: 'string', enum: ['tier1', 'tier2', 'tier3'], description: 'Starting OCR tier (defaults to TIER1)' },
                  forceReprocess: { type: 'boolean', description: 'Whether to force reprocessing even if results exist', default: false },
                },
                required: ['invoiceId', 'fileUrl'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'OCR processing started successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    jobId: { type: 'string' },
                    status: { type: 'string', enum: ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'] },
                    message: { type: 'string' },
                    tier: { type: 'string', enum: ['tier1', 'tier2', 'tier3'] },
                    createdAt: { type: 'string', format: 'date-time' },
                  },
                  required: ['jobId', 'status', 'tier']
                },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'Invoice or file not found' },
        },
      },
    },
    '/ocr/results/{resultId}': {
      get: {
        summary: 'Get OCR result',
        description: 'Retrieves the results of an OCR processing job, including extracted fields and confidence scores.',
        tags: ['OCR'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'resultId',
            in: 'path',
            description: 'ID of the OCR result to retrieve',
            required: true,
            schema: { type: 'string' },
          },
        ],
        responses: {
          '200': {
            description: 'OCR result retrieved successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/OcrResult' },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'OCR result not found' },
        },
      },
    },
    '/ocr/{resultId}/reprocess': {
      post: {
        summary: 'Reprocess with higher tier',
        description: 'Resubmits an invoice for OCR processing using a higher tier to improve extraction accuracy.',
        tags: ['OCR'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'resultId',
            in: 'path',
            description: 'ID of the previous OCR result',
            required: true,
            schema: { type: 'string' },
          },
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  tier: { type: 'string', enum: ['tier2', 'tier3'], description: 'OCR tier to use for reprocessing' },
                },
                required: ['tier'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Reprocessing started successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    jobId: { type: 'string' },
                    status: { type: 'string', enum: ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'] },
                    message: { type: 'string' },
                    tier: { type: 'string', enum: ['tier2', 'tier3'] },
                    createdAt: { type: 'string', format: 'date-time' },
                  },
                  required: ['jobId', 'status', 'tier']
                },
              },
            },
          },
          '400': { description: 'Bad Request - Cannot reprocess with same or lower tier' },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'OCR result not found' },
        },
      },
    },
    '/ocr/history/{invoiceId}': {
      get: {
        summary: 'Get OCR processing history',
        description: 'Retrieves the history of OCR processing attempts for a specific invoice, including all tiers used and results.',
        tags: ['OCR'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'invoiceId',
            in: 'path',
            description: 'ID of the invoice to get history for',
            required: true,
            schema: { type: 'string' },
          },
        ],
        responses: {
          '200': {
            description: 'OCR history retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    invoiceId: { type: 'string' },
                    results: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          id: { type: 'string' },
                          tier: { type: 'string', enum: ['tier1', 'tier2', 'tier3'] },
                          status: { type: 'string', enum: ['pending', 'processing', 'completed', 'failed'] },
                          confidence: { type: 'number' },
                          createdAt: { type: 'string', format: 'date-time' },
                          completedAt: { type: 'string', format: 'date-time', nullable: true },
                          processingTimeMs: { type: 'number', nullable: true },
                          errorMessage: { type: 'string', nullable: true },
                        },
                        required: ['id', 'tier', 'status']
                      },
                    },
                    jobs: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          id: { type: 'string' },
                          tier: { type: 'string', enum: ['tier1', 'tier2', 'tier3'] },
                          status: { type: 'string', enum: ['pending', 'processing', 'completed', 'failed'] },
                          createdAt: { type: 'string', format: 'date-time' },
                          createdBy: { type: 'string' },
                          completedAt: { type: 'string', format: 'date-time', nullable: true },
                          errorMessage: { type: 'string', nullable: true },
                        },
                        required: ['id', 'tier', 'status']
                      },
                    },
                  },
                  required: ['invoiceId', 'results', 'jobs']
                },
              },
            },
          },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'Invoice not found' },
        },
      },
    },
    '/ocr/{resultId}/upgrade': {
      post: {
        summary: 'Upgrade OCR tier',
        description: 'Manually upgrades the OCR processing tier for a result to improve extraction accuracy.',
        tags: ['OCR'],
        security: [{ BearerAuth: [] }],
        parameters: [
          {
            name: 'resultId',
            in: 'path',
            description: 'ID of the OCR result to upgrade',
            required: true,
            schema: { type: 'string' },
          },
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  targetTier: { type: 'string', enum: ['tier2', 'tier3'], description: 'Target OCR tier to upgrade to' },
                },
                required: ['targetTier'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Upgrade started successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    jobId: { type: 'string' },
                    status: { type: 'string', enum: ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'] },
                    message: { type: 'string' },
                    tier: { type: 'string', enum: ['tier2', 'tier3'] },
                    createdAt: { type: 'string', format: 'date-time' },
                  },
                  required: ['jobId', 'status', 'tier']
                },
              },
            },
          },
          '400': { description: 'Bad Request - Cannot upgrade to same or lower tier' },
          '401': { description: 'Unauthorized' },
          '403': { description: 'Forbidden' },
          '404': { description: 'OCR result not found' },
        },
      },
    },
  };

  // Add paths to the OpenAPI document
  openApiDocument.paths = paths;

  return openApiDocument;
}

/**
 * Count the number of operations in the OpenAPI document
 * @param openApiDocument The generated OpenAPI document
 * @returns The number of operations (endpoints)
 */
function countOperations(openApiDocument) {
  let count = 0;
  const paths = openApiDocument.paths || {};
  
  for (const path of Object.values(paths)) {
    // Count each HTTP method as an operation
    for (const method of ['get', 'post', 'put', 'patch', 'delete']) {
      if (path[method]) {
        count++;
      }
    }
  }
  
  return count;
}

/**
 * Validate the OpenAPI document against the schema
 * @param openApiDocument The generated OpenAPI document
 * @returns Validation result with errors if any
 */
function validateOpenApiDocument(openApiDocument) {
  const validator = new OpenAPISchemaValidator({ version: 3 });
  return validator.validate(openApiDocument);
}

/**
 * Format file size in a human-readable format
 * @param bytes File size in bytes
 * @returns Formatted file size (e.g., "1.5 KB")
 */
function formatFileSize(bytes) {
  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
}

/**
 * Main function to generate and save OpenAPI documentation
 */
async function generateOpenApi() {
  try {
    console.log('🚀 Generating static OpenAPI documentation...');
    
    // Generate the OpenAPI document
    const openApiDocument = generateOpenApiDocument();
    
    // Validate the generated document
    console.log('🔍 Validating OpenAPI document...');
    const validationResult = validateOpenApiDocument(openApiDocument);
    
    if (validationResult.errors && validationResult.errors.length > 0) {
      console.error('❌ OpenAPI validation failed:');
      validationResult.errors.forEach((error) => {
        console.error(`- ${error.message} at ${error.path}`);
      });
      process.exit(1);
    }
    
    // Count operations to ensure we have enough endpoints
    const operationCount = countOperations(openApiDocument);
    if (operationCount < 10) {
      console.warn(`⚠️ Warning: Only ${operationCount} operations found. Expected at least 10.`);
    } else {
      console.log(`✅ Found ${operationCount} API operations`);
    }
    
    // Convert to JSON string with pretty formatting
    const jsonContent = JSON.stringify(openApiDocument, null, 2);
    const contentSize = Buffer.byteLength(jsonContent, 'utf8');
    
    // Ensure the public directory exists
    if (!existsSync(PUBLIC_DIR)) {
      console.log(`📁 Creating public directory: ${PUBLIC_DIR}`);
      await mkdir(PUBLIC_DIR, { recursive: true });
    }
    
    // Write to repository root
    console.log(`📝 Writing to ${ROOT_OUTPUT_PATH}`);
    await writeFile(ROOT_OUTPUT_PATH, jsonContent);
    
    // Write to public directory for serving in production
    console.log(`📝 Writing to ${PUBLIC_OUTPUT_PATH}`);
    await writeFile(PUBLIC_OUTPUT_PATH, jsonContent);
    
    console.log(`✅ OpenAPI documentation generated successfully!`);
    console.log(`📊 Statistics:`);
    console.log(`   - File size: ${formatFileSize(contentSize)}`);
    console.log(`   - API endpoints: ${operationCount}`);
    console.log(`   - Schemas: ${Object.keys(openApiDocument.components?.schemas || {}).length}`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error generating OpenAPI documentation:');
    console.error(error);
    process.exit(1);
  }
}

// Execute the main function
generateOpenApi();
