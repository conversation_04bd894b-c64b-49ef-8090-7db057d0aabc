/**
 * tRPC OpenAPI Configuration
 * 
 * This file configures the OpenAPI metadata support for tRPC procedures.
 * It defines the metadata structure, security schemes, and exports configuration
 * that can be used in the tRPC initialization.
 */
import { OpenApiMeta, createOpenApiHttpHandler } from 'trpc-to-openapi';
import { TRPCError } from '@trpc/server';

/**
 * OpenAPI procedure metadata type
 * This extends the base OpenApiMeta type with additional properties
 * specific to our API documentation needs.
 */
export interface OpenApiMetadata extends OpenApiMeta {
  /** HTTP method for the endpoint */
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  
  /** API endpoint path (can include path parameters with curly braces) */
  path: string;
  
  /** Brief description of what the endpoint does */
  summary: string;
  
  /** Detailed description of the endpoint (optional) */
  description?: string;
  
  /** Tags for grouping endpoints in the OpenAPI documentation */
  tags?: string[];
  
  /** Whether this endpoint requires authentication */
  protect?: boolean;
  
  /** Response status code (defaults to 200 for success) */
  statusCode?: number;
  
  /** Whether to include the operation in the OpenAPI document */
  enabled?: boolean;
}

/**
 * OpenAPI configuration object
 * This defines the global settings for the OpenAPI document generation
 */
export const openApiConfig = {
  title: 'AiClearBill API',
  description: 'API for AiClearBill invoice processing platform',
  version: '1.0.0',
  baseUrl: process.env.API_URL || 'http://localhost:4000',
  docsUrl: '/docs',
  
  /**
   * Security schemes configuration
   * Defines how authentication is handled in the API
   */
  securitySchemes: {
    BearerAuth: {
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
      description: 'JWT Bearer token authentication'
    }
  },
  
  /**
   * Default security requirements
   * Applied to protected endpoints (where protect: true)
   */
  security: [
    {
      BearerAuth: []
    }
  ],
  
  /**
   * Response handler for OpenAPI endpoints
   * Maps tRPC errors to appropriate HTTP status codes
   */
  responseMeta: ({ data, errors, ctx, input, path, type, meta }) => {
    // Default to 200 OK for successful operations
    const statusCode = meta?.statusCode || 200;
    
    // If there are errors, map them to appropriate HTTP status codes
    if (errors && errors.length > 0) {
      const error = errors[0];
      if (error instanceof TRPCError) {
        switch (error.code) {
          case 'UNAUTHORIZED':
            return { status: 401 };
          case 'FORBIDDEN':
            return { status: 403 };
          case 'NOT_FOUND':
            return { status: 404 };
          case 'METHOD_NOT_SUPPORTED':
            return { status: 405 };
          case 'TIMEOUT':
            return { status: 408 };
          case 'CONFLICT':
            return { status: 409 };
          case 'PRECONDITION_FAILED':
            return { status: 412 };
          case 'PAYLOAD_TOO_LARGE':
            return { status: 413 };
          case 'UNPROCESSABLE_CONTENT':
            return { status: 422 };
          case 'TOO_MANY_REQUESTS':
            return { status: 429 };
          case 'INTERNAL_SERVER_ERROR':
            return { status: 500 };
          case 'BAD_REQUEST':
          default:
            return { status: 400 };
        }
      }
      return { status: 500 };
    }
    
    return { status: statusCode };
  }
};

/**
 * Helper function to create a security object for protected endpoints
 * @returns Security object for OpenAPI documentation
 */
export const createSecurityObject = () => {
  return [{ BearerAuth: [] }];
};

export default openApiConfig;
