import { initializeApp, cert, type ServiceAccount } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';
import path from 'path';
import fs from 'fs';
import { logger } from './logger';

// Initialize Firebase Admin
const initializeFirebase = () => {
  try {
    // Skip real Firebase initialization in test environment
    if (process.env.NODE_ENV === 'test') {
      logger.info('Test environment detected - using mock Firebase');
      // Return mock Firebase objects for testing
      return require('./firebase-test').firebase;
    }
    
    // Check if running in GCP environment (Production)
    if (process.env.NODE_ENV === 'production' && process.env.GOOGLE_APPLICATION_CREDENTIALS) {
      // In Cloud Run, we use the default credentials
      logger.info('Initializing Firebase with default credentials');
      initializeApp();
    } else {
      // Local development - use service account file
      const serviceAccountPath = process.env.FIREBASE_SERVICE_ACCOUNT_PATH || 
        path.join(process.cwd(), 'firebase-credentials.json');
      
      if (!fs.existsSync(serviceAccountPath)) {
        throw new Error(`Firebase service account file not found at: ${serviceAccountPath}`);
      }
      
      logger.info(`Initializing Firebase with service account: ${serviceAccountPath}`);
      const serviceAccount = JSON.parse(
        fs.readFileSync(serviceAccountPath, 'utf8')
      ) as ServiceAccount;
      
      initializeApp({
        credential: cert(serviceAccount),
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
      });
    }
    
    // Initialize auth, firestore, and storage
    const auth = getAuth();
    const firestore = getFirestore();
    const storage = getStorage();
    
    // Set Firestore settings
    firestore.settings({
      ignoreUndefinedProperties: true,
    });
    
    logger.info('Firebase initialization successful');
    return { auth, firestore, storage };
  } catch (error) {
    logger.error('Firebase initialization failed:', error);
    throw error;
  }
};

export const firebase = initializeFirebase();
