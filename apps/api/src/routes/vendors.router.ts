import { z } from 'zod';
import { router, authProcedure } from '../trpc/trpc';
import { TRPCError } from '@trpc/server';
import { firebase } from '../config/firebase';
import { logger } from '../config/logger';

/**
 * Router for vendor management operations
 */
export const vendorsRouter = router({
  /**
   * Get a list of vendors for the current tenant
   */
  getVendors: authProcedure
    .input(
      z.object({
        tenantId: z.string(),
        active: z.boolean().optional(),
        limit: z.number().min(1).max(100).optional().default(50),
        cursor: z.string().optional(),
        search: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      // Verify that the user has access to this tenant
      await ctx.validateTenantAccess(input.tenantId);

      try {
        // Start building the query
        let vendorsQuery = firebase.firestore
          .collection('vendors')
          .where('tenantId', '==', input.tenantId)
          .orderBy('name');

        // Apply active filter if specified
        if (input.active !== undefined) {
          vendorsQuery = vendorsQuery.where('active', '==', input.active);
        }

        // Apply search filter if specified
        if (input.search) {
          const searchLower = input.search.toLowerCase();
          // Firestore doesn't support text search, so we'll filter in-memory
          // In a production app, we might want to use a dedicated search service
          vendorsQuery = vendorsQuery.where('nameSearchable', '>=', searchLower)
            .where('nameSearchable', '<=', searchLower + '\uf8ff');
        }

        // Apply pagination
        if (input.cursor) {
          const cursorDoc = await firebase.firestore.collection('vendors').doc(input.cursor).get();
          if (!cursorDoc.exists) {
            throw new TRPCError({
              code: 'BAD_REQUEST',
              message: 'Invalid cursor',
            });
          }
          vendorsQuery = vendorsQuery.startAfter(cursorDoc);
        }

        // Execute the query
        const vendorsSnapshot = await vendorsQuery.limit(input.limit).get();
        
        const vendors = vendorsSnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            tenantId: data.tenantId,
            name: data.name,
            taxNumber: data.taxNumber,
            address: data.address,
            contactInfo: data.contactInfo,
            defaultAccountCode: data.defaultAccountCode,
            defaultExpenseCategory: data.defaultExpenseCategory,
            active: data.active,
            createdAt: data.createdAt?.toDate(),
            updatedAt: data.updatedAt?.toDate()
          };
        });

        // Calculate the next cursor
        let nextCursor: string | undefined;
        if (vendors.length === input.limit) {
          nextCursor = vendors[vendors.length - 1].id;
        }

        return {
          vendors,
          nextCursor
        };
      } catch (error) {
        logger.error('Error fetching vendors:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch vendors',
          cause: error,
        });
      }
    }),

  /**
   * Get a specific vendor by ID
   */
  getVendor: authProcedure
    .input(
      z.object({
        vendorId: z.string(),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        const vendorDoc = await firestore.collection('vendors').doc(input.vendorId).get();
        
        if (!vendorDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Vendor not found',
          });
        }
        
        const data = vendorDoc.data()!;
        
        // Verify tenant access
        await ctx.validateTenantAccess(data.tenantId);
        
        return {
          id: vendorDoc.id,
          tenantId: data.tenantId,
          name: data.name,
          taxNumber: data.taxNumber,
          address: data.address,
          contactInfo: data.contactInfo,
          defaultAccountCode: data.defaultAccountCode,
          defaultExpenseCategory: data.defaultExpenseCategory,
          active: data.active,
          createdAt: data.createdAt?.toDate(),
          updatedAt: data.updatedAt?.toDate()
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        logger.error('Error fetching vendor:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch vendor',
          cause: error,
        });
      }
    }),

  /**
   * Create a new vendor
   */
  createVendor: authProcedure
    .input(
      z.object({
        tenantId: z.string(),
        name: z.string().min(1).max(100),
        taxNumber: z.string().optional(),
        address: z.string().optional(),
        contactInfo: z.object({
          email: z.string().email().optional(),
          phone: z.string().optional(),
          website: z.string().url().optional(),
        }).optional(),
        defaultAccountCode: z.string().optional(),
        defaultExpenseCategory: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Verify that the user has access to this tenant
      // and can manage vendors (Tenant Admin or Accountant role)
      await ctx.validateTenantAccess(input.tenantId, ['TenantAdmin', 'Accountant']);

      try {
        // Create the vendor object
        const now = new Date();
        const vendorData = {
          tenantId: input.tenantId,
          name: input.name,
          nameSearchable: input.name.toLowerCase(),
          taxNumber: input.taxNumber || null,
          address: input.address || null,
          contactInfo: input.contactInfo || null,
          defaultAccountCode: input.defaultAccountCode || null,
          defaultExpenseCategory: input.defaultExpenseCategory || null,
          active: true,
          createdAt: now,
          updatedAt: now,
          createdById: ctx.user.id,
        };

        // Add to Firestore
        const vendorRef = await firebase.firestore.collection('vendors').add(vendorData);
        
        return {
          id: vendorRef.id,
          ...vendorData
        };
      } catch (error) {
        logger.error('Error creating vendor:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create vendor',
          cause: error,
        });
      }
    }),

  /**
   * Update an existing vendor
   */
  updateVendor: authProcedure
    .input(
      z.object({
        vendorId: z.string(),
        name: z.string().min(1).max(100).optional(),
        taxNumber: z.string().optional().nullable(),
        address: z.string().optional().nullable(),
        contactInfo: z.object({
          email: z.string().email().optional().nullable(),
          phone: z.string().optional().nullable(),
          website: z.string().url().optional().nullable(),
        }).optional().nullable(),
        defaultAccountCode: z.string().optional().nullable(),
        defaultExpenseCategory: z.string().optional().nullable(),
        active: z.boolean().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        // Get the vendor to verify tenant access
        const vendorDoc = await firestore.collection('vendors').doc(input.vendorId).get();
        
        if (!vendorDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Vendor not found',
          });
        }
        
        const vendorData = vendorDoc.data()!;
        
        // Verify tenant access with proper role
        await ctx.validateTenantAccess(vendorData.tenantId, ['TenantAdmin', 'Accountant']);

        // Build the update object
        const updateData: Record<string, any> = {
          updatedAt: new Date()
        };

        if (input.name !== undefined) {
          updateData.name = input.name;
          updateData.nameSearchable = input.name.toLowerCase();
        }
        
        if (input.taxNumber !== undefined) {
          updateData.taxNumber = input.taxNumber;
        }
        
        if (input.address !== undefined) {
          updateData.address = input.address;
        }
        
        if (input.contactInfo !== undefined) {
          updateData.contactInfo = input.contactInfo;
        }
        
        if (input.defaultAccountCode !== undefined) {
          updateData.defaultAccountCode = input.defaultAccountCode;
        }
        
        if (input.defaultExpenseCategory !== undefined) {
          updateData.defaultExpenseCategory = input.defaultExpenseCategory;
        }
        
        if (input.active !== undefined) {
          updateData.active = input.active;
        }

        // Update in Firestore
        await firebase.firestore.collection('vendors').doc(input.vendorId).update(updateData);
        
        return {
          id: input.vendorId,
          ...vendorData,
          ...updateData,
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        logger.error('Error updating vendor:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update vendor',
          cause: error,
        });
      }
    }),

  /**
   * Delete a vendor (soft delete by setting active to false)
   */
  deleteVendor: authProcedure
    .input(
      z.object({
        vendorId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        // Get the vendor to verify tenant access
        const vendorDoc = await firestore.collection('vendors').doc(input.vendorId).get();
        
        if (!vendorDoc.exists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Vendor not found',
          });
        }
        
        const vendorData = vendorDoc.data()!;
        
        // Verify tenant access with proper role
        await ctx.validateTenantAccess(vendorData.tenantId, ['TenantAdmin']);

        // We implement soft delete by setting active to false
        await firebase.firestore.collection('vendors').doc(input.vendorId).update({
          active: false,
          updatedAt: new Date()
        });
        
        return { success: true };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        logger.error('Error deleting vendor:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete vendor',
          cause: error,
        });
      }
    }),
});
