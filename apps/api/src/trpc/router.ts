import { router } from './trpc';
import { authRouter } from '../routes/auth.router';
import { usersRouter } from '../routes/users.router';
import { tenantsRouter } from '../routes/tenants.router';
import { invoicesRouter } from '../routes/invoices.router';
import { vendorsRouter } from '../routes/vendors.router';
import { ocrRouter } from '../routes/ocr.router';

/**
 * Main application router that combines all sub-routers
 * This defines the entire API surface of our tRPC backend
 */
export const appRouter = router({
  auth: authRouter,
  users: usersRouter,
  tenants: tenantsRouter,
  invoices: invoicesRouter,
  vendors: vendorsRouter,
  ocr: ocrRouter,
});

// Export type definition of the API
export type AppRouter = typeof appRouter;
