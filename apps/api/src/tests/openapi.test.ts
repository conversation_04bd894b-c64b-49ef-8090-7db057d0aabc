/**
 * @file openapi.test.ts
 * @description Tests for OpenAPI documentation validation
 * 
 * This test suite validates the generated OpenAPI documentation against
 * the OpenAPI 3.1.0 schema and checks for completeness, correctness,
 * and quality of the API documentation.
 */

import { describe, it, expect } from 'vitest';
import { readFileSync, existsSync } from 'fs';
import { resolve } from 'path';
import OpenAPISchemaValidator from 'openapi-schema-validator';

// Path to the generated OpenAPI document
const OPENAPI_PATH = resolve(__dirname, '../../../../openapi.json');
const PUBLIC_OPENAPI_PATH = resolve(__dirname, '../../public/openapi.json');

// Expected tags in the OpenAPI document
const EXPECTED_TAGS = ['Auth', 'Users', 'Tenants', 'Invoices', 'Vendors', 'OCR'];

// Expected paths that should exist in the OpenAPI document
const EXPECTED_PATHS = [
  '/auth/user',
  '/auth/verify',
  '/users',
  '/users/invite',
  '/users/{userId}',
  '/tenants',
  '/tenants/{tenantId}',
  '/invoices',
  '/invoices/{invoiceId}',
  '/invoices/{invoiceId}/approve',
  '/invoices/{invoiceId}/reject',
  '/invoices/{invoiceId}/pdf',
  '/vendors',
  '/vendors/{vendorId}',
  '/ocr/process',
  '/ocr/results/{resultId}',
  '/ocr/{resultId}/reprocess',
  '/ocr/history/{invoiceId}',
  '/ocr/{resultId}/upgrade',
];

// Expected schemas that should be defined
const EXPECTED_SCHEMAS = ['User', 'Tenant', 'Invoice', 'Vendor', 'OcrResult', 'Error'];

// Sample paths with parameters to validate parameter definitions
const PATHS_WITH_PARAMETERS = [
  { path: '/users/{userId}', paramName: 'userId' },
  { path: '/tenants/{tenantId}', paramName: 'tenantId' },
  { path: '/invoices/{invoiceId}', paramName: 'invoiceId' },
  { path: '/vendors/{vendorId}', paramName: 'vendorId' },
  { path: '/ocr/results/{resultId}', paramName: 'resultId' },
  { path: '/ocr/history/{invoiceId}', paramName: 'invoiceId' },
];

// Protected endpoints that should have security requirements
const PROTECTED_ENDPOINTS = [
  { path: '/auth/user', method: 'get' },
  { path: '/users', method: 'get' },
  { path: '/tenants', method: 'get' },
  { path: '/invoices', method: 'get' },
  { path: '/vendors', method: 'post' },
  { path: '/ocr/process', method: 'post' },
];

describe('OpenAPI Documentation', () => {
  // Load the OpenAPI document
  let openApiDocument: any;

  it('should have generated openapi.json files', () => {
    // Check if the files exist
    expect(existsSync(OPENAPI_PATH)).toBe(true);
    expect(existsSync(PUBLIC_OPENAPI_PATH)).toBe(true);
    
    // Read the file
    const openApiContent = readFileSync(OPENAPI_PATH, 'utf8');
    
    // Parse the JSON
    expect(() => {
      openApiDocument = JSON.parse(openApiContent);
    }).not.toThrow();
    
    // Check that both files are identical
    const publicOpenApiContent = readFileSync(PUBLIC_OPENAPI_PATH, 'utf8');
    expect(openApiContent).toBe(publicOpenApiContent);
  });

  it('should be a valid OpenAPI 3.1.0 document', () => {
    const validator = new OpenAPISchemaValidator({ version: 3 });
    const result = validator.validate(openApiDocument);
    
    // If there are validation errors, log them for easier debugging
    if (result.errors && result.errors.length > 0) {
      console.error('OpenAPI validation errors:', result.errors);
    }
    
    expect(result.errors).toHaveLength(0);
  });

  it('should have proper metadata', () => {
    expect(openApiDocument.openapi).toBe('3.1.0');
    expect(openApiDocument.info).toBeDefined();
    expect(openApiDocument.info.title).toBe('AiClearBill API');
    expect(openApiDocument.info.description).toBe('API for AiClearBill invoice processing platform');
    expect(openApiDocument.info.version).toBe('1.0.0');
    
    // Check servers configuration
    expect(openApiDocument.servers).toBeDefined();
    expect(openApiDocument.servers).toHaveLength(1);
    expect(openApiDocument.servers[0].url).toBeDefined();
  });

  it('should have at least 25 operations', () => {
    let operationCount = 0;
    
    // Count operations in all paths
    for (const path of Object.values(openApiDocument.paths)) {
      for (const method of ['get', 'post', 'put', 'patch', 'delete']) {
        if ((path as any)[method]) {
          operationCount++;
        }
      }
    }
    
    // We expect at least 25 operations
    expect(operationCount).toBeGreaterThanOrEqual(25);
    console.log(`Found ${operationCount} API operations`);
  });

  it('should include all expected tags', () => {
    expect(openApiDocument.tags).toBeDefined();
    
    // Extract tag names
    const tagNames = openApiDocument.tags.map((tag: any) => tag.name);
    
    // Check that all expected tags are present
    for (const expectedTag of EXPECTED_TAGS) {
      expect(tagNames).toContain(expectedTag);
    }
    
    // Each tag should have a description
    for (const tag of openApiDocument.tags) {
      expect(tag.description).toBeDefined();
      expect(typeof tag.description).toBe('string');
      expect(tag.description.length).toBeGreaterThan(0);
    }
  });

  it('should have proper security schemes configured', () => {
    expect(openApiDocument.components).toBeDefined();
    expect(openApiDocument.components.securitySchemes).toBeDefined();
    expect(openApiDocument.components.securitySchemes.BearerAuth).toBeDefined();
    
    const bearerAuth = openApiDocument.components.securitySchemes.BearerAuth;
    expect(bearerAuth.type).toBe('http');
    expect(bearerAuth.scheme).toBe('bearer');
    expect(bearerAuth.bearerFormat).toBe('JWT');
  });

  it('should include all expected paths', () => {
    expect(openApiDocument.paths).toBeDefined();
    
    // Check that all expected paths are present
    for (const expectedPath of EXPECTED_PATHS) {
      expect(openApiDocument.paths[expectedPath]).toBeDefined();
    }
  });

  it('should have proper security requirements for protected endpoints', () => {
    // Check a sample of protected endpoints
    for (const endpoint of PROTECTED_ENDPOINTS) {
      const { path, method } = endpoint;
      const operation = openApiDocument.paths[path][method];
      
      expect(operation.security).toBeDefined();
      expect(operation.security).toEqual(expect.arrayContaining([{ BearerAuth: [] }]));
    }
  });

  it('should have all required component schemas', () => {
    expect(openApiDocument.components.schemas).toBeDefined();
    
    // Check that all expected schemas are present
    for (const expectedSchema of EXPECTED_SCHEMAS) {
      expect(openApiDocument.components.schemas[expectedSchema]).toBeDefined();
    }
    
    // Check that schemas have required properties
    const userSchema = openApiDocument.components.schemas.User;
    expect(userSchema.properties.uid).toBeDefined();
    expect(userSchema.properties.email).toBeDefined();
    expect(userSchema.properties.role).toBeDefined();
    
    const invoiceSchema = openApiDocument.components.schemas.Invoice;
    expect(invoiceSchema.properties.id).toBeDefined();
    expect(invoiceSchema.properties.tenantId).toBeDefined();
    expect(invoiceSchema.properties.amount).toBeDefined();
    expect(invoiceSchema.properties.status).toBeDefined();
  });

  it('should validate path parameters correctly', () => {
    // Check that paths with parameters define them correctly
    for (const { path, paramName } of PATHS_WITH_PARAMETERS) {
      // Get all operations for this path
      const pathObject = openApiDocument.paths[path];
      expect(pathObject).toBeDefined();
      
      // Check each method that exists for this path
      for (const method of ['get', 'post', 'put', 'patch', 'delete']) {
        const operation = pathObject[method];
        if (operation) {
          // The operation should have parameters
          expect(operation.parameters).toBeDefined();
          
          // Find the path parameter
          const pathParam = operation.parameters.find(
            (param: any) => param.name === paramName && param.in === 'path'
          );
          
          expect(pathParam).toBeDefined();
          expect(pathParam.required).toBe(true);
          expect(pathParam.schema).toBeDefined();
        }
      }
    }
  });

  it('should have proper operation metadata', () => {
    // Sample a few operations to check for metadata
    const sampleOperations = [
      { path: '/auth/user', method: 'get' },
      { path: '/users', method: 'get' },
      { path: '/invoices', method: 'post' },
      { path: '/vendors/{vendorId}', method: 'put' },
      { path: '/ocr/process', method: 'post' },
    ];
    
    for (const { path, method } of sampleOperations) {
      const operation = openApiDocument.paths[path][method];
      
      // Check for summary and description
      expect(operation.summary).toBeDefined();
      expect(typeof operation.summary).toBe('string');
      expect(operation.summary.length).toBeGreaterThan(0);
      
      expect(operation.description).toBeDefined();
      expect(typeof operation.description).toBe('string');
      expect(operation.description.length).toBeGreaterThan(0);
      
      // Check for tags
      expect(operation.tags).toBeDefined();
      expect(operation.tags.length).toBeGreaterThan(0);
      
      // The tag should be one of our expected tags
      expect(EXPECTED_TAGS).toContain(operation.tags[0]);
    }
  });

  it('should have proper response schemas', () => {
    // Sample operations to check for response schemas
    const sampleOperations = [
      { path: '/auth/user', method: 'get' },
      { path: '/users', method: 'get' },
      { path: '/invoices', method: 'post' },
      { path: '/vendors/{vendorId}', method: 'put' },
      { path: '/ocr/process', method: 'post' },
    ];
    
    for (const { path, method } of sampleOperations) {
      const operation = openApiDocument.paths[path][method];
      
      // Check for 200 response
      expect(operation.responses['200']).toBeDefined();
      expect(operation.responses['200'].description).toBeDefined();
      
      // Check for content type
      if (operation.responses['200'].content) {
        expect(operation.responses['200'].content['application/json']).toBeDefined();
        expect(operation.responses['200'].content['application/json'].schema).toBeDefined();
      }
    }
  });

  it('should have error responses for protected endpoints', () => {
    // Check protected endpoints for error responses
    for (const { path, method } of PROTECTED_ENDPOINTS) {
      const operation = openApiDocument.paths[path][method];
      
      // Should have 401 Unauthorized response
      expect(operation.responses['401']).toBeDefined();
      expect(operation.responses['401'].description).toBe('Unauthorized');
      
      // Should have 403 Forbidden response
      expect(operation.responses['403']).toBeDefined();
      expect(operation.responses['403'].description).toBe('Forbidden');
    }
  });

  it('should have proper request body schemas where applicable', () => {
    // Sample operations with request bodies
    const operationsWithRequestBody = [
      { path: '/auth/verify', method: 'post' },
      { path: '/users/invite', method: 'post' },
      { path: '/invoices', method: 'post' },
      { path: '/vendors', method: 'post' },
      { path: '/ocr/process', method: 'post' },
    ];
    
    for (const { path, method } of operationsWithRequestBody) {
      const operation = openApiDocument.paths[path][method];
      
      // Check request body
      expect(operation.requestBody).toBeDefined();
      expect(operation.requestBody.required).toBe(true);
      
      // Check content type
      expect(operation.requestBody.content['application/json']).toBeDefined();
      expect(operation.requestBody.content['application/json'].schema).toBeDefined();
      
      // Schema should have properties
      const schema = operation.requestBody.content['application/json'].schema;
      expect(schema.properties).toBeDefined();
      expect(Object.keys(schema.properties).length).toBeGreaterThan(0);
      
      // Check required properties if defined
      if (schema.required) {
        expect(Array.isArray(schema.required)).toBe(true);
        expect(schema.required.length).toBeGreaterThan(0);
        
        // All required properties should exist in properties
        for (const requiredProp of schema.required) {
          expect(schema.properties[requiredProp]).toBeDefined();
        }
      }
    }
  });

  it('should have consistent operation IDs', () => {
    // Check that all operations have unique operationIds
    const operationIds = new Set();
    
    for (const [path, pathObject] of Object.entries(openApiDocument.paths)) {
      for (const method of ['get', 'post', 'put', 'patch', 'delete']) {
        const operation = (pathObject as any)[method];
        if (operation) {
          // If operationId is defined, it should be unique
          if (operation.operationId) {
            expect(operationIds.has(operation.operationId)).toBe(false);
            operationIds.add(operation.operationId);
          }
        }
      }
    }
  });
});
