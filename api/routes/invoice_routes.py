from api.controllers.invoice_controller import (
    delete_invoice,
    get_invoice,
    get_invoices,
    process_invoice,
    update_invoice,
    upload_invoice,
)
from services.auth import login_required, tenant_required


def register_invoice_routes(bp):
    """Register invoice API routes"""

    # Invoice management
    bp.route("/invoices", methods=["GET"])(
        login_required(tenant_required(get_invoices))
    )

    bp.route("/invoices", methods=["POST"])(
        login_required(tenant_required(upload_invoice))
    )

    bp.route("/invoices/<int:invoice_id>", methods=["GET"])(
        login_required(tenant_required(get_invoice))
    )

    bp.route("/invoices/<int:invoice_id>", methods=["PUT"])(
        login_required(tenant_required(update_invoice))
    )

    bp.route("/invoices/<int:invoice_id>", methods=["DELETE"])(
        login_required(tenant_required(delete_invoice))
    )

    bp.route("/invoices/<int:invoice_id>/process", methods=["POST"])(
        login_required(
            tenant_required(
                lambda invoice_id: process_invoice(invoice_id) and {"success": True}
            )
        )
    )
