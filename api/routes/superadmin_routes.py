from flask import Blueprint

from api.controllers.superadmin_controller import (
    activity_logs,
    add_tenant,
    api_usage_dashboard,
    create_superadmin,
    delete_tenant,
    manage_tenants,
    manage_users,
    superadmin_dashboard,
    superadmin_login,
    superadmin_logout,
    system_health_dashboard,
    system_settings,
    tenant_details,
    update_tenant,
    update_tenant_quota,
    user_details,
)

# Create superadmin blueprint
superadmin_bp = Blueprint("superadmin", __name__, url_prefix="/loginadmin")

# Authentication
superadmin_bp.route("/login", methods=["GET", "POST"])(superadmin_login)
superadmin_bp.route("/logout")(superadmin_logout)

# Dashboard
superadmin_bp.route("/dashboard")(superadmin_dashboard)

# Tenants
superadmin_bp.route("/tenants")(manage_tenants)
superadmin_bp.route("/tenants/<int:tenant_id>")(tenant_details)
superadmin_bp.route("/tenants/<int:tenant_id>/quota", methods=["POST"])(
    update_tenant_quota
)
superadmin_bp.route("/tenants/add", methods=["POST"])(add_tenant)
superadmin_bp.route("/tenants/<int:tenant_id>/delete", methods=["POST"])(delete_tenant)
superadmin_bp.route("/tenants/<int:tenant_id>/update", methods=["POST"])(update_tenant)

# Users
superadmin_bp.route("/users")(manage_users)
superadmin_bp.route("/users/<int:user_id>")(user_details)

# System Settings
superadmin_bp.route("/settings", methods=["GET", "POST"])(system_settings)

# Activity Logs
superadmin_bp.route("/activity-logs")(activity_logs)

# API Usage
superadmin_bp.route("/api-usage")(api_usage_dashboard)

# System Health
superadmin_bp.route("/system-health")(system_health_dashboard)

# API routes for superadmin
superadmin_api_bp = Blueprint("superadmin_api", __name__, url_prefix="/api/superadmin")

# Create superadmin account (one-time setup)
superadmin_api_bp.route("/create", methods=["POST"])(create_superadmin)
