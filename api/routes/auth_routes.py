from api.controllers.auth_controller import (
    get_current_user,
    login,
    logout,
    set_current_tenant,
)
from services.auth import login_required


def register_auth_routes(bp):
    """Register authentication API routes"""

    # Login and authentication
    bp.route("/auth/login", methods=["POST"])(login)
    bp.route("/auth/logout", methods=["POST"])(logout)
    bp.route("/auth/user", methods=["GET"])(login_required(get_current_user))
    bp.route("/auth/tenant", methods=["POST"])(login_required(set_current_tenant))
