from api.controllers.vendor_controller import (
    create_vendor,
    delete_vendor,
    get_vendor,
    get_vendors,
    update_vendor,
)
from services.auth import login_required, tenant_required


def register_vendor_routes(bp):
    """Register vendor API routes"""

    # Vendor management
    bp.route("/vendors", methods=["GET"])(login_required(tenant_required(get_vendors)))

    bp.route("/vendors", methods=["POST"])(
        login_required(tenant_required(create_vendor))
    )

    bp.route("/vendors/<int:vendor_id>", methods=["GET"])(
        login_required(tenant_required(get_vendor))
    )

    bp.route("/vendors/<int:vendor_id>", methods=["PUT"])(
        login_required(tenant_required(update_vendor))
    )

    bp.route("/vendors/<int:vendor_id>", methods=["DELETE"])(
        login_required(tenant_required(delete_vendor))
    )
