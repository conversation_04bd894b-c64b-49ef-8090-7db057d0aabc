from api.controllers.tenant_controller import (
    create_tenant,
    delete_tenant,
    get_tenant,
    get_tenants,
    update_tenant,
)
from services.auth import admin_required, login_required


def register_tenant_routes(bp):
    """Register tenant API routes"""

    # Tenant management (admin only)
    bp.route("/tenants", methods=["GET"])(login_required(admin_required(get_tenants)))

    bp.route("/tenants", methods=["POST"])(
        login_required(admin_required(create_tenant))
    )

    bp.route("/tenants/<int:tenant_id>", methods=["GET"])(login_required(get_tenant))

    bp.route("/tenants/<int:tenant_id>", methods=["PUT"])(
        login_required(admin_required(update_tenant))
    )

    bp.route("/tenants/<int:tenant_id>", methods=["DELETE"])(
        login_required(admin_required(delete_tenant))
    )
