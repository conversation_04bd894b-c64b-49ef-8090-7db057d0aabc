from flask import Blueprint

from .auth_routes import register_auth_routes
from .invoice_routes import register_invoice_routes
from .tenant_routes import register_tenant_routes
from .vendor_routes import register_vendor_routes

# Create API blueprint
api_bp = Blueprint("api", __name__, url_prefix="/api")

# Import and register routes

# Register all routes
register_auth_routes(api_bp)
register_invoice_routes(api_bp)
register_vendor_routes(api_bp)
register_tenant_routes(api_bp)
