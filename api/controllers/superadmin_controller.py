import json
import logging
import uuid
from datetime import datetime, timedelta
from functools import wraps

from flask import (
    flash,
    g,
    jsonify,
    redirect,
    render_template,
    request,
    session,
    url_for,
)
from sqlalchemy.exc import IntegrityError
from werkzeug.security import check_password_hash, generate_password_hash

from api.models.invoice import Invoice
from api.models.system_metrics import (
    APIKeyUsage,
    SuperAdminUser,
    SystemHealthLog,
    SystemSetting,
    TenantQuota,
    UserActivity,
)
from api.models.tenant import Tenant
from api.models.user import User
from api.models.vendor import Vendor
from app import db

logger = logging.getLogger(__name__)


# Decorator for superadmin authentication
def superadmin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Check if user is logged in as superadmin
        admin_id = session.get("superadmin_id")
        if not admin_id:
            if request.path.startswith("/api/"):
                return jsonify({"error": "Superadmin access required"}), 403
            flash("Superadmin access required", "danger")
            return redirect(url_for("superadmin.superadmin_login"))

        # Get admin user
        admin = SuperAdminUser.query.get(admin_id)
        if not admin:
            session.pop("superadmin_id", None)
            if request.path.startswith("/api/"):
                return jsonify({"error": "Invalid superadmin session"}), 403
            flash("Invalid superadmin session", "danger")
            return redirect(url_for("superadmin.superadmin_login"))

        # Set admin in global context
        g.superadmin = admin

        return f(*args, **kwargs)

    return decorated_function


# Superadmin login
def superadmin_login():
    if request.method == "POST":
        username = request.form.get("username")
        password = request.form.get("password")

        admin = SuperAdminUser.query.filter_by(username=username).first()

        if admin and check_password_hash(admin.password_hash, password):
            # Set session
            session["superadmin_id"] = admin.id

            # Update last login
            admin.last_login = datetime.utcnow()
            db.session.commit()

            # Log activity
            record_activity(
                admin_id=admin.id,
                activity_type="superadmin_login",
                ip_address=request.remote_addr,
                user_agent=request.user_agent.string,
            )

            flash("Login successful", "success")
            return redirect(url_for("superadmin.superadmin_dashboard"))
        else:
            flash("Invalid username or password", "danger")

    return render_template("superadmin/login.html")


# Superadmin logout
def superadmin_logout():
    admin_id = session.get("superadmin_id")
    if admin_id:
        # Log activity
        record_activity(
            admin_id=admin_id,
            activity_type="superadmin_logout",
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string,
        )

    session.pop("superadmin_id", None)
    flash("You have been logged out", "info")
    return redirect(url_for("superadmin.superadmin_login"))


# Superadmin dashboard
@superadmin_required
def superadmin_dashboard():
    # Get basic stats
    stats = {
        "total_tenants": Tenant.query.count(),
        "total_users": User.query.count(),
        "total_invoices": Invoice.query.count(),
        "total_vendors": Vendor.query.count(),
        "active_users_24h": UserActivity.query.filter(
            UserActivity.activity_type == "login",
            UserActivity.timestamp >= datetime.utcnow() - timedelta(hours=24),
        )
        .with_entities(UserActivity.user_id)
        .distinct()
        .count(),
        "new_tenants_7d": Tenant.query.filter(
            Tenant.created_at >= datetime.utcnow() - timedelta(days=7)
        ).count(),
        "processed_invoices_30d": Invoice.query.filter(
            Invoice.status == "READY",
            Invoice.updated_at >= datetime.utcnow() - timedelta(days=30),
        ).count(),
    }

    # Top tenants by activity
    top_tenants = (
        db.session.query(
            UserActivity.tenant_id,
            Tenant.name,
            db.func.count(UserActivity.id).label("activity_count"),
        )
        .join(Tenant, UserActivity.tenant_id == Tenant.id)
        .filter(UserActivity.timestamp >= datetime.utcnow() - timedelta(days=30))
        .group_by(UserActivity.tenant_id, Tenant.name)
        .order_by(db.func.count(UserActivity.id).desc())
        .limit(5)
        .all()
    )

    # Recent activities
    recent_activities = (
        UserActivity.query.order_by(UserActivity.timestamp.desc()).limit(10).all()
    )

    # System health
    system_health = (
        SystemHealthLog.query.order_by(SystemHealthLog.timestamp.desc()).limit(5).all()
    )

    return render_template(
        "superadmin/dashboard.html",
        stats=stats,
        top_tenants=top_tenants,
        recent_activities=recent_activities,
        system_health=system_health,
    )


# Tenants management
@superadmin_required
def manage_tenants():
    page = request.args.get("page", 1, type=int)
    per_page = request.args.get("per_page", 10, type=int)

    query = Tenant.query

    # Apply filters
    search = request.args.get("search")
    if search:
        query = query.filter(Tenant.name.ilike(f"%{search}%"))

    status = request.args.get("status")
    if status:
        if status == "active":
            query = query.filter(Tenant.is_active)
        elif status == "inactive":
            query = query.filter(Tenant.is_active == False)

    # Apply sorting
    sort_by = request.args.get("sort_by", "created_at")
    sort_dir = request.args.get("sort_dir", "desc")

    if sort_dir == "desc":
        query = query.order_by(getattr(Tenant, sort_by).desc())
    else:
        query = query.order_by(getattr(Tenant, sort_by).asc())

    # Paginate
    pagination = query.paginate(page=page, per_page=per_page)
    tenants = pagination.items

    return render_template(
        "superadmin/tenants.html",
        tenants=tenants,
        pagination=pagination,
        search=search,
        status=status,
        sort_by=sort_by,
        sort_dir=sort_dir,
    )


# Tenant details
@superadmin_required
def tenant_details(tenant_id):
    tenant = Tenant.query.get_or_404(tenant_id)

    # Get tenant quota
    quota = TenantQuota.query.filter_by(tenant_id=tenant_id).first()
    if not quota:
        # Create default quota if none exists
        quota = TenantQuota(tenant_id=tenant_id)
        db.session.add(quota)
        db.session.commit()

    # Get tenant statistics
    users = User.query.filter_by(tenant_id=tenant_id).all()
    invoices = Invoice.query.filter_by(tenant_id=tenant_id).all()

    # Recent activities
    recent_activities = (
        UserActivity.query.filter_by(tenant_id=tenant_id)
        .order_by(UserActivity.timestamp.desc())
        .limit(20)
        .all()
    )

    # API usage
    api_usage = APIKeyUsage.query.filter_by(tenant_id=tenant_id).all()

    # Calculate storage usage
    storage_usage_mb = sum(invoice.file_size or 0 for invoice in invoices) / (
        1024 * 1024
    )

    return render_template(
        "superadmin/tenant_details.html",
        tenant=tenant,
        quota=quota,
        users=users,
        invoices=invoices,
        storage_usage_mb=storage_usage_mb,
        recent_activities=recent_activities,
        api_usage=api_usage,
    )


# Update tenant quota
@superadmin_required
def update_tenant_quota(tenant_id):
    Tenant.query.get_or_404(tenant_id)
    quota = TenantQuota.query.filter_by(tenant_id=tenant_id).first()

    if not quota:
        quota = TenantQuota(tenant_id=tenant_id)
        db.session.add(quota)

    # Update quota
    quota.max_users = request.form.get("max_users", type=int) or quota.max_users
    quota.max_storage_mb = (
        request.form.get("max_storage_mb", type=int) or quota.max_storage_mb
    )
    quota.max_invoices_per_month = (
        request.form.get("max_invoices_per_month", type=int)
        or quota.max_invoices_per_month
    )
    quota.max_api_calls_per_day = (
        request.form.get("max_api_calls_per_day", type=int)
        or quota.max_api_calls_per_day
    )
    quota.plan_type = request.form.get("plan_type") or quota.plan_type
    quota.is_trial = "is_trial" in request.form

    trial_expires = request.form.get("trial_expires_at")
    if trial_expires:
        try:
            quota.trial_expires_at = datetime.strptime(trial_expires, "%Y-%m-%d")
        except ValueError:
            flash("Invalid date format for trial expiration", "danger")

    db.session.commit()
    flash("Tenant quota updated successfully", "success")

    return redirect(url_for("superadmin.tenant_details", tenant_id=tenant_id))


# Users management
@superadmin_required
def manage_users():
    page = request.args.get("page", 1, type=int)
    per_page = request.args.get("per_page", 20, type=int)

    # Mock users for demo
    mock_users = [
        {
            "id": 1,
            "username": "john.doe",
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe",
            "roles": "admin,user",
            "tenant_id": 1,
            "tenant": {"name": "Sample Tenant"},
            "created_at": datetime.utcnow() - timedelta(days=30),
            "last_login": datetime.utcnow() - timedelta(hours=2),
            "is_active": True,
        },
        {
            "id": 2,
            "username": "jane.smith",
            "email": "<EMAIL>",
            "first_name": "Jane",
            "last_name": "Smith",
            "roles": "user",
            "tenant_id": 1,
            "tenant": {"name": "Sample Tenant"},
            "created_at": datetime.utcnow() - timedelta(days=25),
            "last_login": datetime.utcnow() - timedelta(days=1),
            "is_active": True,
        },
    ]

    try:
        # Try to get real users if the model exists
        users = []
        pagination = None
        tenants = []

        if "User" in globals():
            query = User.query

            # Apply filters
            search = request.args.get("search")
            if search:
                query = query.filter(
                    db.or_(
                        User.username.ilike(f"%{search}%"),
                        User.email.ilike(f"%{search}%"),
                        User.first_name.ilike(f"%{search}%"),
                        User.last_name.ilike(f"%{search}%"),
                    )
                )

            tenant_id = request.args.get("tenant_id", type=int)
            if tenant_id:
                query = query.filter(User.tenant_id == tenant_id)

            role = request.args.get("role")
            if role:
                query = query.filter(User.roles.like(f"%{role}%"))

            # Apply sorting
            sort_by = request.args.get("sort_by", "created_at")
            sort_dir = request.args.get("sort_dir", "desc")

            if sort_dir == "desc":
                query = query.order_by(getattr(User, sort_by).desc())
            else:
                query = query.order_by(getattr(User, sort_by).asc())

            # Paginate
            pagination = query.paginate(page=page, per_page=per_page)
            users = pagination.items

            # Get all tenants for filter dropdown
            tenants = Tenant.query.all()
        else:
            # Use mock data
            from flask_sqlalchemy import Pagination

            pagination = Pagination(None, page, per_page, len(mock_users), mock_users)
            users = mock_users
            search = request.args.get("search", "")
            tenant_id = request.args.get("tenant_id")
            role = request.args.get("role", "")
            sort_by = request.args.get("sort_by", "created_at")
            sort_dir = request.args.get("sort_dir", "desc")

    except Exception as e:
        # Use mock data if error occurred
        logger.error(f"Error in manage_users: {str(e)}")
        from flask_sqlalchemy import Pagination

        pagination = Pagination(None, page, per_page, len(mock_users), mock_users)
        users = mock_users
        search = request.args.get("search", "")
        tenant_id = request.args.get("tenant_id")
        role = request.args.get("role", "")
        sort_by = request.args.get("sort_by", "created_at")
        sort_dir = request.args.get("sort_dir", "desc")
        tenants = []

    # Define user stats for the dashboard view
    user_stats = {
        "total_users": len(users) if users else 0,
        "active_users": (
            len([u for u in users if getattr(u, "is_active", True)]) if users else 0
        ),
        "admins": (
            len(
                [
                    u
                    for u in users
                    if hasattr(u, "roles") and "admin" in getattr(u, "roles", "")
                ]
            )
            if users
            else 0
        ),
        "inactive_users": (
            len([u for u in users if not getattr(u, "is_active", True)]) if users else 0
        ),
    }

    return render_template(
        "superadmin/users.html",
        users=users,
        pagination=pagination,
        search=search,
        tenant_id=tenant_id,
        role=role,
        sort_by=sort_by,
        sort_dir=sort_dir,
        tenants=tenants,
        user_stats=user_stats,
    )


# User details
@superadmin_required
def user_details(user_id):
    try:
        user = None

        # Try to get real user if model exists
        if "User" in globals():
            user = User.query.get(user_id)

        # If user not found, or model doesn't exist, use mock data
        if not user:
            # Mock user for demo
            if user_id == "1":
                user = {
                    "id": 1,
                    "username": "john.doe",
                    "email": "<EMAIL>",
                    "first_name": "John",
                    "last_name": "Doe",
                    "roles": "admin,user",
                    "tenant_id": 1,
                    "tenant": {"name": "Sample Tenant"},
                    "created_at": datetime.utcnow() - timedelta(days=30),
                    "last_login": datetime.utcnow() - timedelta(hours=2),
                    "is_active": True,
                }
            else:
                user = {
                    "id": 2,
                    "username": "jane.smith",
                    "email": "<EMAIL>",
                    "first_name": "Jane",
                    "last_name": "Smith",
                    "roles": "user",
                    "tenant_id": 1,
                    "tenant": {"name": "Sample Tenant"},
                    "created_at": datetime.utcnow() - timedelta(days=25),
                    "last_login": datetime.utcnow() - timedelta(days=1),
                    "is_active": True,
                }

        # Mock recent activities for demo
        recent_activities = [
            {
                "id": 1,
                "timestamp": datetime.utcnow() - timedelta(hours=2),
                "activity_type": "login",
                "ip_address": "*************",
                "country_code": "US",
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Safari/537.36",
                "tenant_id": 1,
            },
            {
                "id": 2,
                "timestamp": datetime.utcnow() - timedelta(hours=1),
                "activity_type": "invoice_upload",
                "ip_address": "*************",
                "country_code": "US",
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Safari/537.36",
                "tenant_id": 1,
            },
        ]

        # Mock geo logins for demo
        geo_logins = [
            {"country_code": "US", "login_count": 42},
            {"country_code": "CA", "login_count": 15},
            {"country_code": "UK", "login_count": 7},
        ]

        # Try to get actual data if models exist
        if "User" in globals() and "UserActivity" in globals() and user_id:
            # Get user's recent activities
            try:
                db_activities = (
                    UserActivity.query.filter_by(user_id=user_id)
                    .order_by(UserActivity.timestamp.desc())
                    .limit(50)
                    .all()
                )

                if db_activities:
                    recent_activities = db_activities

                # Get logins by geography
                db_geo_logins = (
                    db.session.query(
                        UserActivity.country_code,
                        db.func.count(UserActivity.id).label("login_count"),
                    )
                    .filter(
                        UserActivity.user_id == user_id,
                        UserActivity.activity_type == "login",
                        UserActivity.country_code.isnot(None),
                    )
                    .group_by(UserActivity.country_code)
                    .order_by(db.func.count(UserActivity.id).desc())
                    .all()
                )

                if db_geo_logins:
                    geo_logins = db_geo_logins
            except Exception as e:
                logger.error(f"Error fetching user activities: {str(e)}")

        return render_template(
            "superadmin/user_details.html",
            user=user,
            recent_activities=recent_activities,
            geo_logins=geo_logins,
        )
    except Exception as e:
        logger.error(f"Error in user_details: {str(e)}")
        flash(f"Error retrieving user details: {str(e)}", "danger")
        return redirect(url_for("superadmin.manage_users"))


# System settings
@superadmin_required
def system_settings():
    if request.method == "POST":
        # Update settings
        for key, value in request.form.items():
            if key.startswith("setting_"):
                setting_id = key.replace("setting_", "")
                setting = SystemSetting.query.get(setting_id)
                if setting:
                    setting.value = value
                    setting.updated_by = g.superadmin.id
                    setting.updated_at = datetime.utcnow()

        db.session.commit()
        flash("Settings updated successfully", "success")

    # Get all settings
    settings = SystemSetting.query.all()

    return render_template("superadmin/settings.html", settings=settings)


# Activity logs
@superadmin_required
def activity_logs():
    # Get query parameters for filtering
    search = request.args.get("search", "").strip()
    log_level = request.args.get("log_level", "").strip()
    date_from = request.args.get("date_from", "").strip()
    date_to = request.args.get("date_to", "").strip()
    page = request.args.get("page", 1, type=int)
    request.args.get("format", "").strip().lower()

    # Log the action
    record_activity(
        session.get("superadmin_id"),
        "activity_logs_access",
        ip_address=request.remote_addr,
        user_agent=request.user_agent.string,
    )

    # Mock activity stats for demo
    activity_stats = {
        "total_activities": 243,
        "tenant_activities": 165,
        "admin_activities": 42,
        "system_events": 36,
        "active_tenants": 5,
    }

    # Mock recent activities for demo
    recent_activities = []

    # Query database for activity logs with filters
    # Fallback to original query logic if UserActivity exists
    query = UserActivity.query if "UserActivity" in globals() else []

    try:
        # Apply filters
        activity_type = request.args.get("activity_type")
        if activity_type and hasattr(query, "filter"):
            query = query.filter(UserActivity.activity_type == activity_type)

        tenant_id = request.args.get("tenant_id", type=int)
        if tenant_id and hasattr(query, "filter"):
            query = query.filter(UserActivity.tenant_id == tenant_id)

        user_id = request.args.get("user_id", type=int)
        if user_id and hasattr(query, "filter"):
            query = query.filter(UserActivity.user_id == user_id)

        # Apply sorting if possible
        if hasattr(query, "order_by"):
            query = query.order_by(UserActivity.timestamp.desc())

        # Paginate if possible
        if hasattr(query, "paginate"):
            pagination = query.paginate(page=page, per_page=10)
            activities = pagination.items
        else:
            # Mock pagination for template display
            from flask_sqlalchemy import Pagination

            pagination = Pagination(query, page, 10, 0, [])
            activities = []

    except Exception:
        # Mock pagination for template display
        from flask_sqlalchemy import Pagination

        pagination = Pagination(None, page, 10, 0, [])
        activities = []

    return render_template(
        "superadmin/activity_logs.html",
        activities=activities,
        recent_activities=recent_activities,
        activity_stats=activity_stats,
        pagination=pagination,
        search=search,
        log_level=log_level,
        date_from=date_from,
        date_to=date_to,
    )


# API usage dashboard
@superadmin_required
def api_usage_dashboard():
    # Mock usage stats for demo
    usage_stats = {
        "ocr_requests": 312,
        "ocr_requests_change": 8.3,
        "qbo_requests": 228,
        "qbo_requests_change": 5.2,
        "storage_used": "1.2 GB",
        "storage_limit": "5 GB",
        "avg_response_time": 115,
        "avg_response_time_change": -2.5,
    }

    # Mock top tenants for demo
    top_tenants = [
        {
            "id": 1,
            "name": "Sample Tenant",
            "total_requests": 543,
            "ocr_requests": 312,
            "qbo_requests": 231,
            "storage_used": "1.2 GB",
            "trend": 8.5,
        }
    ]

    try:
        # Get API usage by service - try original implementation if possible
        api_usage_by_service = []
        api_usage_by_tenant = []
        api_usage_over_time = []

        # If APIKeyUsage model is available, use it
        if "APIKeyUsage" in globals():
            api_usage_by_service = (
                db.session.query(
                    APIKeyUsage.service,
                    db.func.sum(APIKeyUsage.request_count).label("total_requests"),
                    db.func.sum(APIKeyUsage.token_count).label("total_tokens"),
                )
                .group_by(APIKeyUsage.service)
                .all()
            )

            # Get API usage by tenant
            api_usage_by_tenant = (
                db.session.query(
                    APIKeyUsage.tenant_id,
                    Tenant.name,
                    db.func.sum(APIKeyUsage.request_count).label("total_requests"),
                    db.func.sum(APIKeyUsage.token_count).label("total_tokens"),
                )
                .join(Tenant, APIKeyUsage.tenant_id == Tenant.id)
                .group_by(APIKeyUsage.tenant_id, Tenant.name)
                .order_by(db.func.sum(APIKeyUsage.request_count).desc())
                .limit(10)
                .all()
            )

            # Get API usage over time (last 30 days)
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            api_usage_over_time = (
                APIKeyUsage.query.filter(APIKeyUsage.updated_at >= thirty_days_ago)
                .order_by(APIKeyUsage.updated_at)
                .all()
            )
    except Exception as e:
        # If anything fails, use mock data
        logger.error(f"Error fetching API usage data: {str(e)}")

    return render_template(
        "superadmin/api_usage.html",
        usage_stats=usage_stats,
        top_tenants=top_tenants,
        api_usage_by_service=api_usage_by_service,
        api_usage_by_tenant=api_usage_by_tenant,
        api_usage_over_time=api_usage_over_time,
    )


# System health dashboard
@superadmin_required
def system_health_dashboard():
    # Mock services for demo
    services = [
        {
            "id": 1,
            "name": "Web Application",
            "description": "Main web application server",
            "status": "operational",
            "uptime": 99.98,
            "response_time": 92,
            "last_check": datetime.utcnow(),
            "endpoint": "https://api.example.com/health",
        },
        {
            "id": 2,
            "name": "Database Service",
            "description": "PostgreSQL database server",
            "status": "operational",
            "uptime": 99.95,
            "response_time": 45,
            "last_check": datetime.utcnow(),
            "endpoint": "postgres://db.example.com:5432",
        },
        {
            "id": 3,
            "name": "OCR API",
            "description": "Optical Character Recognition Service",
            "status": "operational",
            "uptime": 99.5,
            "response_time": 320,
            "last_check": datetime.utcnow(),
            "endpoint": "https://ocr.example.com/api/v1",
        },
        {
            "id": 4,
            "name": "QuickBooks API",
            "description": "Integration with QuickBooks Online",
            "status": "degraded",
            "uptime": 98.2,
            "response_time": 580,
            "last_check": datetime.utcnow(),
            "endpoint": "https://api.quickbooks.com/v3",
        },
    ]

    # Mock error logs for demo
    recent_errors = [
        {
            "id": 1,
            "timestamp": datetime.utcnow() - timedelta(hours=2),
            "service": "QuickBooks API",
            "type": "Connection Timeout",
            "message": "Connection to QuickBooks API timed out after 30 seconds",
            "count": 3,
            "first_seen": datetime.utcnow() - timedelta(hours=4),
            "stack_trace": "ConnectionError: Connection timed out after 30.0 seconds\n  at app/services/quickbooks.py:145\n  at app/controllers/invoice_controller.py:322",
        }
    ]

    try:
        # Try to get actual health logs if the model exists
        if "SystemHealthLog" in globals():
            health_logs = (
                SystemHealthLog.query.order_by(SystemHealthLog.timestamp.desc())
                .limit(100)
                .all()
            )

            # Group by service if logs exist
            if health_logs:
                service_data = {}
                for log in health_logs:
                    if log.service not in service_data:
                        service_data[log.service] = {
                            "id": len(service_data) + 1,
                            "name": log.service,
                            "description": log.description or log.service,
                            "status": log.status,
                            "uptime": 99.9,  # Placeholder
                            "response_time": log.response_time_ms,
                            "last_check": log.timestamp,
                            "endpoint": log.endpoint or "N/A",
                        }

                if service_data:
                    services = list(service_data.values())
    except Exception as e:
        # If anything fails, use mock data
        logger.error(f"Error fetching system health data: {str(e)}")

    return render_template(
        "superadmin/system_health.html", services=services, recent_errors=recent_errors
    )


# Add tenant
@superadmin_required
def add_tenant():
    """Add a new tenant by superadmin"""
    if request.method == "POST":
        try:
            # Create tenant
            tenant = Tenant(
                name=request.form.get("name"),
                is_active=True,
                default_currency=request.form.get("default_currency", "USD"),
                default_language=request.form.get("default_language", "en-US"),
                timezone=request.form.get("timezone", "UTC"),
                created_at=datetime.utcnow(),
            )
            db.session.add(tenant)
            db.session.flush()  # Get tenant ID without committing

            # Create tenant quota
            quota = TenantQuota(
                tenant_id=tenant.id,
                max_users=request.form.get("max_users", type=int, default=5),
                max_storage_mb=request.form.get(
                    "max_storage_mb", type=int, default=1024
                ),
                max_invoices_per_month=request.form.get(
                    "max_invoices_per_month", type=int, default=100
                ),
                max_api_calls_per_day=request.form.get(
                    "max_api_calls_per_day", type=int, default=1000
                ),
                plan_type=request.form.get("plan_type", "basic"),
                is_trial="is_trial" in request.form,
            )

            # Set trial expiration if is_trial
            if "is_trial" in request.form and request.form.get("trial_expires_at"):
                try:
                    quota.trial_expires_at = datetime.strptime(
                        request.form.get("trial_expires_at"), "%Y-%m-%d"
                    )
                except ValueError:
                    # Default to 30 days if date format is invalid
                    quota.trial_expires_at = datetime.utcnow() + timedelta(days=30)

            db.session.add(quota)
            db.session.commit()

            # Log activity
            record_activity(
                admin_id=g.superadmin.id,
                activity_type="tenant_created",
                details=json.dumps(
                    {"tenant_id": tenant.id, "tenant_name": tenant.name}
                ),
            )

            flash(f'Tenant "{tenant.name}" created successfully', "success")
            return redirect(url_for("superadmin.tenant_details", tenant_id=tenant.id))

        except IntegrityError as e:
            db.session.rollback()
            flash(f"Error creating tenant: {str(e)}", "danger")
        except Exception as e:
            db.session.rollback()
            flash(f"Unexpected error creating tenant: {str(e)}", "danger")

    # Shouldn't get here with POST request, redirect back to tenants list
    return redirect(url_for("superadmin.manage_tenants"))


# Create a superadmin account
def create_superadmin():
    # Check if any superadmin exists
    if SuperAdminUser.query.count() > 0:
        return jsonify({"error": "Superadmin account already exists"}), 400

    data = request.get_json()

    # Validate input
    required_fields = ["username", "email", "password", "first_name", "last_name"]
    for field in required_fields:
        if field not in data:
            return jsonify({"error": f"Missing required field: {field}"}), 400

    # Create admin account
    admin = SuperAdminUser(
        username=data["username"],
        email=data["email"],
        password_hash=generate_password_hash(data["password"]),
        first_name=data["first_name"],
        last_name=data["last_name"],
        api_key=str(uuid.uuid4()),
    )

    db.session.add(admin)
    db.session.commit()

    return jsonify({"message": "Superadmin account created successfully"}), 201


# Helper function to record admin activity
def record_activity(
    admin_id, activity_type, ip_address=None, user_agent=None, details=None
):
    """Record superadmin activity"""
    try:
        # Log to system log file
        logger.info(
            f"SuperAdmin activity: {activity_type} by admin_id={admin_id} from {ip_address}"
        )

        # In production, you would also store this in a database table
        # similar to UserActivity but for superadmins
    except Exception as e:
        logger.error(f"Error recording superadmin activity: {str(e)}")


# Delete tenant
@superadmin_required
def delete_tenant(tenant_id):
    """Delete a tenant by superadmin"""
    tenant = Tenant.query.get_or_404(tenant_id)

    if request.method == "POST":
        try:
            tenant_name = tenant.name

            # Delete all related data
            # Note: In a real application, you might want to:
            # 1. Archive the data instead of permanently deleting
            # 2. Implement a soft-delete mechanism
            # 3. Have a background task for deletion of large tenants

            # Delete all user sessions for this tenant
            # (In a real app, you'd implement this)

            # Log activity before deleting
            record_activity(
                admin_id=g.superadmin.id,
                activity_type="tenant_deleted",
                details=json.dumps(
                    {"tenant_id": tenant.id, "tenant_name": tenant.name}
                ),
            )

            # Delete tenant and all related records
            db.session.delete(tenant)  # This should cascade to related records
            db.session.commit()

            flash(f'Tenant "{tenant_name}" has been permanently deleted', "success")
        except Exception as e:
            db.session.rollback()
            flash(f"Error deleting tenant: {str(e)}", "danger")

    return redirect(url_for("superadmin.manage_tenants"))


# Update tenant
@superadmin_required
def update_tenant(tenant_id):
    """Update tenant details by superadmin"""
    tenant = Tenant.query.get_or_404(tenant_id)

    if request.method == "POST":
        try:
            # Update tenant details
            tenant.name = request.form.get("name", tenant.name)
            tenant.is_active = "is_active" in request.form
            tenant.default_currency = request.form.get(
                "default_currency", tenant.default_currency
            )
            tenant.default_language = request.form.get(
                "default_language", tenant.default_language
            )
            tenant.timezone = request.form.get("timezone", tenant.timezone)
            tenant.updated_at = datetime.utcnow()

            # Get or create tenant quota
            quota = TenantQuota.query.filter_by(tenant_id=tenant.id).first()
            if not quota:
                quota = TenantQuota(tenant_id=tenant.id)
                db.session.add(quota)

            # Update quota
            quota.max_users = request.form.get(
                "max_users", type=int, default=quota.max_users
            )
            quota.max_storage_mb = request.form.get(
                "max_storage_mb", type=int, default=quota.max_storage_mb
            )
            quota.max_invoices_per_month = request.form.get(
                "max_invoices_per_month", type=int, default=quota.max_invoices_per_month
            )
            quota.max_api_calls_per_day = request.form.get(
                "max_api_calls_per_day", type=int, default=quota.max_api_calls_per_day
            )
            quota.plan_type = request.form.get("plan_type", quota.plan_type)

            # Handle trial status
            is_trial = "is_trial" in request.form
            was_trial = quota.is_trial

            quota.is_trial = is_trial

            # Update trial expiration date if needed
            if is_trial and (not was_trial or request.form.get("trial_expires_at")):
                try:
                    quota.trial_expires_at = datetime.strptime(
                        request.form.get("trial_expires_at"), "%Y-%m-%d"
                    )
                except (ValueError, TypeError):
                    # If date is not provided or invalid, set to 30 days from now
                    quota.trial_expires_at = datetime.utcnow() + timedelta(days=30)

            db.session.commit()

            # Log activity
            record_activity(
                admin_id=g.superadmin.id,
                activity_type="tenant_updated",
                details=json.dumps(
                    {"tenant_id": tenant.id, "tenant_name": tenant.name}
                ),
            )

            flash(f'Tenant "{tenant.name}" updated successfully', "success")
        except Exception as e:
            db.session.rollback()
            flash(f"Error updating tenant: {str(e)}", "danger")

    return redirect(url_for("superadmin.tenant_details", tenant_id=tenant.id))


# User details page
@superadmin_required
def user_details(user_id):
    """View and manage user details"""
    user = User.query.get_or_404(user_id)

    # Get user activity logs
    activities = []  # In a real app, you'd get this from a user_activity table

    # Get user metrics and usage stats
    invoices_count = Invoice.query.filter_by(created_by=user.id).count()

    # Get recent invoices
    recent_invoices = (
        Invoice.query.filter_by(created_by=user.id)
        .order_by(Invoice.created_at.desc())
        .limit(5)
        .all()
    )

    return render_template(
        "superadmin/user_details.html",
        user=user,
        activities=activities,
        invoices_count=invoices_count,
        recent_invoices=recent_invoices,
    )
