import datetime

from flask import jsonify, request, session

from api.models.tenant import Tenant
from api.models.user import User
from app import db
from services.auth import verify_id_token


def login():
    """API endpoint for user login with Firebase token"""
    data = request.get_json()

    if not data or "idToken" not in data:
        return jsonify({"error": "ID token is required"}), 400

    # Verify the Firebase token
    id_token = data.get("idToken")
    decoded_token = verify_id_token(id_token)

    if not decoded_token:
        return jsonify({"error": "Invalid ID token"}), 401

    # Get user info from token
    firebase_uid = decoded_token.get("uid")
    email = decoded_token.get("email")
    name = decoded_token.get("name", "")

    if not firebase_uid or not email:
        return jsonify({"error": "Invalid token data"}), 401

    # Find or create user
    user = User.find_by_firebase_uid(firebase_uid)

    if not user:
        # Check if email exists
        user = User.find_by_email(email)

        if user:
            # Update existing user with Firebase UID
            user.firebase_uid = firebase_uid
            user.last_login = datetime.datetime.utcnow()
        else:
            # Create new user
            names = name.split(" ", 1) if name else [""]
            first_name = names[0]
            last_name = names[1] if len(names) > 1 else ""

            user = User(
                email=email,
                username=email.split("@")[0],
                firebase_uid=firebase_uid,
                first_name=first_name,
                last_name=last_name,
                last_login=datetime.datetime.utcnow(),
            )
            db.session.add(user)

        db.session.commit()

    # Store user in session
    session["user_id"] = user.id
    session["user_data"] = user.to_dict()

    # Return user data and available tenants
    tenants = Tenant.query.all()
    tenant_data = [tenant.to_dict() for tenant in tenants]

    return jsonify({"user": user.to_dict(), "tenants": tenant_data})


def logout():
    """API endpoint for user logout"""
    # Clear session
    session.pop("user_id", None)
    session.pop("user_data", None)
    session.pop("current_tenant_id", None)

    return jsonify({"message": "Logged out successfully"})


def get_current_user():
    """API endpoint to get current user data"""
    user_id = session.get("user_id")

    if not user_id:
        return jsonify({"error": "Not authenticated"}), 401

    user = User.query.get(user_id)

    if not user:
        # Clear invalid session
        session.pop("user_id", None)
        session.pop("user_data", None)
        return jsonify({"error": "User not found"}), 404

    return jsonify(user.to_dict())


def set_current_tenant():
    """API endpoint to set current tenant"""
    data = request.get_json()

    if not data or "tenantId" not in data:
        return jsonify({"error": "Tenant ID is required"}), 400

    tenant_id = data.get("tenantId")
    tenant = Tenant.query.get(tenant_id)

    if not tenant:
        return jsonify({"error": "Tenant not found"}), 404

    # Store in session
    session["current_tenant_id"] = tenant_id

    return jsonify({"message": "Tenant set successfully", "tenant": tenant.to_dict()})
