import hashlib
import imghdr
import os
import uuid
from datetime import datetime

from flask import current_app, g, jsonify, request
from werkzeug.utils import secure_filename

from api.models.invoice import Invoice, VATLine
from api.models.vendor import Vendor
from app import db, logger
from backend.dal.invoice import get_invoice_by_id
from backend.pydantic_models.invoice import InvoiceRead
from services.ocr import OCRService


# Helper functions
def allowed_file(filename):
    """Check if the file extension is allowed"""
    if not filename or "." not in filename:
        return False
    ext = filename.rsplit(".", 1)[1].lower()
    return ext in current_app.config["ALLOWED_EXTENSIONS"]


def validate_file_content(file_stream, filename):
    """Validate file content matches its extension"""
    # Reset file pointer
    file_stream.seek(0)

    # Get file extension
    ext = filename.rsplit(".", 1)[1].lower() if "." in filename else ""

    # Check image files with imghdr
    if ext in ["jpg", "jpeg", "png"]:
        file_header = file_stream.read(512)
        file_stream.seek(0)  # Reset pointer

        image_format = imghdr.what(None, file_header)
        if not image_format or image_format not in ["jpeg", "png"]:
            return False

    # For PDFs, check for the PDF header
    elif ext == "pdf":
        file_header = file_stream.read(5)
        file_stream.seek(0)  # Reset pointer

        if file_header != b"%PDF-":
            return False

    # SVG validation
    elif ext == "svg":
        file_header = file_stream.read(512).lower()
        file_stream.seek(0)  # Reset pointer

        if b"<svg" not in file_header:
            return False

    return True


def calculate_file_hash(file_data):
    """Calculate SHA256 hash of file data"""
    return hashlib.sha256(file_data).hexdigest()


def save_uploaded_file(file, tenant_id):
    """Save uploaded file and return file path"""
    # Validate file type
    if not allowed_file(file.filename):
        raise ValueError("File type not allowed")

    # Further validate file content
    if not validate_file_content(file, file.filename):
        raise ValueError("Invalid file content")

    # Create directory if it doesn't exist
    upload_dir = os.path.join(current_app.config["UPLOAD_FOLDER"], str(tenant_id))
    os.makedirs(upload_dir, exist_ok=True)

    # Generate unique filename with secure_filename to prevent path traversal
    original_filename = secure_filename(file.filename)
    ext = (
        original_filename.rsplit(".", 1)[1].lower() if "." in original_filename else ""
    )
    unique_filename = f"{uuid.uuid4().hex}.{ext}"

    # Save file
    try:
        file_path = os.path.join(upload_dir, unique_filename)
        file.save(file_path)

        # Verify file was saved correctly
        if not os.path.exists(file_path):
            raise ValueError("Failed to save file")

        # For development, use local file path
        file_url = f"/uploads/{tenant_id}/{unique_filename}"

        return file_path, file_url, original_filename
    except Exception as e:
        logger.error(f"Error saving file: {str(e)}")
        raise ValueError(f"Error saving file: {str(e)}")


# API endpoints
def upload_invoice():
    """API endpoint for invoice file upload"""
    tenant_id = g.tenant_id
    user_id = g.user["id"]

    # Check if file is in request
    if "file" not in request.files:
        return jsonify({"error": "No file part"}), 400

    file = request.files["file"]

    # Check if file is selected
    if file.filename == "":
        return jsonify({"error": "No file selected"}), 400

    # Check file type
    if not allowed_file(file.filename):
        return jsonify({"error": "File type not allowed"}), 400

    # Calculate file hash to check for duplicates
    file_data = file.read()
    file.seek(0)  # Reset file pointer after reading
    file_hash = calculate_file_hash(file_data)

    # Check for duplicate invoice
    duplicate = Invoice.find_duplicate(tenant_id, file_hash)
    if duplicate:
        return (
            jsonify(
                {
                    "error": "Duplicate invoice",
                    "duplicateId": duplicate.id,
                    "message": "This invoice has already been uploaded.",
                }
            ),
            400,
        )

    # Save file
    file_path, file_url, original_filename = save_uploaded_file(file, tenant_id)

    # Create invoice record
    invoice = Invoice(
        tenant_id=tenant_id,
        created_by=user_id,
        file_url=file_url,
        file_name=original_filename,
        file_type=file.content_type,
        file_size=len(file_data),
        sha256=file_hash,
        status="PENDING",
    )

    db.session.add(invoice)
    db.session.commit()

    # Start OCR processing (would be async in production)
    try:
        process_invoice(invoice.id)
    except Exception as e:
        current_app.logger.error(f"Error starting OCR processing: {str(e)}")

    return jsonify(invoice.to_dict()), 201


def get_invoices():
    """API endpoint to list invoices"""
    tenant_id = g.tenant_id

    # Parse query parameters
    status = request.args.get("status")
    vendor_id = request.args.get("vendorId")
    page = int(request.args.get("page", 1))
    per_page = min(int(request.args.get("perPage", 20)), 100)

    # Build query
    query = Invoice.query.filter_by(tenant_id=tenant_id)

    if status:
        query = query.filter_by(status=status)

    if vendor_id:
        query = query.filter_by(vendor_id=vendor_id)

    # Order by creation date, newest first
    query = query.order_by(Invoice.created_at.desc())

    # Paginate results
    pagination = query.paginate(page=page, per_page=per_page)

    # Format response
    invoices = [invoice.to_dict() for invoice in pagination.items]

    return jsonify(
        {
            "invoices": invoices,
            "pagination": {
                "page": pagination.page,
                "perPage": pagination.per_page,
                "total": pagination.total,
                "pages": pagination.pages,
            },
        }
    )


def get_invoice(invoice_id):
    """API endpoint to get a specific invoice (refactored)"""
    tenant_id = g.tenant_id
    invoice = get_invoice_by_id(invoice_id)
    if not invoice or invoice.tenant_id != tenant_id:
        return jsonify({"error": "Invoice not found"}), 404
    # Validate response with Pydantic
    try:
        invoice_data = InvoiceRead(
            id=str(invoice.id),
            vendor=invoice.vendor_id,  # Adjust as needed
            amount=invoice.total_amount,
            status=invoice.status,
            created_at=invoice.created_at,
        )
        return jsonify(invoice_data.dict())
    except Exception as e:
        return jsonify({"error": f"Invalid invoice data: {str(e)}"}), 500


def update_invoice(invoice_id):
    """API endpoint to update invoice data (refactored)"""
    tenant_id = g.tenant_id
    invoice = get_invoice_by_id(invoice_id)
    if not invoice or invoice.tenant_id != tenant_id:
        return jsonify({"error": "Invoice not found"}), 404
    data = request.get_json()
    if not data:
        return jsonify({"error": "No data provided"}), 400
    try:
        update_data = InvoiceUpdate(**data)
    except Exception as e:
        return jsonify({"error": f"Invalid input: {str(e)}"}), 400
    # Use DAL to update invoice
    try:
        updated_invoice = dal_update_invoice(invoice, update_data)
        return jsonify(updated_invoice.to_dict())
    except Exception as e:
        return jsonify({"error": f"Failed to update invoice: {str(e)}"}), 500


def delete_invoice(invoice_id):
    """API endpoint to delete an invoice"""
    tenant_id = g.tenant_id

    invoice = Invoice.query.filter_by(id=invoice_id, tenant_id=tenant_id).first()

    if not invoice:
        return jsonify({"error": "Invoice not found"}), 404

    # Delete file if exists
    file_path = os.path.join(
        current_app.config["UPLOAD_FOLDER"],
        str(tenant_id),
        invoice.file_url.split("/")[-1],
    )
    if os.path.exists(file_path):
        os.remove(file_path)

    # Delete invoice and associated VAT lines
    db.session.delete(invoice)
    db.session.commit()

    return jsonify({"message": "Invoice deleted successfully"})


def process_invoice(invoice_id):
    """Process invoice with OCR (would be async in production)"""
    invoice = Invoice.query.get(invoice_id)

    if not invoice:
        return False

    # Update status
    invoice.status = "PROCESSING"
    db.session.commit()

    try:
        # Get file path
        file_path = os.path.join(
            current_app.config["UPLOAD_FOLDER"],
            str(invoice.tenant_id),
            invoice.file_url.split("/")[-1],
        )

        # Read file content
        with open(file_path, "rb") as f:
            file_content = f.read()

        # Process with OCR
        ocr_result = OCRService.process_image(file_content)

        # Extract structured data
        structured_data = ocr_result["structured_data"]

        # Update invoice with extracted data
        invoice.ocr_raw = str(ocr_result)
        invoice.ocr_confidence = ocr_result["confidence"]
        invoice.status = "READY"
        invoice.processed_at = datetime.utcnow()

        # Set extracted fields
        if structured_data.get("invoice_date"):
            try:
                invoice.invoice_date = datetime.strptime(
                    structured_data["invoice_date"], "%d/%m/%Y"
                )
            except (ValueError, TypeError):
                pass

        invoice.invoice_number = structured_data.get("invoice_number")
        invoice.total_amount = structured_data.get("total_amount")

        # Check if vendor exists
        vendor_name = structured_data.get("vendor_name")
        if vendor_name:
            vendor = Vendor.find_by_name(invoice.tenant_id, vendor_name)
            if vendor:
                invoice.vendor_id = vendor.id
            else:
                invoice.new_vendor_name = vendor_name

        # Add VAT lines
        for vat_line in structured_data.get("vat_lines", []):
            new_vat_line = VATLine(
                invoice_id=invoice.id,
                amount=vat_line["amount"],
                tax_code=vat_line["tax_code"],
                tax_rate=vat_line.get("tax_rate"),
            )
            db.session.add(new_vat_line)

        db.session.commit()
        return True

    except Exception as e:
        current_app.logger.error(f"Error processing invoice {invoice_id}: {str(e)}")

        # Update invoice status to ERROR
        invoice.status = "ERROR"
        invoice.error_message = str(e)
        db.session.commit()

        return False
