from datetime import datetime

from flask import g, jsonify, request

from api.models.invoice import Invoice
from api.models.vendor import Vendor
from app import db


def get_vendors():
    """API endpoint to list vendors"""
    tenant_id = g.tenant_id

    # Parse query parameters
    name_query = request.args.get("name")
    page = int(request.args.get("page", 1))
    per_page = min(int(request.args.get("perPage", 20)), 100)

    # Build query
    query = Vendor.query.filter_by(tenant_id=tenant_id)

    if name_query:
        query = query.filter(Vendor.name.ilike(f"%{name_query}%"))

    # Order by name
    query = query.order_by(Vendor.name)

    # Paginate results
    pagination = query.paginate(page=page, per_page=per_page)

    # Format response
    vendors = [vendor.to_dict() for vendor in pagination.items]

    return jsonify(
        {
            "vendors": vendors,
            "pagination": {
                "page": pagination.page,
                "perPage": pagination.per_page,
                "total": pagination.total,
                "pages": pagination.pages,
            },
        }
    )


def create_vendor():
    """API endpoint to create a new vendor"""
    tenant_id = g.tenant_id
    user_id = g.user["id"]
    data = request.get_json()

    # Validate required fields
    if not data or "name" not in data:
        return jsonify({"error": "Vendor name is required"}), 400

    # Check if vendor with same name already exists
    existing_vendor = Vendor.find_by_name(tenant_id, data["name"])
    if existing_vendor:
        return (
            jsonify(
                {"error": "Vendor already exists", "existingId": existing_vendor.id}
            ),
            400,
        )

    # Create new vendor
    vendor = Vendor(
        tenant_id=tenant_id,
        created_by=user_id,
        name=data["name"],
        email=data.get("email"),
        phone=data.get("phone"),
        address=data.get("address"),
        website=data.get("website"),
        default_account=data.get("defaultAccount"),
        default_tax_code=data.get("defaultTaxCode"),
        qbo_vendor_id=data.get("qboVendorId"),
    )

    db.session.add(vendor)
    db.session.commit()

    # Update invoices with new vendor name
    if vendor.name:
        # Find invoices with matching new_vendor_name
        invoices = Invoice.query.filter_by(
            tenant_id=tenant_id, new_vendor_name=vendor.name, vendor_id=None
        ).all()

        # Update invoices with new vendor
        for invoice in invoices:
            invoice.vendor_id = vendor.id
            invoice.new_vendor_name = None
            invoice.updated_at = datetime.utcnow()

        if invoices:
            db.session.commit()

    return jsonify(vendor.to_dict()), 201


def get_vendor(vendor_id):
    """API endpoint to get a specific vendor"""
    tenant_id = g.tenant_id

    vendor = Vendor.query.filter_by(id=vendor_id, tenant_id=tenant_id).first()

    if not vendor:
        return jsonify({"error": "Vendor not found"}), 404

    return jsonify(vendor.to_dict())


def update_vendor(vendor_id):
    """API endpoint to update vendor data"""
    tenant_id = g.tenant_id
    data = request.get_json()

    vendor = Vendor.query.filter_by(id=vendor_id, tenant_id=tenant_id).first()

    if not vendor:
        return jsonify({"error": "Vendor not found"}), 404

    # Update fields
    if "name" in data:
        # Check if new name conflicts with existing vendor
        if data["name"] != vendor.name:
            existing_vendor = Vendor.find_by_name(tenant_id, data["name"])
            if existing_vendor and existing_vendor.id != vendor_id:
                return (
                    jsonify(
                        {
                            "error": "Vendor with this name already exists",
                            "existingId": existing_vendor.id,
                        }
                    ),
                    400,
                )
        vendor.name = data["name"]

    if "email" in data:
        vendor.email = data["email"]

    if "phone" in data:
        vendor.phone = data["phone"]

    if "address" in data:
        vendor.address = data["address"]

    if "website" in data:
        vendor.website = data["website"]

    if "defaultAccount" in data:
        vendor.default_account = data["defaultAccount"]

    if "defaultTaxCode" in data:
        vendor.default_tax_code = data["defaultTaxCode"]

    if "qboVendorId" in data:
        vendor.qbo_vendor_id = data["qboVendorId"]

    vendor.updated_at = datetime.utcnow()
    db.session.commit()

    return jsonify(vendor.to_dict())


def delete_vendor(vendor_id):
    """API endpoint to delete a vendor"""
    tenant_id = g.tenant_id

    vendor = Vendor.query.filter_by(id=vendor_id, tenant_id=tenant_id).first()

    if not vendor:
        return jsonify({"error": "Vendor not found"}), 404

    # Check for invoices using this vendor
    invoices_count = Invoice.query.filter_by(vendor_id=vendor_id).count()

    if invoices_count > 0:
        return (
            jsonify(
                {
                    "error": "Cannot delete vendor with associated invoices",
                    "invoicesCount": invoices_count,
                }
            ),
            400,
        )

    # Delete vendor
    db.session.delete(vendor)
    db.session.commit()

    return jsonify({"message": "Vendor deleted successfully"})
