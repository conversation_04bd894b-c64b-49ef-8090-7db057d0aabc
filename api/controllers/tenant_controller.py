from datetime import datetime

from flask import g, jsonify, request

from api.models.invoice import Invoice
from api.models.tenant import Tenant
from api.models.user import User
from api.models.vendor import Vendor
from app import db


def get_tenants():
    """API endpoint to list tenants (admin only)"""
    user = User.query.get(g.user["id"])

    # Check if user is admin
    if not user.is_admin:
        return jsonify({"error": "Unauthorized access"}), 403

    # Parse query parameters
    name_query = request.args.get("name")
    page = int(request.args.get("page", 1))
    per_page = min(int(request.args.get("perPage", 20)), 100)

    # Build query
    query = Tenant.query

    if name_query:
        query = query.filter(Tenant.name.ilike(f"%{name_query}%"))

    # Order by name
    query = query.order_by(Tenant.name)

    # Paginate results
    pagination = query.paginate(page=page, per_page=per_page)

    # Format response
    tenants = [tenant.to_dict() for tenant in pagination.items]

    return jsonify(
        {
            "tenants": tenants,
            "pagination": {
                "page": pagination.page,
                "perPage": pagination.per_page,
                "total": pagination.total,
                "pages": pagination.pages,
            },
        }
    )


def create_tenant():
    """API endpoint to create a new tenant (admin only)"""
    user = User.query.get(g.user["id"])

    # Check if user is admin
    if not user.is_admin:
        return jsonify({"error": "Unauthorized access"}), 403

    data = request.get_json()

    # Validate required fields
    if not data or "name" not in data:
        return jsonify({"error": "Tenant name is required"}), 400

    # Create new tenant
    tenant = Tenant(
        name=data["name"],
        logo_url=data.get("logoUrl"),
        default_currency=data.get("defaultCurrency", "USD"),
        default_language=data.get("defaultLanguage", "en-US"),
        timezone=data.get("timezone", "UTC"),
    )

    # Add QuickBooks data if provided
    if data.get("qboRealmId"):
        tenant.qbo_realm_id = data["qboRealmId"]

    if data.get("qboTokens"):
        qbo_tokens = data["qboTokens"]
        tenant.qbo_access_token = qbo_tokens.get("accessToken")
        tenant.qbo_refresh_token = qbo_tokens.get("refreshToken")

        # Parse expiry date if provided
        if qbo_tokens.get("expiresAt"):
            try:
                tenant.qbo_token_expires_at = datetime.fromisoformat(
                    qbo_tokens["expiresAt"]
                )
            except (ValueError, TypeError):
                pass

    db.session.add(tenant)
    db.session.commit()

    return jsonify(tenant.to_dict()), 201


def get_tenant(tenant_id):
    """API endpoint to get a specific tenant"""
    # Either user is admin or tenant is associated with user
    user = User.query.get(g.user["id"])

    tenant = Tenant.query.get(tenant_id)

    if not tenant:
        return jsonify({"error": "Tenant not found"}), 404

    # Check access rights
    if not user.is_admin and user.tenant_id != tenant.id:
        return jsonify({"error": "Unauthorized access"}), 403

    return jsonify(tenant.to_dict())


def update_tenant(tenant_id):
    """API endpoint to update tenant data (admin only)"""
    user = User.query.get(g.user["id"])

    # Check if user is admin
    if not user.is_admin:
        return jsonify({"error": "Unauthorized access"}), 403

    data = request.get_json()
    tenant = Tenant.query.get(tenant_id)

    if not tenant:
        return jsonify({"error": "Tenant not found"}), 404

    # Update fields
    if "name" in data:
        tenant.name = data["name"]

    if "logoUrl" in data:
        tenant.logo_url = data["logoUrl"]

    if "defaultCurrency" in data:
        tenant.default_currency = data["defaultCurrency"]

    if "defaultLanguage" in data:
        tenant.default_language = data["defaultLanguage"]

    if "timezone" in data:
        tenant.timezone = data["timezone"]

    # Update QuickBooks data if provided
    if "qboRealmId" in data:
        tenant.qbo_realm_id = data["qboRealmId"]

    if "qboTokens" in data:
        qbo_tokens = data["qboTokens"]
        if "accessToken" in qbo_tokens:
            tenant.qbo_access_token = qbo_tokens["accessToken"]

        if "refreshToken" in qbo_tokens:
            tenant.qbo_refresh_token = qbo_tokens["refreshToken"]

        # Parse expiry date if provided
        if "expiresAt" in qbo_tokens:
            try:
                tenant.qbo_token_expires_at = datetime.fromisoformat(
                    qbo_tokens["expiresAt"]
                )
            except (ValueError, TypeError):
                pass

    tenant.updated_at = datetime.utcnow()
    db.session.commit()

    return jsonify(tenant.to_dict())


def delete_tenant(tenant_id):
    """API endpoint to delete a tenant (admin only)"""
    user = User.query.get(g.user["id"])

    # Check if user is admin
    if not user.is_admin:
        return jsonify({"error": "Unauthorized access"}), 403

    tenant = Tenant.query.get(tenant_id)

    if not tenant:
        return jsonify({"error": "Tenant not found"}), 404

    # Check for resources tied to this tenant
    users_count = User.query.filter_by(tenant_id=tenant_id).count()
    vendors_count = Vendor.query.filter_by(tenant_id=tenant_id).count()
    invoices_count = Invoice.query.filter_by(tenant_id=tenant_id).count()

    if users_count > 0 or vendors_count > 0 or invoices_count > 0:
        return (
            jsonify(
                {
                    "error": "Cannot delete tenant with associated resources",
                    "usersCount": users_count,
                    "vendorsCount": vendors_count,
                    "invoicesCount": invoices_count,
                }
            ),
            400,
        )

    # Delete tenant
    db.session.delete(tenant)
    db.session.commit()

    return jsonify({"message": "Tenant deleted successfully"})
