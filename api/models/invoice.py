from datetime import datetime

from app import db


class VATLine(db.Model):
    """VAT line model for invoice tax details"""

    id = db.Column(db.Integer, primary_key=True)
    amount = db.Column(db.Float, nullable=False)
    tax_code = db.Column(db.String(64), nullable=False)
    tax_rate = db.Column(db.Float, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Foreign keys
    invoice_id = db.Column(db.Integer, db.<PERSON>ey("invoice.id"), nullable=False)

    def __repr__(self):
        return f"<VATLine {self.amount} - {self.tax_code}>"

    def to_dict(self):
        """Convert VAT line to dictionary for API responses"""
        return {
            "id": self.id,
            "amount": self.amount,
            "taxCode": self.tax_code,
            "taxRate": self.tax_rate,
        }


class Invoice(db.Model):
    """Invoice model for OCR processing"""

    id = db.Column(db.Integer, primary_key=True)
    file_url = db.Column(db.String(512), nullable=False)
    file_name = db.Column(db.String(256), nullable=True)
    file_type = db.Column(db.String(64), nullable=True)
    file_size = db.Column(db.Integer, nullable=True)
    sha256 = db.Column(db.String(64), nullable=True)

    # Status tracking
    status = db.Column(
        db.String(32), default="PENDING"
    )  # PENDING, PROCESSING, READY, PUSHED, ERROR
    error_message = db.Column(db.Text, nullable=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(
        db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )
    processed_at = db.Column(db.DateTime, nullable=True)
    pushed_at = db.Column(db.DateTime, nullable=True)

    # QuickBooks integration
    qbo_txn_id = db.Column(db.String(128), nullable=True)

    # Invoice fields from OCR extraction
    invoice_date = db.Column(db.Date, nullable=True)
    invoice_number = db.Column(db.String(64), nullable=True)
    memo = db.Column(db.Text, nullable=True)
    validated = db.Column(db.Boolean, default=False)
    total_amount = db.Column(db.Float, nullable=True)
    currency = db.Column(db.String(3), default="USD")
    new_vendor_name = db.Column(
        db.String(128), nullable=True
    )  # Detected but not matched vendor

    # OCR processing data
    ocr_raw = db.Column(db.Text, nullable=True)  # JSON string of raw OCR results
    ocr_confidence = db.Column(db.Float, nullable=True)

    # Foreign keys
    tenant_id = db.Column(db.Integer, db.ForeignKey("tenant.id"), nullable=False)
    vendor_id = db.Column(db.Integer, db.ForeignKey("vendor.id"), nullable=True)
    created_by = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=True)

    # Relationships
    vat_lines = db.relationship(
        "VATLine", backref="invoice", lazy=True, cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<Invoice {self.invoice_number or self.id}>"

    @classmethod
    def find_duplicate(cls, tenant_id, sha256):
        """Find a duplicate invoice by SHA256 hash"""
        if not sha256:
            return None
        return cls.query.filter_by(tenant_id=tenant_id, sha256=sha256).first()

    def to_dict(self):
        """Convert invoice to dictionary for API responses"""
        return {
            "id": self.id,
            "fileUrl": self.file_url,
            "fileName": self.file_name,
            "fileType": self.file_type,
            "fileSize": self.file_size,
            "sha256": self.sha256,
            "status": self.status,
            "errorMessage": self.error_message,
            "invoiceDate": self.invoice_date.isoformat() if self.invoice_date else None,
            "invoiceNumber": self.invoice_number,
            "memo": self.memo,
            "validated": self.validated,
            "totalAmount": self.total_amount,
            "currency": self.currency,
            "newVendorName": self.new_vendor_name,
            "ocrConfidence": self.ocr_confidence,
            "tenantId": self.tenant_id,
            "vendorId": self.vendor_id,
            "qboTxnId": self.qbo_txn_id,
            "vatLines": [vat_line.to_dict() for vat_line in self.vat_lines],
            "createdAt": self.created_at.isoformat() if self.created_at else None,
            "updatedAt": self.updated_at.isoformat() if self.updated_at else None,
            "processedAt": self.processed_at.isoformat() if self.processed_at else None,
            "pushedAt": self.pushed_at.isoformat() if self.pushed_at else None,
        }
