from datetime import datetime

from app import db


class Vendor(db.Model):
    """Vendor model for invoices"""

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(
        db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )

    # QuickBooks Online integration
    default_account = db.Column(db.String(128), nullable=True)
    default_tax_code = db.Column(db.String(128), nullable=True)
    qbo_vendor_id = db.Column(db.String(128), nullable=True)

    # Contact information
    email = db.Column(db.String(128), nullable=True)
    phone = db.Column(db.String(32), nullable=True)
    address = db.Column(db.String(256), nullable=True)
    website = db.Column(db.String(128), nullable=True)

    # Foreign keys
    tenant_id = db.Column(db.Integer, db.<PERSON>ey("tenant.id"), nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=True)

    # Relationships
    invoices = db.relationship("Invoice", backref="vendor", lazy=True)

    def __repr__(self):
        return f"<Vendor {self.name}>"

    @classmethod
    def find_by_name(cls, tenant_id, name):
        """Find a vendor by name (case-insensitive)"""
        return cls.query.filter(
            cls.tenant_id == tenant_id, db.func.lower(cls.name) == db.func.lower(name)
        ).first()

    def to_dict(self):
        """Convert vendor to dictionary for API responses"""
        return {
            "id": self.id,
            "name": self.name,
            "email": self.email,
            "phone": self.phone,
            "address": self.address,
            "website": self.website,
            "defaultAccount": self.default_account,
            "defaultTaxCode": self.default_tax_code,
            "qboVendorId": self.qbo_vendor_id,
            "tenantId": self.tenant_id,
            "createdAt": self.created_at.isoformat() if self.created_at else None,
            "updatedAt": self.updated_at.isoformat() if self.updated_at else None,
        }
