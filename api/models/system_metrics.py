import uuid
from datetime import datetime

from app import db


class UserActivity(db.Model):
    """Model for tracking user activities"""

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>("user.id"), nullable=False)
    tenant_id = db.Column(db.Integer, db.<PERSON>ey("tenant.id"), nullable=False)
    activity_type = db.Column(
        db.String(64), nullable=False
    )  # login, logout, upload, process, etc.
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    ip_address = db.Column(db.String(45), nullable=True)  # IPv6 can be up to 45 chars
    user_agent = db.Column(db.String(256), nullable=True)
    country_code = db.Column(db.String(2), nullable=True)
    region = db.Column(db.String(64), nullable=True)
    city = db.Column(db.String(64), nullable=True)
    details = db.Column(db.Text, nullable=True)  # JSON string with additional context

    # Relationships
    user = db.relationship("User", backref=db.backref("activities", lazy=True))
    tenant = db.relationship("Tenant", backref=db.backref("user_activities", lazy=True))

    def __repr__(self):
        return f"<UserActivity {self.id} {self.activity_type}>"

    def to_dict(self):
        """Convert activity to dictionary for API responses"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "tenant_id": self.tenant_id,
            "activity_type": self.activity_type,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "country_code": self.country_code,
            "region": self.region,
            "city": self.city,
            "details": self.details,
        }


class APIKeyUsage(db.Model):
    """Model for tracking API key usage"""

    id = db.Column(db.Integer, primary_key=True)
    tenant_id = db.Column(db.Integer, db.ForeignKey("tenant.id"), nullable=False)
    api_key = db.Column(db.String(36), nullable=False, index=True)
    service = db.Column(
        db.String(64), nullable=False
    )  # gemini, tesseract, firebase, etc.
    endpoint = db.Column(db.String(128), nullable=False)
    request_count = db.Column(db.Integer, default=0)
    token_count = db.Column(db.Integer, default=0)  # For LLM APIs like Gemini
    last_used = db.Column(db.DateTime, default=datetime.utcnow)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(
        db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )

    # Relationships
    tenant = db.relationship("Tenant", backref=db.backref("api_usage", lazy=True))

    def __repr__(self):
        return f"<APIKeyUsage {self.id} {self.service}>"

    def to_dict(self):
        """Convert API usage to dictionary for API responses"""
        return {
            "id": self.id,
            "tenant_id": self.tenant_id,
            "api_key": self.api_key,
            "service": self.service,
            "endpoint": self.endpoint,
            "request_count": self.request_count,
            "token_count": self.token_count,
            "last_used": self.last_used.isoformat() if self.last_used else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class TenantQuota(db.Model):
    """Model for tracking tenant quotas"""

    id = db.Column(db.Integer, primary_key=True)
    tenant_id = db.Column(
        db.Integer, db.ForeignKey("tenant.id"), nullable=False, unique=True
    )
    max_users = db.Column(db.Integer, default=5)
    max_storage_mb = db.Column(db.Integer, default=1024)  # 1GB default
    max_invoices_per_month = db.Column(db.Integer, default=100)
    max_api_calls_per_day = db.Column(db.Integer, default=1000)
    current_storage_used_mb = db.Column(db.Float, default=0)
    current_invoices_this_month = db.Column(db.Integer, default=0)
    current_api_calls_today = db.Column(db.Integer, default=0)
    plan_type = db.Column(db.String(32), default="basic")  # basic, premium, enterprise
    is_trial = db.Column(db.Boolean, default=True)
    trial_expires_at = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(
        db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )

    # Relationships
    tenant = db.relationship("Tenant", backref=db.backref("quota", uselist=False))

    def __repr__(self):
        return f"<TenantQuota {self.id} {self.plan_type}>"

    def to_dict(self):
        """Convert quota to dictionary for API responses"""
        return {
            "id": self.id,
            "tenant_id": self.tenant_id,
            "max_users": self.max_users,
            "max_storage_mb": self.max_storage_mb,
            "max_invoices_per_month": self.max_invoices_per_month,
            "max_api_calls_per_day": self.max_api_calls_per_day,
            "current_storage_used_mb": self.current_storage_used_mb,
            "current_invoices_this_month": self.current_invoices_this_month,
            "current_api_calls_today": self.current_api_calls_today,
            "plan_type": self.plan_type,
            "is_trial": self.is_trial,
            "trial_expires_at": (
                self.trial_expires_at.isoformat() if self.trial_expires_at else None
            ),
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class SuperAdminUser(db.Model):
    """Model for superadmin users"""

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(256), nullable=False)
    first_name = db.Column(db.String(64), nullable=True)
    last_name = db.Column(db.String(64), nullable=True)
    last_login = db.Column(db.DateTime, nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(
        db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )
    api_key = db.Column(db.String(36), default=lambda: str(uuid.uuid4()), unique=True)

    def __repr__(self):
        return f"<SuperAdminUser {self.username}>"

    def to_dict(self):
        """Convert admin user to dictionary for API responses"""
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class SystemSetting(db.Model):
    """Model for system-wide settings"""

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(64), unique=True, nullable=False)
    value = db.Column(db.Text, nullable=True)
    description = db.Column(db.String(256), nullable=True)
    is_protected = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(
        db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )
    updated_by = db.Column(
        db.Integer, db.ForeignKey("super_admin_user.id"), nullable=True
    )

    def __repr__(self):
        return f"<SystemSetting {self.key}>"

    def to_dict(self):
        """Convert setting to dictionary for API responses"""
        return {
            "id": self.id,
            "key": self.key,
            "value": self.value if not self.is_protected else "********",
            "description": self.description,
            "is_protected": self.is_protected,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "updated_by": self.updated_by,
        }


class SystemHealthLog(db.Model):
    """Model for system health logs"""

    id = db.Column(db.Integer, primary_key=True)
    service = db.Column(db.String(64), nullable=False)
    status = db.Column(db.String(32), nullable=False)  # up, down, degraded
    response_time_ms = db.Column(db.Integer, nullable=True)
    message = db.Column(db.Text, nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f"<SystemHealthLog {self.id} {self.service} {self.status}>"

    def to_dict(self):
        """Convert health log to dictionary for API responses"""
        return {
            "id": self.id,
            "service": self.service,
            "status": self.status,
            "response_time_ms": self.response_time_ms,
            "message": self.message,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
        }
