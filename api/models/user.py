from datetime import datetime

from flask_login import UserMixin

from app import db


class User(UserMixin, db.Model):
    """User model for authentication and access control"""

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    firebase_uid = db.Column(db.String(128), unique=True, nullable=True)
    password_hash = db.Column(db.String(256), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(
        db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )

    # Tenant association
    tenant_id = db.Column(db.Integer, db.ForeignKey("tenant.id"), nullable=True)
    roles = db.Column(db.String(256), default="user")  # comma-separated roles

    # Personal information
    first_name = db.Column(db.String(64), nullable=True)
    last_name = db.Column(db.String(64), nullable=True)
    avatar_url = db.Column(db.String(256), nullable=True)
    last_login = db.Column(db.DateTime, nullable=True)
    is_active = db.Column(db.Boolean, default=True)

    def __repr__(self):
        return f"<User {self.username}>"

    @property
    def is_admin(self):
        """Check if user has admin role"""
        return "admin" in self.roles.split(",")

    @property
    def full_name(self):
        """Get user's full name or username if not available"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        else:
            return self.username

    @classmethod
    def find_by_email(cls, email):
        """Find a user by email"""
        return cls.query.filter_by(email=email).first()

    @classmethod
    def find_by_firebase_uid(cls, firebase_uid):
        """Find a user by Firebase UID"""
        return cls.query.filter_by(firebase_uid=firebase_uid).first()

    def to_dict(self):
        """Convert user to dictionary for API responses"""
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "firstName": self.first_name,
            "lastName": self.last_name,
            "fullName": self.full_name,
            "avatarUrl": self.avatar_url,
            "tenantId": self.tenant_id,
            "roles": self.roles.split(","),
            "isAdmin": self.is_admin,
            "lastLogin": self.last_login.isoformat() if self.last_login else None,
            "createdAt": self.created_at.isoformat() if self.created_at else None,
        }
