from datetime import datetime

from app import db


class Tenant(db.Model):
    """Tenant model for multi-tenant application"""

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(
        db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )

    # QuickBooks Online integration
    qbo_realm_id = db.Column(db.String(128), nullable=True)
    qbo_access_token = db.Column(db.String(1024), nullable=True)
    qbo_refresh_token = db.Column(db.String(1024), nullable=True)
    qbo_token_expires_at = db.Column(db.DateTime, nullable=True)

    # Tenant preferences
    logo_url = db.Column(db.String(256), nullable=True)
    default_currency = db.Column(db.String(3), default="USD")
    default_language = db.Column(db.String(5), default="en-US")
    timezone = db.Column(db.String(64), default="UTC")

    # Relationships
    users = db.relationship("User", backref="tenant", lazy=True)
    vendors = db.relationship("Vendor", backref="tenant", lazy=True)
    invoices = db.relationship("Invoice", backref="tenant", lazy=True)

    def __repr__(self):
        return f"<Tenant {self.name}>"

    @property
    def qbo_connected(self):
        """Check if QuickBooks Online is connected"""
        return bool(self.qbo_realm_id and self.qbo_access_token)

    @property
    def qbo_token_expired(self):
        """Check if QuickBooks Online token is expired"""
        if not self.qbo_token_expires_at:
            return True
        return datetime.utcnow() > self.qbo_token_expires_at

    def to_dict(self):
        """Convert tenant to dictionary for API responses"""
        return {
            "id": self.id,
            "name": self.name,
            "logoUrl": self.logo_url,
            "defaultCurrency": self.default_currency,
            "defaultLanguage": self.default_language,
            "timezone": self.timezone,
            "createdAt": self.created_at.isoformat() if self.created_at else None,
            "updatedAt": self.updated_at.isoformat() if self.updated_at else None,
            "qboConnected": self.qbo_connected,
        }
