# AiClearBill (Billsnapp) &nbsp;🧾⚡

Production-ready, multi-tenant invoice automation platform that combines **tiered AI-powered OCR**, **robust accounting connectors**, and **enterprise-grade RBAC** to eliminate manual data entry and streamline bookkeeping.

---

## 1&nbsp;·&nbsp;Why AiClearBill?

| Capability | Description |
| --- | --- |
| Tiered OCR Pipeline | Three-layer extraction (Gemma → Gemini → Document AI) delivers 99 %+ field accuracy while controlling costs. |
| Accounting Integrations | Out-of-the-box QuickBooks Online connector with secure token storage, bulk push, webhooks, and delta sync. |
| Multi-Tenant & RBAC | Fine-grained roles (`<PERSON><PERSON><PERSON>`, `Checker`, `Builder-in-Chief`, …) and Firestore security rules protect every tenant. |
| Offline-First PWA | React + Vite frontend works on desktop & mobile (installable PWA, IndexedDB caching, service-worker sync). |
| Cloud-Native | Firebase Auth, Firestore, MongoDB Atlas, Google Cloud Run, Pub/Sub, Cloud Tasks, KMS encryption. |

---

## 2&nbsp;·&nbsp;Quick Start (Developer)

```bash
# 1. <PERSON><PERSON> & enter the repo
git clone https://github.com/Jpkay/AiClearBill.git
cd AiClearBill

# 2. One-shot environment bootstrap (installs pnpm deps, builds shared pkgs, creates .env files, downloads TF models…)
./setup.sh

# 3. Launch all apps in watch mode
pnpm dev       # turbo runs apps/api and apps/web concurrently

# 4. Run tests & type-checks
pnpm test      # jest / vitest
pnpm lint      # eslint + prettier
pnpm type-check
```

> **Prerequisites**  
> Node 18 +, pnpm 8 +, Docker (for optional `docker-compose up`), and a Firebase project for local emulators.

---

## 3&nbsp;·&nbsp;Monorepo Layout

```
.
├── apps/
│   ├── api/        # TypeScript tRPC API  ➜  see apps/api/README.md
│   └── web/        # React PWA frontend  ➜  see apps/web (frontend/README.md)
├── backend/        # Legacy Python FastAPI + Celery workers
├── packages/
│   ├── shared-types  # DTOs & domain models
│   └── eslint-config # Shared lint rules
├── docs/           # Feature & integration specs (see links below)
├── scripts/        # Utility scripts (generate types, etc.)
└── setup.sh        # Automated dev-env bootstrap
```

Turbo repo tasks: `build`, `dev`, `lint`, `test`, `type-check`, `clean`.

---

## 4&nbsp;·&nbsp;Architecture Overview

| Layer | Tech | Key Docs |
| ----- | ---- | -------- |
| **Auth & RBAC** | Firebase Auth, custom claims | [`RBAC.md`](RBAC.md) |
| **Data** | MongoDB Atlas (primary), Firestore (realtime) | [`STACK.md`](STACK.md) |
| **OCR Service** | Gemma LLM → Gemini → Vertex Document AI | [`apps/api/README.md`](apps/api/README.md) |
| **Accounting Connector** | QBO OAuth 2, webhooks, Cloud Tasks | [`docs/accounting-connector-guide.md`](docs/accounting-connector-guide.md) & [`docs/qbo-webhooks.md`](docs/qbo-webhooks.md) |
| **Frontend** | React 18, Vite, Tailwind, tRPC client | [`frontend/README.md`](frontend/README.md) |

A high-level component diagram is available in [`docs/accounting-connector-guide.md`](docs/accounting-connector-guide.md#architecture).

---

## 5&nbsp;·&nbsp;Documentation Index

Category | Document
---------|----------
Project Plan | **[`ROADMAP.md`](ROADMAP.md)**
Backend (TS) | **[`apps/api/README.md`](apps/api/README.md)**
Backend (Python) | `backend/` package docs & code comments
Frontend | **[`frontend/README.md`](frontend/README.md)**
Security | **[`RBAC.md`](RBAC.md)**
Data Stack | **[`STACK.md`](STACK.md)**
Accounting | **[`docs/accounting-connector-guide.md`](docs/accounting-connector-guide.md)**
QuickBooks Webhooks | **[`docs/qbo-webhooks.md`](docs/qbo-webhooks.md)**
Feature Specs | `docs/features/` (• Invoice Data Grid • Detail Drawer • Audit Logging • User Preferences …)

---

## 6&nbsp;·&nbsp;Configuration

All runtime configuration is driven by **environment variables**.

1. **Authoritative list** – see the repo-root [`.env.sample`](./.env.sample).  
   Copy it to `.env` (or service-specific `.env` files) and replace the `<placeholders>` with real values/secrets.
2. **Purpose & scope** – each variable’s service, description, and requirement status are documented in  
   [`docs/env-var-matrix.md`](docs/env-var-matrix.md).
3. **Bootstrap helper** – `./setup.sh` generates blank `.env` stubs for `apps/api` and `apps/web` with sane local defaults, but **does not** fill secrets.
4. **CI guard** – the `check-env-vars` step in GitHub Actions fails the build if new code references a variable missing from `.env.sample`, preventing “missing API_KEY” surprises.

> Keep secrets out of Git — commit only `*.example` / `.env.sample` files.

---

## 7&nbsp;·&nbsp;Setup & Environment

1. Run `./setup.sh` (idempotent) – creates `.env` files under `apps/api` and `apps/web` with sensible defaults.  
2. Start local Firebase emulators (optional): `firebase emulators:start`.  
3. For Dockerized stack (Mongo, Redis, Celery, Nginx), run `docker-compose up`.  
4. Configure Google Cloud credentials for OCR tiers if you need to execute end-to-end flows locally.

See inline comments in **[`setup.sh`](setup.sh)** for variable explanations.

---

## 8&nbsp;·&nbsp;Development Workflow

| Step | Command | Notes |
| ---- | ------- | ----- |
| Install deps | `pnpm install` | Workspace hoisting via pnpm |
| Start dev servers | `pnpm dev` | Turbo watches API & Web |
| Run tests | `pnpm test` | Jest (API) & Vitest (Web) |
| Lint & format | `pnpm lint` / `pnpm format` | Commit hooks via Husky & lint-staged |
| Type-check | `pnpm type-check` | Strict TypeScript everywhere |
| Build prod | `pnpm build` | Outputs to `dist/` or `frontend/dist` |
| Docker build | `docker-compose build` | CI builds images for ghcr.io |

Branch naming: `feat/…`, `fix/…`, `chore/…`.  
Commits follow **Conventional Commits** (enforced by `@commitlint`).

---

## 9&nbsp;·&nbsp;Contributing 🤝

We welcome issues, feature requests, and PRs!

1. Fork & create a feature branch (`git checkout -b feat/amazing-feature`)
2. Run `pnpm lint && pnpm test`
3. Commit using Conventional Commits (`feat: add amazing feature`)
4. Push & open a PR against `main`
5. The CI pipeline (lint → test → build) must pass before review

Please read [`CODE_OF_CONDUCT`](.github/CODE_OF_CONDUCT.md) *(coming soon)*.

---

## 10&nbsp;·&nbsp;Roadmap & Feature Progress

The live project plan (with completion percentages) is maintained in **[`ROADMAP.md`](ROADMAP.md)**.

Detailed specs & status for individual features live under **[`docs/features/`](docs/features)**:
- Invoice Data Grid
- Invoice Detail Drawer
- Audit Logging System
- User Preferences
- …and more.

---

## 11&nbsp;·&nbsp;License

MIT © 2025 Jean-Philippe Kayobotsi & Contributors
