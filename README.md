# AiClearBill (Billsnapp) &nbsp;🧾⚡

Production-ready, multi-tenant invoice-automation platform that combines **tiered AI-powered OCR**, **robust accounting connectors**, and **enterprise-grade RBAC** to eliminate manual data-entry and streamline bookkeeping.

---

## 1 · Why AiClearBill?

| Capability | Description |
| ---------- | ----------- |
| Tiered OCR Pipeline | Three-layer extraction (Gemma → Gemini → Document AI) delivers 99 %+ field accuracy while controlling costs. |
| Accounting Integrations | Out-of-the-box QuickBooks Online connector with secure token storage, bulk push, webhooks, and delta sync. |
| Multi-Tenant & RBAC | Fine-grained roles (`<PERSON><PERSON><PERSON>`, `Checker`, `Builder-in-Chief`, …) and Firestore security rules protect every tenant. |
| Offline-First PWA | React + Vite frontend works on desktop & mobile (installable PWA, IndexedDB caching, service-worker sync). |
| Cloud-Native | Firebase Auth, Firestore, MongoDB Atlas, Google Cloud Run, Pub/Sub, Cloud Tasks, KMS encryption. |

---

## 2 · Quick Start (Developer)

```bash
# 1. <PERSON><PERSON> & enter the repo
git clone https://github.com/Jpkay/AiClearBill.git
cd AiClearBill

# 2. One-shot environment bootstrap
./setup.sh          # installs pnpm deps, builds shared pkgs, creates .env files…

# 3. Launch all apps in watch mode
pnpm dev            # turbo runs apps/api and apps/web concurrently

# 4. Run tests & type-checks
pnpm test
pnpm lint
pnpm type-check
```

- 📖 **Development Guide**: For detailed setup instructions, debugging tips, and troubleshooting → [docs/DEV_GUIDE.md](docs/DEV_GUIDE.md)

> **Prerequisites**  
> Node 18+, pnpm 8+, Docker (for optional `docker-compose up`), and a Firebase project for local emulators.

---

## 3 · API Documentation  🚀
*(section content unchanged)*

---

## 4 · Monorepo Layout

```
.
├── apps/
│   ├── api/        # TypeScript tRPC API  ➜  see apps/api/README.md
│   └── web/        # React PWA frontend  ➜  see apps/web/README.md
├── backend/        # Legacy Python FastAPI + Celery workers
…
```

---

## 5 · Architecture

### 5.1 Current Stack Overview
AiClearBill currently operates a **dual-backend** architecture:

| Layer | Tech | Purpose |
|-------|------|---------|
| **TypeScript API** (`apps/api`) | Fastify + tRPC | Authentication, RBAC, Firestore ops, QuickBooks integration, *new* OCR orchestrator |
| **Python OCR Service** (`backend/`) | FastAPI | Legacy OCR orchestration (Gemini/Tesseract) and a handful of endpoints |

Both containers run on Cloud Run and share Pub/Sub topics, leading to duplicated logging, error handling, and Firestore writes.

### 5.2 Migration Strategy
We are converging on a **single TypeScript backend**.  
See the full phased plan, diagrams, risks, and acceptance criteria in  
➡️ **[docs/DUAL_BACKEND_MIGRATION.md](docs/DUAL_BACKEND_MIGRATION.md)**.

*(All subsequent sections have been renumbered accordingly.)*

---
