#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');
const { execSync } = require('child_process');

// Configuration
const API_URL = process.env.API_URL || 'http://localhost:8000';
const OPENAPI_PATH = '/openapi.json';
const OUTPUT_DIR = path.resolve(__dirname, '../src/api-types');
const OPENAPI_JSON_PATH = path.resolve(OUTPUT_DIR, 'openapi.json');

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

console.log(`Fetching OpenAPI schema from ${API_URL}${OPENAPI_PATH}...`);

// Determine if we need http or https
const client = API_URL.startsWith('https') ? https : http;

// Fetch the OpenAPI schema
const req = client.get(`${API_URL}${OPENAPI_PATH}`, (res) => {
  let data = '';

  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    if (res.statusCode !== 200) {
      console.error(`Failed to fetch OpenAPI schema: ${res.statusCode} ${res.statusMessage}`);
      console.error(data);
      process.exit(1);
    }

    try {
      // Parse and validate the schema
      const schema = JSON.parse(data);
      
      // Save the schema to a file
      fs.writeFileSync(OPENAPI_JSON_PATH, JSON.stringify(schema, null, 2));
      console.log(`OpenAPI schema saved to ${OPENAPI_JSON_PATH}`);

      // Generate TypeScript types
      console.log('Generating TypeScript types...');
      
      try {
        // Check if openapi-typescript is installed
        execSync('npx openapi-typescript --help', { stdio: 'ignore' });
      } catch (error) {
        console.log('Installing openapi-typescript...');
        execSync('npm install --save-dev openapi-typescript', { stdio: 'inherit' });
      }

      // Generate types
      execSync(`npx openapi-typescript ${OPENAPI_JSON_PATH} --output ${path.join(OUTPUT_DIR, 'api.ts')}`, {
        stdio: 'inherit'
      });

      console.log('TypeScript types generated successfully!');
    } catch (error) {
      console.error('Error processing OpenAPI schema:', error);
      process.exit(1);
    }
  });
});

req.on('error', (error) => {
  console.error('Error fetching OpenAPI schema:', error);
  process.exit(1);
});

req.end();
