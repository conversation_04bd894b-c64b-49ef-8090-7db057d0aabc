const tsPlugin = require("@typescript-eslint/eslint-plugin");
const tsParser = require("@typescript-eslint/parser");
const reactPlugin = require("eslint-plugin-react");
const prettier = require("eslint-config-prettier");

module.exports = [
  // TS/TSX rules (apply TS parser/plugins/rules)
  {
    files: ["**/*.ts", "**/*.tsx"],
    languageOptions: { parser: tsParser },
    plugins: { "@typescript-eslint": tsPlugin },
    rules: {
      "@typescript-eslint/explicit-module-boundary-types": "error",
      "@typescript-eslint/no-unused-vars": "error",
      "@typescript-eslint/no-explicit-any": "error",
    },
  },
  // JS/JSX rules (do not reference TS rules at all)
  {
    files: ["**/*.js", "**/*.jsx"],
    rules: {
      // Place JS/JSX-specific rules here if needed
    },
  },
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    ignores: [
      "eslint.config.js",
      "node_modules/**",
      "build/**",
      "dist/**",
      ".next/**",
      "*.config.js",
      "coverage/**",
      "public/**",
    ],
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        ecmaVersion: "latest",
        sourceType: "module",
        ecmaFeatures: { jsx: true },
      },
      globals: {
        window: "readonly",
        document: "readonly",
        console: "readonly",
        module: "writable",
        process: "readonly",
      },
    },
    plugins: {
      react: reactPlugin,
      prettier: require("eslint-plugin-prettier"),
    },
    rules: {
      ...reactPlugin.configs.recommended.rules,
      "prettier/prettier": "error",
      "react/prop-types": "off",
      "react/react-in-jsx-scope": "off",
    },
    settings: {
      react: {
        version: "detect",
      },
    },
  },
];
