{"name": "openapi-typescript", "description": "Convert OpenAPI 3.0 & 3.1 schemas to TypeScript", "version": "6.7.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bin": {"openapi-typescript": "bin/cli.js"}, "type": "module", "main": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "./*": "./*"}, "homepage": "https://openapi-ts.pages.dev", "repository": {"type": "git", "url": "https://github.com/drwpow/openapi-typescript", "directory": "packages/openapi-typescript"}, "keywords": ["swagger", "typescript", "ts", "dts", "openapi", "codegen", "generation", "openapi 3", "node"], "bugs": {"url": "https://github.com/drwpow/openapi-typescript/issues"}, "scripts": {"build": "pnpm run build:esm && pnpm run build:cjs", "build:esm": "del dist && tsc -p tsconfig.build.json", "build:cjs": "esbuild --bundle --platform=node --target=es2019 --outfile=dist/index.cjs --external:js-yaml --external:undici src/index.ts --footer:js=\"module.exports = module.exports.default;\"", "dev": "tsc -p tsconfig.build.json --watch", "download:schemas": "vite-node ./scripts/download-schemas.ts", "format": "prettier --write \"src/**/*\"", "lint": "pnpm run --parallel --filter=\"openapi-typescript\" \"/^lint:/\"", "lint:js": "eslint \"{src,test}/**/*.{js,ts}\"", "lint:prettier": "prettier --check \"src/**/*\"", "test": "pnpm run --parallel --filter=\"openapi-typescript\" \"/^test:/\"", "test:js": "vitest run", "test:ts": "tsc --noEmit", "update:examples": "pnpm run download:schemas && vite-node ./scripts/update-examples.ts", "prepublish": "pnpm run build", "version": "pnpm run build"}, "dependencies": {"ansi-colors": "^4.1.3", "fast-glob": "^3.3.2", "js-yaml": "^4.1.0", "supports-color": "^9.4.0", "undici": "^5.28.4", "yargs-parser": "^21.1.1"}, "devDependencies": {"@types/degit": "^2.8.6", "@types/js-yaml": "^4.0.9", "@types/node": "^20.12.12", "degit": "^2.8.4", "del-cli": "^5.1.0", "esbuild": "^0.21.3", "execa": "^7.2.0", "vite": "^5.2.11", "vite-node": "^1.6.0", "vitest": "^1.6.0"}}