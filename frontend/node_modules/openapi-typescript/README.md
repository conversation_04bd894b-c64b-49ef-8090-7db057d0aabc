<img src="../../docs/public/assets/openapi-ts.svg" alt="openapi-typescript" width="200" height="40" />

openapi-typescript generates TypeScript types from static <a href="https://spec.openapis.org/oas/latest.html" target="_blank" rel="noopener noreferrer">OpenAPI</a> schemas quickly using only Node.js. It is fast, lightweight, (almost) dependency-free, and no Java/node-gyp/running OpenAPI servers necessary.

The code is [MIT-licensed](./LICENSE) and free for use.

## Features

- ✅ Supports OpenAPI 3.0 and 3.1 (including advanced features like <a href="https://spec.openapis.org/oas/v3.1.0#discriminator-object" target="_blank" rel="noopener noreferrer">discriminators</a>)
- ✅ Generate **runtime-free types** that outperform old-school codegen
- ✅ Load schemas from YAML or JSON, locally or remotely
- ✅ Native Node.js code is fast and generates types within milliseconds

## Examples

👀 [See examples](./examples/)

## Setup

This library requires the latest version of <a href="https://nodejs.org/en" target="_blank" rel="noopener noreferrer">Node.js</a> installed (20.x or higher recommended). With that present, run the following in your project:

```bash
npm i -D openapi-typescript
```

> ✨ **Tip**
>
> Enabling [noUncheckedIndexedAccess](https://www.typescriptlang.org/tsconfig#noUncheckedIndexedAccess) in `tsconfig.json` can go along way to improve type safety ([read more](/docs/src/content/docs/advanced.md#enable-nouncheckedindexedaccess-in-your-tsconfigjson))

## Basic usage

First, generate a local type file by running `npx openapi-typescript`:

```bash
# Local schema
npx openapi-typescript ./path/to/my/schema.yaml -o ./path/to/my/schema.d.ts
# 🚀 ./path/to/my/schema.yaml -> ./path/to/my/schema.d.ts [7ms]
```

```bash
# Remote schema
npx openapi-typescript https://myapi.dev/api/v1/openapi.yaml -o ./path/to/my/schema.d.ts
# 🚀 https://myapi.dev/api/v1/openapi.yaml -> ./path/to/my/schema.d.ts [250ms]
```

> ⚠️ Be sure to <a href="https://redocly.com/docs/cli/commands/lint/" target="_blank" rel="noopener noreferrer">validate your schemas</a>! openapi-typescript will err on invalid schemas.

Then, import schemas from the generated file like so:

```ts
import { paths, components } from "./path/to/my/schema"; // <- generated by openapi-typescript

// Schema Obj
type MyType = components["schemas"]["MyType"];

// Path params
type EndpointParams = paths["/my/endpoint"]["parameters"];

// Response obj
type SuccessResponse = paths["/my/endpoint"]["get"]["responses"][200]["content"]["application/json"]["schema"];
type ErrorResponse = paths["/my/endpoint"]["get"]["responses"][500]["content"]["application/json"]["schema"];
```

#### 🦠 Globbing local schemas

```bash
npx openapi-typescript "specs/**/*.yaml" --output schemas/

# 🚀 specs/one.yaml -> schemas/specs/one.ts [7ms]
# 🚀 specs/two.yaml -> schemas/specs/two.ts [7ms]
# 🚀 specs/three.yaml -> schemas/specs/three.ts [7ms]
```

_Thanks, [@sharmarajdaksh](https://github.com/sharmarajdaksh)!_

#### ☁️ Remote schemas

```bash
npx openapi-typescript https://petstore3.swagger.io/api/v3/openapi.yaml --output petstore.d.ts

# 🚀 https://petstore3.swagger.io/api/v3/openapi.yaml -> petstore.d.ts [250ms]
```

_Thanks, [@psmyrdek](https://github.com/psmyrdek)!_

## 📓 Docs

[View Docs](https://openapi-ts.pages.dev/)
