import c from "ansi-colors";
import type { Fetch, ParameterObject, ReferenceObject } from "./types.js";
export { c };
interface CommentObject {
    const?: unknown;
    default?: unknown;
    deprecated?: boolean;
    description?: string;
    enum?: unknown[];
    example?: string;
    format?: string;
    nullable?: boolean;
    summary?: string;
    title?: string;
    type?: string | string[];
}
export declare const LB_RE: RegExp;
export declare const DOUBLE_QUOTE_RE: RegExp;
export declare const TS_INDEX_RE: RegExp;
export declare function walk(obj: unknown, cb: (value: Record<string, unknown>, path: (string | number)[]) => void, path?: (string | number)[]): void;
export declare function getSchemaObjectComment(v: CommentObject, indentLv?: number): string | undefined;
export declare function comment(text: string, indentLv?: number): string;
export declare function parseRef(ref: string): {
    filename: string;
    path: string[];
};
export declare function parseTSIndex(type: string): string[];
export declare function makeTSIndex(parts: (string | number)[]): string;
export declare function decodeRef(ref: string): string;
export declare function encodeRef(ref: string): string;
export declare function tsArrayOf(type: string): string;
export declare function tsIntersectionOf(...types: string[]): string;
export declare function tsNonNullable(type: string): string;
export declare function tsOneOf(...types: string[]): string;
export declare function tsPick(root: string, keys: string[]): string;
export declare function tsOmit(root: string, keys: string[]): string;
export declare function tsWithRequired(root: string, keys: string[]): string;
export declare function tsOptionalProperty(key: string): string;
export declare function tsReadonly(type: string): string;
export declare function tsTupleOf(...types: string[]): string;
export declare function tsUnionOf(...types: (string | number | boolean)[]): string;
export declare function escStr(input: unknown): string;
export declare function escObjKey(input: string): string;
export declare function indent(input: string, level: number): string;
export declare function getEntries<T>(obj: ArrayLike<T> | Record<string, T>, alphabetize?: boolean, excludeDeprecated?: boolean): [string, T][];
export declare function error(msg: string): void;
export declare function isRemoteURL(url: string): boolean;
export declare function isFilepath(url: string): boolean;
export declare function getDefaultFetch(): Fetch;
export declare function getParametersArray(parameters?: ParameterObject | (ParameterObject | ReferenceObject)[]): (ParameterObject | ReferenceObject)[];
