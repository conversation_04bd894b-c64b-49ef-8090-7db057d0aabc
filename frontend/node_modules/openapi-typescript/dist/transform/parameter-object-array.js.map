{"version": 3, "file": "parameter-object-array.js", "sourceRoot": "./", "sources": ["transform/parameter-object-array.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,kBAAkB,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AAChF,OAAO,wBAAwB,MAAM,uBAAuB,CAAC;AAO7D,MAAM,CAAC,OAAO,UAAU,6BAA6B,CAAC,oBAA+G,EAAE,EAAE,IAAI,EAAE,GAAG,EAAkC;IAClN,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,MAAM,UAAU,GAAkD,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAE,CAAqB,CAAC,IAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAElN,KAAK,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,UAAU,EAAE,CAAC;QACrC,IAAI,GAAG,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;QACxB,IAAI,GAAG,CAAC,cAAc;YAAE,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QAC9C,MAAM,IAAI,GAAgC,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAC/F,IAAI,CAAC,IAAI;YAAE,SAAS;QACpB,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CACT,MAAM,CACJ,GAAG,GAAG,KAAK,wBAAwB,CAAC,IAAI,EAAE;YACxC,IAAI,EAAE,GAAG,IAAI,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;YACvC,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,QAAQ,EAAE,GAAG,CAAC,QAAQ,GAAG,CAAC,EAAE;SAC5C,CAAC,GAAG,EACL,GAAG,CAAC,QAAQ,CACb,CACF,CAAC;IACJ,CAAC;IACD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC"}