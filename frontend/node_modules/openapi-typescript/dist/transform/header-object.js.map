{"version": 3, "file": "header-object.js", "sourceRoot": "./", "sources": ["transform/header-object.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,sBAAsB,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AAC7F,OAAO,wBAAwB,MAAM,wBAAwB,CAAC;AAC9D,OAAO,qBAAqB,MAAM,oBAAoB,CAAC;AAOvD,MAAM,CAAC,OAAO,UAAU,qBAAqB,CAAC,YAA0B,EAAE,EAAE,IAAI,EAAE,GAAG,EAAgC;IACnH,IAAI,YAAY,CAAC,MAAM;QAAE,OAAO,qBAAqB,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;IAC1F,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;QACzB,IAAI,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;QACvB,MAAM,MAAM,GAAa,CAAC,GAAG,CAAC,CAAC;QAC/B,QAAQ,EAAE,CAAC;QACX,KAAK,MAAM,CAAC,WAAW,EAAE,eAAe,CAAC,IAAI,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACtH,MAAM,CAAC,GAAG,sBAAsB,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAC5D,IAAI,CAAC;gBAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;YACxC,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;YAC9B,IAAI,GAAG,CAAC,cAAc;gBAAE,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YAC9C,IAAI,MAAM,IAAI,eAAe,EAAE,CAAC;gBAC9B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,qBAAqB,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,GAAG,IAAI,IAAI,WAAW,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC/H,CAAC;iBAAM,CAAC;gBACN,MAAM,SAAS,GAAG,wBAAwB,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,GAAG,IAAI,IAAI,WAAW,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;gBACrG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,SAAS,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;QACD,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;QACnC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC"}