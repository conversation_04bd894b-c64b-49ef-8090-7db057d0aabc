{"version": 3, "file": "response-object.js", "sourceRoot": "./", "sources": ["transform/response-object.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,sBAAsB,EAAE,MAAM,EAAE,kBAAkB,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AAC5H,OAAO,qBAAqB,MAAM,oBAAoB,CAAC;AACvD,OAAO,wBAAwB,MAAM,wBAAwB,CAAC;AAO9D,MAAM,CAAC,OAAO,UAAU,uBAAuB,CAAC,cAA8B,EAAE,EAAE,IAAI,EAAE,GAAG,EAAkC;IAC3H,MAAM,MAAM,GAAa,CAAC,GAAG,CAAC,CAAC;IAC/B,IAAI,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;IAGvB,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;QAC3B,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC5C,QAAQ,EAAE,CAAC;QACX,KAAK,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,UAAU,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC9G,MAAM,CAAC,GAAG,sBAAsB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YACzD,IAAI,CAAC;gBAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;YACxC,IAAI,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAC1B,IAAI,GAAG,CAAC,cAAc;gBAAE,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YAC9C,IAAI,MAAM,IAAI,YAAY,EAAE,CAAC;gBAC3B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,YAAY,CAAC,IAAI,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YACjE,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,YAAY,CAAC,QAAQ;oBAAE,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAC1D,MAAM,CAAC,IAAI,CACT,MAAM,CACJ,GAAG,GAAG,KAAK,qBAAqB,CAAC,YAAY,EAAE;oBAC7C,IAAI,EAAE,GAAG,IAAI,YAAY,IAAI,EAAE;oBAC/B,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,QAAQ,EAAE;iBAC1B,CAAC,GAAG,EACL,QAAQ,CACT,CACF,CAAC;YACJ,CAAC;QACH,CAAC;QACD,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;QACpC,QAAQ,EAAE,CAAC;IACb,CAAC;IAGD,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;QAC3B,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC5C,QAAQ,EAAE,CAAC;QACX,KAAK,MAAM,CAAC,WAAW,EAAE,eAAe,CAAC,IAAI,UAAU,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACxH,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;YAC9B,IAAI,GAAG,CAAC,cAAc;gBAAE,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CACT,MAAM,CACJ,GAAG,GAAG,KAAK,wBAAwB,CAAC,eAAe,EAAE;gBACnD,IAAI,EAAE,GAAG,IAAI,YAAY,WAAW,EAAE;gBACtC,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,QAAQ,EAAE,QAAQ,EAAE;aACpC,CAAC,GAAG,EACL,QAAQ,CACT,CACF,CAAC;QACJ,CAAC;QACD,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;QACpC,QAAQ,EAAE,CAAC;IACb,CAAC;SAAM,CAAC;QACN,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,CAAC;QACjD,QAAQ,EAAE,CAAC;IACb,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;IACnC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC"}