{"version": 3, "file": "request-body-object.js", "sourceRoot": "./", "sources": ["transform/request-body-object.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,sBAAsB,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AAC7F,OAAO,wBAAwB,MAAM,wBAAwB,CAAC;AAC9D,OAAO,qBAAqB,MAAM,oBAAoB,CAAC;AAOvD,MAAM,CAAC,OAAO,UAAU,0BAA0B,CAAC,iBAAoC,EAAE,EAAE,IAAI,EAAE,GAAG,EAAqC;IACvI,IAAI,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;IACvB,MAAM,MAAM,GAAa,CAAC,GAAG,CAAC,CAAC;IAC/B,QAAQ,EAAE,CAAC;IACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC5F,QAAQ,EAAE,CAAC;IAEX,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,KAAK,MAAM,CAAC,WAAW,EAAE,eAAe,CAAC,IAAI,UAAU,CAAC,iBAAiB,CAAC,OAAO,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC;QAC3H,MAAM,CAAC,GAAG,sBAAsB,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;QAC5D,IAAI,CAAC;YAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;QACxC,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;QAC9B,IAAI,GAAG,CAAC,cAAc;YAAE,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAI,MAAM,IAAI,eAAe,EAAE,CAAC;YAC9B,MAAM,CAAC,IAAI,CACT,MAAM,CACJ,GAAG,GAAG,KAAK,qBAAqB,CAAC,eAAe,EAAE;gBAChD,IAAI,EAAE,GAAG,IAAI,IAAI,WAAW,EAAE;gBAC9B,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,QAAQ,EAAE;aAC1B,CAAC,GAAG,EACL,QAAQ,CACT,CACF,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,SAAS,GAAG,wBAAwB,CAAC,eAAe,EAAE;gBAC1D,IAAI,EAAE,GAAG,IAAI,IAAI,WAAW,EAAE;gBAC9B,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,QAAQ,EAAE;aAC1B,CAAC,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,SAAS,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IACD,QAAQ,EAAE,CAAC;IACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;IACpC,QAAQ,EAAE,CAAC;IACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;IACnC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC"}