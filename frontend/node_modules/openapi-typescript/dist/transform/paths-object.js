import { escStr, getEntries, getParametersArray, getSchemaObjectComment, indent } from "../utils.js";
import transformParameterObject from "./parameter-object.js";
import transformPathItemObject from "./path-item-object.js";
const OPERATIONS = ["get", "post", "put", "delete", "options", "head", "patch", "trace"];
function extractPathParams(obj) {
    const params = new Map();
    if (obj && "parameters" in obj) {
        for (const p of getParametersArray(obj.parameters)) {
            if ("in" in p && p.in === "path")
                params.set(p.name, p);
        }
    }
    return params;
}
export default function transformPathsObject(pathsObject, ctx) {
    let { indentLv } = ctx;
    const output = ["{"];
    indentLv++;
    for (const [url, pathItemObject] of getEntries(pathsObject, ctx.alphabetize, ctx.excludeDeprecated)) {
        if (!pathItemObject || typeof pathItemObject !== "object")
            continue;
        let path = url;
        if ("$ref" in pathItemObject) {
            const c = getSchemaObjectComment(pathItemObject, indentLv);
            if (c)
                output.push(indent(c, indentLv));
            output.push(indent(`${escStr(path)}: ${pathItemObject.$ref};`, indentLv));
            continue;
        }
        const pathParams = new Map([...extractPathParams(pathItemObject), ...OPERATIONS.flatMap((op) => Array.from(extractPathParams(pathItemObject[op])))]);
        if (ctx.pathParamsAsTypes && pathParams.size) {
            for (const p of pathParams.values()) {
                const paramType = transformParameterObject(p, { path: `#/paths/${url}/parameters/${p.name}`, ctx });
                path = path.replace(`{${p.name}}`, `\${${paramType}}`);
            }
            path = `[path: \`${path}\`]`;
        }
        else {
            path = escStr(path);
        }
        output.push(indent(`${path}: ${transformPathItemObject(pathItemObject, {
            path: `#/paths/${url}`,
            ctx: { ...ctx, indentLv },
        })};`, indentLv));
    }
    indentLv--;
    output.push(indent("}", indentLv));
    return output.join("\n");
}
//# sourceMappingURL=paths-object.js.map