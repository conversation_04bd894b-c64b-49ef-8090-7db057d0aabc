{"version": 3, "file": "components-object.js", "sourceRoot": "./", "sources": ["transform/components-object.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,sBAAsB,EAAE,MAAM,EAAE,kBAAkB,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AACpH,OAAO,qBAAqB,MAAM,oBAAoB,CAAC;AACvD,OAAO,wBAAwB,MAAM,uBAAuB,CAAC;AAC7D,OAAO,uBAAuB,MAAM,uBAAuB,CAAC;AAC5D,OAAO,0BAA0B,MAAM,0BAA0B,CAAC;AAClE,OAAO,uBAAuB,MAAM,sBAAsB,CAAC;AAC3D,OAAO,wBAAwB,MAAM,wBAAwB,CAAC;AAC9D,OAAO,qBAAqB,MAAM,oBAAoB,CAAC;AAEvD,MAAM,CAAC,OAAO,UAAU,yBAAyB,CAAC,UAA4B,EAAE,GAAkB;IAChG,IAAI,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;IACvB,MAAM,MAAM,GAAa,CAAC,GAAG,CAAC,CAAC;IAC/B,QAAQ,EAAE,CAAC;IAGX,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG,wBAAwB,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,uBAAuB,EAAE,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;QAC3H,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,OAAO,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;IACxD,CAAC;SAAM,CAAC;QACN,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,CAAC;IACnD,CAAC;IAGD,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC9C,QAAQ,EAAE,CAAC;QACX,KAAK,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC9G,MAAM,CAAC,GAAG,sBAAsB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;YAC3D,IAAI,CAAC;gBAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;YACxC,IAAI,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAC1B,IAAI,GAAG,CAAC,cAAc;gBAAE,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YAC9C,IAAI,MAAM,IAAI,cAAc,EAAE,CAAC;gBAC7B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,qBAAqB,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,0BAA0B,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YACtI,CAAC;iBAAM,CAAC;gBACN,MAAM,YAAY,GAAG,uBAAuB,CAAC,cAAc,EAAE;oBAC3D,IAAI,EAAE,0BAA0B,IAAI,EAAE;oBACtC,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,QAAQ,EAAE;iBAC1B,CAAC,CAAC;gBACH,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,YAAY,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QACD,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;IACtC,CAAC;SAAM,CAAC;QACN,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC,CAAC;IACrD,CAAC;IAGD,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;QAC1B,MAAM,UAAU,GAAa,EAAE,CAAC;QAChC,QAAQ,EAAE,CAAC;QAEX,KAAK,MAAM,CAAC,IAAI,EAAE,eAAe,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAChH,MAAM,CAAC,GAAG,sBAAsB,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAC5D,IAAI,CAAC;gBAAE,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC5C,IAAI,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAC1B,IAAI,GAAG,CAAC,cAAc;gBAAE,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YAC9C,IAAI,MAAM,IAAI,eAAe,EAAE,CAAC;gBAC9B,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,qBAAqB,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,2BAA2B,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC5I,CAAC;iBAAM,CAAC;gBACN,IAAI,eAAe,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;oBAC/D,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAChC,CAAC;gBACD,MAAM,aAAa,GAAG,wBAAwB,CAAC,eAAe,EAAE;oBAC9D,IAAI,EAAE,2BAA2B,IAAI,EAAE;oBACvC,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,QAAQ,EAAE;iBAC1B,CAAC,CAAC;gBACH,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,aAAa,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QACD,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,QAAQ,CAAC,EAAE,GAAG,UAAU,EAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;IACxF,CAAC;SAAM,CAAC;QACN,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC,CAAC;IACtD,CAAC;IAGD,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;QAC7B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC,CAAC;QAClD,QAAQ,EAAE,CAAC;QACX,KAAK,MAAM,CAAC,IAAI,EAAE,iBAAiB,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,aAAa,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACrH,MAAM,CAAC,GAAG,sBAAsB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;YAC9D,IAAI,CAAC;gBAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;YACxC,IAAI,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAC1B,IAAI,MAAM,IAAI,iBAAiB,EAAE,CAAC;gBAChC,IAAI,GAAG,CAAC,cAAc;oBAAE,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;gBAC9C,MAAM,CAAC,IAAI,CACT,MAAM,CACJ,GAAG,GAAG,KAAK,qBAAqB,CAAC,iBAAiB,EAAE;oBAClD,IAAI,EAAE,8BAA8B,IAAI,EAAE;oBAC1C,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,QAAQ,EAAE;iBAC1B,CAAC,GAAG,EACL,QAAQ,CACT,CACF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,iBAAiB,CAAC,QAAQ;oBAAE,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAC/D,IAAI,GAAG,CAAC,cAAc;oBAAE,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;gBAC9C,MAAM,eAAe,GAAG,0BAA0B,CAAC,iBAAiB,EAAE;oBACpE,IAAI,EAAE,8BAA8B,IAAI,EAAE;oBAC1C,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,QAAQ,EAAE;iBAC1B,CAAC,CAAC;gBACH,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,eAAe,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QACD,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;IACtC,CAAC;SAAM,CAAC;QACN,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC,CAAC;IACzD,CAAC;IAGD,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;QACvB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC5C,QAAQ,EAAE,CAAC;QACX,KAAK,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC1G,MAAM,CAAC,GAAG,sBAAsB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YACzD,IAAI,CAAC;gBAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;YACxC,IAAI,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAC1B,IAAI,GAAG,CAAC,cAAc;gBAAE,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YAC9C,IAAI,MAAM,IAAI,YAAY,EAAE,CAAC;gBAC3B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,qBAAqB,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,wBAAwB,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YAClI,CAAC;iBAAM,CAAC;gBACN,MAAM,UAAU,GAAG,qBAAqB,CAAC,YAAY,EAAE;oBACrD,IAAI,EAAE,wBAAwB,IAAI,EAAE;oBACpC,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,QAAQ,EAAE;iBAC1B,CAAC,CAAC;gBACH,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,UAAU,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QACD,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;IACtC,CAAC;SAAM,CAAC;QACN,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,CAAC;IACnD,CAAC;IAGD,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC9C,QAAQ,EAAE,CAAC;QACX,KAAK,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC9G,IAAI,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAC1B,IAAI,GAAG,CAAC,cAAc;gBAAE,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YAC9C,IAAI,MAAM,IAAI,cAAc,EAAE,CAAC;gBAC7B,MAAM,CAAC,GAAG,sBAAsB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;gBAC3D,IAAI,CAAC;oBAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;gBACxC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,qBAAqB,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,0BAA0B,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YACtI,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CACT,MAAM,CACJ,GAAG,GAAG,KAAK,uBAAuB,CAAC,cAAc,EAAE;oBACjD,IAAI,EAAE,0BAA0B,IAAI,EAAE;oBACtC,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,QAAQ,EAAE;iBAC1B,CAAC,GAAG,EACL,QAAQ,CACT,CACF,CAAC;YACJ,CAAC;QACH,CAAC;QACD,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;IACtC,CAAC;SAAM,CAAC;QACN,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC,CAAC;IACrD,CAAC;IAED,QAAQ,EAAE,CAAC;IACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;IACnC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC"}