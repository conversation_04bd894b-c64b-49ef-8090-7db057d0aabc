/// <reference types="node" />
/// <reference types="node" />
import type { OpenAPI3, OpenAPITSOptions } from "./types.js";
import type { Readable } from "node:stream";
import { URL } from "node:url";
export * from "./types.js";
export declare const COMMENT_HEADER = "/**\n * This file was auto-generated by openapi-typescript.\n * Do not make direct changes to the file.\n */\n\n";
declare function openapiTS(schema: string | URL | OpenAPI3 | Readable, options?: OpenAPITSOptions): Promise<string>;
export default openapiTS;
