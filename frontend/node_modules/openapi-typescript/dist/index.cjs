"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// ../../node_modules/.pnpm/ansi-colors@4.1.3/node_modules/ansi-colors/symbols.js
var require_symbols = __commonJS({
  "../../node_modules/.pnpm/ansi-colors@4.1.3/node_modules/ansi-colors/symbols.js"(exports2, module2) {
    "use strict";
    var isHyper = typeof process !== "undefined" && process.env.TERM_PROGRAM === "Hyper";
    var isWindows = typeof process !== "undefined" && process.platform === "win32";
    var isLinux = typeof process !== "undefined" && process.platform === "linux";
    var common = {
      ballotDisabled: "\u2612",
      ballotOff: "\u2610",
      ballotOn: "\u2611",
      bullet: "\u2022",
      bulletWhite: "\u25E6",
      fullBlock: "\u2588",
      heart: "\u2764",
      identicalTo: "\u2261",
      line: "\u2500",
      mark: "\u203B",
      middot: "\xB7",
      minus: "\uFF0D",
      multiplication: "\xD7",
      obelus: "\xF7",
      pencilDownRight: "\u270E",
      pencilRight: "\u270F",
      pencilUpRight: "\u2710",
      percent: "%",
      pilcrow2: "\u2761",
      pilcrow: "\xB6",
      plusMinus: "\xB1",
      question: "?",
      section: "\xA7",
      starsOff: "\u2606",
      starsOn: "\u2605",
      upDownArrow: "\u2195"
    };
    var windows = Object.assign({}, common, {
      check: "\u221A",
      cross: "\xD7",
      ellipsisLarge: "...",
      ellipsis: "...",
      info: "i",
      questionSmall: "?",
      pointer: ">",
      pointerSmall: "\xBB",
      radioOff: "( )",
      radioOn: "(*)",
      warning: "\u203C"
    });
    var other = Object.assign({}, common, {
      ballotCross: "\u2718",
      check: "\u2714",
      cross: "\u2716",
      ellipsisLarge: "\u22EF",
      ellipsis: "\u2026",
      info: "\u2139",
      questionFull: "\uFF1F",
      questionSmall: "\uFE56",
      pointer: isLinux ? "\u25B8" : "\u276F",
      pointerSmall: isLinux ? "\u2023" : "\u203A",
      radioOff: "\u25EF",
      radioOn: "\u25C9",
      warning: "\u26A0"
    });
    module2.exports = isWindows && !isHyper ? windows : other;
    Reflect.defineProperty(module2.exports, "common", { enumerable: false, value: common });
    Reflect.defineProperty(module2.exports, "windows", { enumerable: false, value: windows });
    Reflect.defineProperty(module2.exports, "other", { enumerable: false, value: other });
  }
});

// ../../node_modules/.pnpm/ansi-colors@4.1.3/node_modules/ansi-colors/index.js
var require_ansi_colors = __commonJS({
  "../../node_modules/.pnpm/ansi-colors@4.1.3/node_modules/ansi-colors/index.js"(exports2, module2) {
    "use strict";
    var isObject = (val) => val !== null && typeof val === "object" && !Array.isArray(val);
    var ANSI_REGEX = /[\u001b\u009b][[\]#;?()]*(?:(?:(?:[^\W_]*;?[^\W_]*)\u0007)|(?:(?:[0-9]{1,4}(;[0-9]{0,4})*)?[~0-9=<>cf-nqrtyA-PRZ]))/g;
    var hasColor = () => {
      if (typeof process !== "undefined") {
        return process.env.FORCE_COLOR !== "0";
      }
      return false;
    };
    var create = () => {
      const colors = {
        enabled: hasColor(),
        visible: true,
        styles: {},
        keys: {}
      };
      const ansi = (style2) => {
        let open = style2.open = `\x1B[${style2.codes[0]}m`;
        let close = style2.close = `\x1B[${style2.codes[1]}m`;
        let regex = style2.regex = new RegExp(`\\u001b\\[${style2.codes[1]}m`, "g");
        style2.wrap = (input, newline) => {
          if (input.includes(close)) input = input.replace(regex, close + open);
          let output = open + input + close;
          return newline ? output.replace(/\r*\n/g, `${close}$&${open}`) : output;
        };
        return style2;
      };
      const wrap = (style2, input, newline) => {
        return typeof style2 === "function" ? style2(input) : style2.wrap(input, newline);
      };
      const style = (input, stack) => {
        if (input === "" || input == null) return "";
        if (colors.enabled === false) return input;
        if (colors.visible === false) return "";
        let str = "" + input;
        let nl = str.includes("\n");
        let n = stack.length;
        if (n > 0 && stack.includes("unstyle")) {
          stack = [.../* @__PURE__ */ new Set(["unstyle", ...stack])].reverse();
        }
        while (n-- > 0) str = wrap(colors.styles[stack[n]], str, nl);
        return str;
      };
      const define = (name, codes, type) => {
        colors.styles[name] = ansi({ name, codes });
        let keys = colors.keys[type] || (colors.keys[type] = []);
        keys.push(name);
        Reflect.defineProperty(colors, name, {
          configurable: true,
          enumerable: true,
          set(value) {
            colors.alias(name, value);
          },
          get() {
            let color = (input) => style(input, color.stack);
            Reflect.setPrototypeOf(color, colors);
            color.stack = this.stack ? this.stack.concat(name) : [name];
            return color;
          }
        });
      };
      define("reset", [0, 0], "modifier");
      define("bold", [1, 22], "modifier");
      define("dim", [2, 22], "modifier");
      define("italic", [3, 23], "modifier");
      define("underline", [4, 24], "modifier");
      define("inverse", [7, 27], "modifier");
      define("hidden", [8, 28], "modifier");
      define("strikethrough", [9, 29], "modifier");
      define("black", [30, 39], "color");
      define("red", [31, 39], "color");
      define("green", [32, 39], "color");
      define("yellow", [33, 39], "color");
      define("blue", [34, 39], "color");
      define("magenta", [35, 39], "color");
      define("cyan", [36, 39], "color");
      define("white", [37, 39], "color");
      define("gray", [90, 39], "color");
      define("grey", [90, 39], "color");
      define("bgBlack", [40, 49], "bg");
      define("bgRed", [41, 49], "bg");
      define("bgGreen", [42, 49], "bg");
      define("bgYellow", [43, 49], "bg");
      define("bgBlue", [44, 49], "bg");
      define("bgMagenta", [45, 49], "bg");
      define("bgCyan", [46, 49], "bg");
      define("bgWhite", [47, 49], "bg");
      define("blackBright", [90, 39], "bright");
      define("redBright", [91, 39], "bright");
      define("greenBright", [92, 39], "bright");
      define("yellowBright", [93, 39], "bright");
      define("blueBright", [94, 39], "bright");
      define("magentaBright", [95, 39], "bright");
      define("cyanBright", [96, 39], "bright");
      define("whiteBright", [97, 39], "bright");
      define("bgBlackBright", [100, 49], "bgBright");
      define("bgRedBright", [101, 49], "bgBright");
      define("bgGreenBright", [102, 49], "bgBright");
      define("bgYellowBright", [103, 49], "bgBright");
      define("bgBlueBright", [104, 49], "bgBright");
      define("bgMagentaBright", [105, 49], "bgBright");
      define("bgCyanBright", [106, 49], "bgBright");
      define("bgWhiteBright", [107, 49], "bgBright");
      colors.ansiRegex = ANSI_REGEX;
      colors.hasColor = colors.hasAnsi = (str) => {
        colors.ansiRegex.lastIndex = 0;
        return typeof str === "string" && str !== "" && colors.ansiRegex.test(str);
      };
      colors.alias = (name, color) => {
        let fn = typeof color === "string" ? colors[color] : color;
        if (typeof fn !== "function") {
          throw new TypeError("Expected alias to be the name of an existing color (string) or a function");
        }
        if (!fn.stack) {
          Reflect.defineProperty(fn, "name", { value: name });
          colors.styles[name] = fn;
          fn.stack = [name];
        }
        Reflect.defineProperty(colors, name, {
          configurable: true,
          enumerable: true,
          set(value) {
            colors.alias(name, value);
          },
          get() {
            let color2 = (input) => style(input, color2.stack);
            Reflect.setPrototypeOf(color2, colors);
            color2.stack = this.stack ? this.stack.concat(fn.stack) : fn.stack;
            return color2;
          }
        });
      };
      colors.theme = (custom) => {
        if (!isObject(custom)) throw new TypeError("Expected theme to be an object");
        for (let name of Object.keys(custom)) {
          colors.alias(name, custom[name]);
        }
        return colors;
      };
      colors.alias("unstyle", (str) => {
        if (typeof str === "string" && str !== "") {
          colors.ansiRegex.lastIndex = 0;
          return str.replace(colors.ansiRegex, "");
        }
        return "";
      });
      colors.alias("noop", (str) => str);
      colors.none = colors.clear = colors.noop;
      colors.stripColor = colors.unstyle;
      colors.symbols = require_symbols();
      colors.define = define;
      return colors;
    };
    module2.exports = create();
    module2.exports.create = create;
  }
});

// src/index.ts
var src_exports = {};
__export(src_exports, {
  COMMENT_HEADER: () => COMMENT_HEADER,
  default: () => src_default
});
module.exports = __toCommonJS(src_exports);
var import_node_url2 = require("url");

// src/load.ts
var import_node_fs = __toESM(require("fs"), 1);
var import_node_path2 = __toESM(require("path"), 1);
var import_node_stream = require("stream");
var import_node_url = require("url");
var import_js_yaml = __toESM(require("js-yaml"), 1);

// src/utils.ts
var import_ansi_colors = __toESM(require_ansi_colors(), 1);
var import_node_path = require("path");

// ../../node_modules/.pnpm/supports-color@9.4.0/node_modules/supports-color/index.js
var import_node_process = __toESM(require("process"), 1);
var import_node_os = __toESM(require("os"), 1);
var import_node_tty = __toESM(require("tty"), 1);
function hasFlag(flag, argv = globalThis.Deno ? globalThis.Deno.args : import_node_process.default.argv) {
  const prefix = flag.startsWith("-") ? "" : flag.length === 1 ? "-" : "--";
  const position = argv.indexOf(prefix + flag);
  const terminatorPosition = argv.indexOf("--");
  return position !== -1 && (terminatorPosition === -1 || position < terminatorPosition);
}
var { env } = import_node_process.default;
var flagForceColor;
if (hasFlag("no-color") || hasFlag("no-colors") || hasFlag("color=false") || hasFlag("color=never")) {
  flagForceColor = 0;
} else if (hasFlag("color") || hasFlag("colors") || hasFlag("color=true") || hasFlag("color=always")) {
  flagForceColor = 1;
}
function envForceColor() {
  if ("FORCE_COLOR" in env) {
    if (env.FORCE_COLOR === "true") {
      return 1;
    }
    if (env.FORCE_COLOR === "false") {
      return 0;
    }
    return env.FORCE_COLOR.length === 0 ? 1 : Math.min(Number.parseInt(env.FORCE_COLOR, 10), 3);
  }
}
function translateLevel(level) {
  if (level === 0) {
    return false;
  }
  return {
    level,
    hasBasic: true,
    has256: level >= 2,
    has16m: level >= 3
  };
}
function _supportsColor(haveStream, { streamIsTTY, sniffFlags = true } = {}) {
  const noFlagForceColor = envForceColor();
  if (noFlagForceColor !== void 0) {
    flagForceColor = noFlagForceColor;
  }
  const forceColor = sniffFlags ? flagForceColor : noFlagForceColor;
  if (forceColor === 0) {
    return 0;
  }
  if (sniffFlags) {
    if (hasFlag("color=16m") || hasFlag("color=full") || hasFlag("color=truecolor")) {
      return 3;
    }
    if (hasFlag("color=256")) {
      return 2;
    }
  }
  if ("TF_BUILD" in env && "AGENT_NAME" in env) {
    return 1;
  }
  if (haveStream && !streamIsTTY && forceColor === void 0) {
    return 0;
  }
  const min = forceColor || 0;
  if (env.TERM === "dumb") {
    return min;
  }
  if (import_node_process.default.platform === "win32") {
    const osRelease = import_node_os.default.release().split(".");
    if (Number(osRelease[0]) >= 10 && Number(osRelease[2]) >= 10586) {
      return Number(osRelease[2]) >= 14931 ? 3 : 2;
    }
    return 1;
  }
  if ("CI" in env) {
    if ("GITHUB_ACTIONS" in env || "GITEA_ACTIONS" in env) {
      return 3;
    }
    if (["TRAVIS", "CIRCLECI", "APPVEYOR", "GITLAB_CI", "BUILDKITE", "DRONE"].some((sign) => sign in env) || env.CI_NAME === "codeship") {
      return 1;
    }
    return min;
  }
  if ("TEAMCITY_VERSION" in env) {
    return /^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(env.TEAMCITY_VERSION) ? 1 : 0;
  }
  if (env.COLORTERM === "truecolor") {
    return 3;
  }
  if (env.TERM === "xterm-kitty") {
    return 3;
  }
  if ("TERM_PROGRAM" in env) {
    const version = Number.parseInt((env.TERM_PROGRAM_VERSION || "").split(".")[0], 10);
    switch (env.TERM_PROGRAM) {
      case "iTerm.app": {
        return version >= 3 ? 3 : 2;
      }
      case "Apple_Terminal": {
        return 2;
      }
    }
  }
  if (/-256(color)?$/i.test(env.TERM)) {
    return 2;
  }
  if (/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(env.TERM)) {
    return 1;
  }
  if ("COLORTERM" in env) {
    return 1;
  }
  return min;
}
function createSupportsColor(stream, options = {}) {
  const level = _supportsColor(stream, {
    streamIsTTY: stream && stream.isTTY,
    ...options
  });
  return translateLevel(level);
}
var supportsColor = {
  stdout: createSupportsColor({ isTTY: import_node_tty.default.isatty(1) }),
  stderr: createSupportsColor({ isTTY: import_node_tty.default.isatty(2) })
};
var supports_color_default = supportsColor;

// src/utils.ts
var import_undici = require("undici");
if (!supports_color_default.stdout || supports_color_default.stdout.hasBasic === false) import_ansi_colors.default.enabled = false;
var COMMENT_RE = /\*\//g;
var LB_RE = /\r?\n/g;
var DOUBLE_QUOTE_RE = /(?<!\\)"/g;
var ESC_0_RE = /~0/g;
var ESC_1_RE = /~1/g;
var TS_UNION_INTERSECTION_RE = /[&|]/;
var TS_READONLY_RE = /^readonly\s+/;
var JS_OBJ_KEY = /^(\d+|[A-Za-z_$][A-Za-z0-9_$]*)$/;
function walk(obj, cb, path2 = []) {
  if (!obj || typeof obj !== "object") return;
  if (Array.isArray(obj)) {
    for (let i = 0; i < obj.length; i++) walk(obj[i], cb, path2.concat(i));
    return;
  }
  cb(obj, path2);
  for (const k of Object.keys(obj)) walk(obj[k], cb, path2.concat(k));
}
function getSchemaObjectComment(v, indentLv) {
  if (!v || typeof v !== "object") return;
  const output = [];
  if (v.title) output.push(v.title);
  if (v.summary) output.push(v.summary);
  if (v.format) output.push(`Format: ${v.format}`);
  if (v.deprecated) output.push("@deprecated");
  const supportedJsDocTags = ["description", "default", "example"];
  for (const field of supportedJsDocTags) {
    const allowEmptyString = field === "default" || field === "example";
    if (v[field] === void 0) {
      continue;
    }
    if (v[field] === "" && !allowEmptyString) {
      continue;
    }
    const serialized = typeof v[field] === "object" ? JSON.stringify(v[field], null, 2) : v[field];
    output.push(`@${field} ${serialized}`);
  }
  if ("const" in v) output.push("@constant");
  if (v.enum) {
    let type = "unknown";
    if (Array.isArray(v.type)) type = v.type.join("|");
    else if (typeof v.type === "string") type = v.type;
    output.push(`@enum {${type}${v.nullable ? `|null` : ""}}`);
  }
  return output.length ? comment(output.join("\n"), indentLv) : void 0;
}
function comment(text, indentLv) {
  const commentText = text.trim().replace(COMMENT_RE, "*\\/");
  if (!commentText.includes("\n")) return `/** ${commentText} */`;
  const star = indent(" *", indentLv != null ? indentLv : 0);
  const body = commentText.split(LB_RE).map((ln) => {
    ln = ln.trimEnd();
    return ln.length > 0 ? `${star} ${ln}` : star;
  });
  return ["/**", body.join("\n"), indent(" */", indentLv != null ? indentLv : 0)].join("\n");
}
function parseRef(ref) {
  if (typeof ref !== "string") return { filename: ".", path: [] };
  if (ref.includes("#/")) {
    const [filename, pathStr] = ref.split("#");
    const parts = pathStr.split("/");
    const path2 = [];
    for (const part of parts) {
      if (!part || part === "properties") continue;
      path2.push(decodeRef(part));
    }
    return { filename: filename || ".", path: path2 };
  } else if (ref.includes('["')) {
    const parts = ref.split('["');
    const path2 = [];
    for (const part of parts) {
      const sanitized = part.replace('"]', "").trim();
      if (!sanitized || sanitized === "properties") continue;
      path2.push(sanitized);
    }
    return { filename: ".", path: path2 };
  }
  return { filename: ref, path: [] };
}
function makeTSIndex(parts) {
  return `${parts[0]}[${parts.slice(1).map(escStr).join("][")}]`;
}
function decodeRef(ref) {
  return ref.replace(ESC_0_RE, "~").replace(ESC_1_RE, "/").replace(DOUBLE_QUOTE_RE, '\\"');
}
function parenthesise(type) {
  return TS_UNION_INTERSECTION_RE.test(type) || TS_READONLY_RE.test(type) ? `(${type})` : type;
}
function tsArrayOf(type) {
  return `${parenthesise(type)}[]`;
}
function tsIntersectionOf(...types) {
  types = types.filter((t) => t !== "unknown");
  if (types.length === 0) return "unknown";
  if (types.length === 1) return String(types[0]);
  return types.map((t) => parenthesise(t)).join(" & ");
}
function tsOneOf(...types) {
  if (types.length === 1) {
    return types[0];
  } else if (types.length > 5) {
    return tsUnionOf(...types);
  }
  return `OneOf<[${types.join(", ")}]>`;
}
function tsOmit(root, keys) {
  return `Omit<${root}, ${tsUnionOf(...keys.map(escStr))}>`;
}
function tsWithRequired(root, keys) {
  return `WithRequired<${root}, ${tsUnionOf(...keys.map(escStr))}>`;
}
function tsOptionalProperty(key) {
  return `${key}?`;
}
function tsReadonly(type) {
  return `readonly ${type}`;
}
function tsTupleOf(...types) {
  return `[${types.join(", ")}]`;
}
function tsUnionOf(...types) {
  if (types.length === 0) return "never";
  const members = /* @__PURE__ */ new Set();
  for (const t of types) {
    if (t === "unknown") return "unknown";
    members.add(String(t));
  }
  if (members.has("never") && members.size === 1) return "never";
  members.delete("never");
  const memberArr = Array.from(members);
  if (members.size === 1) return memberArr[0];
  let out = "";
  for (let i = 0; i < memberArr.length; i++) {
    const t = memberArr[i];
    out += parenthesise(t);
    if (i !== memberArr.length - 1) out += " | ";
  }
  return out;
}
function escStr(input) {
  if (typeof input !== "string") return JSON.stringify(input);
  return `"${input.replace(LB_RE, "").replace(DOUBLE_QUOTE_RE, '\\"')}"`;
}
function escObjKey(input) {
  return JS_OBJ_KEY.test(input) ? input : escStr(input);
}
function indent(input, level) {
  if (level > 0) {
    return "  ".repeat(level).concat(input);
  } else {
    return input;
  }
}
function getEntries(obj, alphabetize, excludeDeprecated) {
  let entries = Object.entries(obj);
  if (alphabetize) entries.sort(([a], [b]) => a.localeCompare(b, "en", { numeric: true }));
  if (excludeDeprecated) entries = entries.filter(([, v]) => !(v && typeof v === "object" && "deprecated" in v && v.deprecated));
  return entries;
}
function error(msg) {
  console.error(import_ansi_colors.default.red(` \u2718  ${msg}`));
}
function isRemoteURL(url) {
  return url.startsWith("https://") || url.startsWith("//") || url.startsWith("http://");
}
function isFilepath(url) {
  return url.startsWith("file://") || (0, import_node_path.isAbsolute)(url);
}
function getDefaultFetch() {
  const globalFetch = globalThis.fetch;
  if (typeof globalFetch === "undefined") {
    return import_undici.fetch;
  }
  return globalFetch;
}
function getParametersArray(parameters = []) {
  return Array.isArray(parameters) ? parameters : [parameters];
}

// src/load.ts
var EXT_RE = /\.(yaml|yml|json)#?\/?/i;
var VIRTUAL_JSON_URL = `file:///_json`;
function parseSchema(source) {
  return source.trim().startsWith("{") ? JSON.parse(source) : import_js_yaml.default.load(source);
}
function resolveSchema(filename) {
  if (isRemoteURL(filename)) return new import_node_url.URL(filename.startsWith("//") ? `https:${filename}` : filename);
  const localPath = import_node_path2.default.isAbsolute(filename) ? new import_node_url.URL(`file://${filename}`) : new import_node_url.URL(filename, `file://${process.cwd()}/`);
  if (!import_node_fs.default.existsSync(localPath)) {
    error(`Could not locate ${filename}`);
    process.exit(1);
  } else if (import_node_fs.default.statSync(localPath).isDirectory()) {
    error(`${localPath} is a directory not a file`);
    process.exit(1);
  }
  return localPath;
}
function parseHttpHeaders(httpHeaders) {
  const finalHeaders = {};
  for (const [k, v] of Object.entries(httpHeaders)) {
    if (typeof v === "string") {
      finalHeaders[k] = v;
    } else {
      try {
        const stringVal = JSON.stringify(v);
        finalHeaders[k] = stringVal;
      } catch (err) {
        error(`Can\u2019t parse key: ${k} into JSON format. Continuing with the next HTTP header that is specified`);
      }
    }
  }
  return finalHeaders;
}
async function load(schema, options) {
  var _a;
  let schemaID = ".";
  if (schema instanceof import_node_url.URL) {
    const hint = (_a = options.hint) != null ? _a : "OpenAPI3";
    if (schema.href !== options.rootURL.href) {
      schemaID = relativePath(options.rootURL, schema);
    }
    if (options.urlCache.has(schemaID)) {
      return options.schemas;
    }
    options.urlCache.add(schemaID);
    if (schema.protocol.startsWith("http")) {
      const headers = { "User-Agent": "openapi-typescript" };
      if (options.auth) headers.Authorization = options.auth;
      if (options.httpHeaders) {
        const parsedHeaders = parseHttpHeaders(options.httpHeaders);
        for (const [k, v] of Object.entries(parsedHeaders)) {
          headers[k] = v;
        }
      }
      const res = await options.fetch(schema, {
        method: options.httpMethod || "GET",
        headers
      });
      const contents = await res.text();
      options.schemas[schemaID] = { hint, schema: parseSchema(contents) };
    } else {
      const contents = import_node_fs.default.readFileSync(schema, "utf8");
      options.schemas[schemaID] = { hint, schema: parseSchema(contents) };
    }
  } else if (schema instanceof import_node_stream.Readable) {
    const readable = schema;
    const contents = await new Promise((resolve) => {
      readable.resume();
      readable.setEncoding("utf8");
      let content = "";
      readable.on("data", (chunk) => {
        content += chunk;
      });
      readable.on("end", () => {
        resolve(content.trim());
      });
    });
    options.schemas[schemaID] = { hint: "OpenAPI3", schema: parseSchema(contents) };
  } else if (typeof schema === "object") {
    options.schemas[schemaID] = {
      hint: "OpenAPI3",
      schema: JSON.parse(JSON.stringify(schema))
      // create deep clone of inline schema (don’t mutate)
    };
  } else {
    error(`Invalid schema`);
    process.exit(1);
  }
  const currentSchema = options.schemas[schemaID].schema;
  if (options.schemas[schemaID].hint === "OpenAPI3") {
    if ("components" in currentSchema && currentSchema.components && "examples" in currentSchema.components) delete currentSchema.components.examples;
  }
  const refPromises = [];
  walk(currentSchema, (rawNode, nodePath) => {
    for (const k of ["allOf", "anyOf", "oneOf"]) {
      if (Array.isArray(rawNode[k])) {
        rawNode[k] = rawNode[k].filter((o) => {
          if (!o || typeof o !== "object" || Array.isArray(o)) throw new Error(`${nodePath}.${k}: Expected array of objects. Is your schema valid?`);
          if (!("$ref" in o) || typeof o.$ref !== "string") return true;
          const ref2 = parseRef(o.$ref);
          return !ref2.path.some((i) => i.startsWith("x-"));
        });
      }
    }
    if (!rawNode || typeof rawNode !== "object" || Array.isArray(rawNode)) throw new Error(`${nodePath}: Expected object, got ${Array.isArray(rawNode) ? "array" : typeof rawNode}. Is your schema valid?`);
    if (!("$ref" in rawNode) || typeof rawNode.$ref !== "string") return;
    const node = rawNode;
    const ref = parseRef(node.$ref);
    if (ref.filename === ".") {
      return;
    }
    if (ref.path.some((i) => i.startsWith("x-"))) {
      delete node.$ref;
      return;
    }
    const isRemoteFullSchema = ref.path[0] === "paths" || ref.path[0] === "components";
    const hintPath = [...nodePath];
    if (ref.filename) hintPath.push(ref.filename);
    hintPath.push(...ref.path);
    const hint = isRemoteFullSchema ? "OpenAPI3" : getHint({ path: hintPath, external: !!ref.filename, startFrom: options.hint });
    if (isRemoteURL(ref.filename) || isFilepath(ref.filename)) {
      const nextURL2 = new import_node_url.URL(ref.filename.startsWith("//") ? `https://${ref.filename}` : ref.filename);
      refPromises.push(load(nextURL2, { ...options, hint }));
      node.$ref = node.$ref.replace(ref.filename, nextURL2.href);
      return;
    }
    if (options.rootURL.href === VIRTUAL_JSON_URL) {
      error(`Can\u2019t resolve "${ref.filename}" from dynamic JSON. Either load this schema from a filepath/URL, or set the \`cwd\` option: \`openapiTS(schema, { cwd: '/path/to/cwd' })\`.`);
      process.exit(1);
    }
    const nextURL = new import_node_url.URL(ref.filename, schema instanceof import_node_url.URL ? schema : options.rootURL);
    const nextID = relativePath(schema instanceof import_node_url.URL ? schema : options.rootURL, nextURL);
    refPromises.push(load(nextURL, { ...options, hint }));
    node.$ref = node.$ref.replace(ref.filename, nextID);
  });
  await Promise.all(refPromises);
  if (schemaID === ".") {
    for (const subschemaID of Object.keys(options.schemas)) {
      walk(options.schemas[subschemaID].schema, (rawNode) => {
        if (!("$ref" in rawNode) || typeof rawNode.$ref !== "string") return;
        const node = rawNode;
        const ref = parseRef(node.$ref);
        if (ref.filename === ".") {
          if (subschemaID === "." || ref.path[0] === "external") {
            node.$ref = makeTSIndex(ref.path);
          } else {
            node.$ref = makeTSIndex(["external", subschemaID, ...ref.path]);
          }
        } else {
          const refURL = new import_node_url.URL(ref.filename, new import_node_url.URL(subschemaID, options.rootURL));
          node.$ref = makeTSIndex(["external", relativePath(options.rootURL, refURL), ...ref.path]);
        }
      });
    }
  }
  for (const k of Object.keys(options.schemas)) {
    walk(options.schemas[k].schema, (rawNode, nodePath) => {
      if (typeof rawNode === "object" && "in" in rawNode) {
        const key = k === "." ? makeTSIndex(nodePath) : makeTSIndex(["external", k, ...nodePath]);
        options.parameters[key] = rawNode;
      }
    });
  }
  for (const k of Object.keys(options.schemas)) {
    if (JSON.stringify(options.schemas[k].schema).includes('"discriminator"')) {
      walk(options.schemas[k].schema, (rawNode, nodePath) => {
        const node = rawNode;
        if (!node.discriminator) return;
        const discriminator = { ...node.discriminator };
        const oneOf = [];
        if (Array.isArray(node.oneOf)) {
          for (const child of node.oneOf) {
            if (!child || typeof child !== "object" || !("$ref" in child)) continue;
            oneOf.push(child.$ref);
          }
        }
        if (oneOf.length) discriminator.oneOf = oneOf;
        options.discriminators[schemaID === "." ? makeTSIndex(nodePath) : makeTSIndex(["external", k, ...nodePath])] = discriminator;
      });
    }
  }
  return options.schemas;
}
function relativePath(src, dest) {
  const isSameOrigin = dest.protocol.startsWith("http") && src.protocol.startsWith("http") && dest.origin === src.origin;
  const isSameDisk = dest.protocol === "file:" && src.protocol === "file:";
  if (isSameOrigin || isSameDisk) {
    return import_node_path2.default.posix.relative(import_node_path2.default.posix.dirname(src.pathname), dest.pathname);
  }
  return dest.href;
}
function getHint({ path: path2, external, startFrom }) {
  if (startFrom && startFrom !== "OpenAPI3") {
    switch (startFrom) {
      case "OperationObject":
        return getHintFromOperationObject(path2, external);
      case "RequestBodyObject":
        return getHintFromRequestBodyObject(path2, external);
      case "ResponseObject":
        return getHintFromResponseObject(path2, external);
      case "SchemaMap":
        return "SchemaObject";
      default:
        return startFrom;
    }
  }
  switch (path2[0]) {
    case "paths": {
      if (EXT_RE.test(path2[2])) {
        return "SchemaMap";
      }
      return getHintFromPathItemObject(path2.slice(2), external);
    }
    case "components":
      return getHintFromComponentsObject(path2.slice(1), external);
  }
  return void 0;
}
function getHintFromComponentsObject(path2, external) {
  switch (path2[0]) {
    case "schemas":
    case "headers":
      return getHintFromSchemaObject(path2.slice(2), external);
    case "parameters":
      return getHintFromParameterObject(path2.slice(2), external);
    case "responses":
      return getHintFromResponseObject(path2.slice(2), external);
    case "requestBodies":
      return getHintFromRequestBodyObject(path2.slice(2), external);
    case "pathItems":
      return getHintFromPathItemObject(path2.slice(2), external);
  }
  return "SchemaObject";
}
function getHintFromMediaTypeObject(path2, external) {
  switch (path2[0]) {
    case "schema":
      return getHintFromSchemaObject(path2.slice(1), external);
  }
  return "MediaTypeObject";
}
function getHintFromOperationObject(path2, external) {
  switch (path2[0]) {
    case "parameters":
      return "ParameterObject[]";
    case "requestBody":
      return getHintFromRequestBodyObject(path2.slice(1), external);
    case "responses":
      return getHintFromResponseObject(path2.slice(2), external);
  }
  return "OperationObject";
}
function getHintFromParameterObject(path2, external) {
  switch (path2[0]) {
    case "content":
      return getHintFromMediaTypeObject(path2.slice(2), external);
    case "schema":
      return getHintFromSchemaObject(path2.slice(1), external);
  }
  return "ParameterObject";
}
function getHintFromPathItemObject(path2, external) {
  switch (path2[0]) {
    case "parameters": {
      if (typeof path2[1] === "number") {
        return "ParameterObject[]";
      }
      return getHintFromParameterObject(path2.slice(1), external);
    }
    default:
      return getHintFromOperationObject(path2.slice(1), external);
  }
}
function getHintFromRequestBodyObject(path2, external) {
  switch (path2[0]) {
    case "content":
      return getHintFromMediaTypeObject(path2.slice(2), external);
  }
  return "RequestBodyObject";
}
function getHintFromResponseObject(path2, external) {
  switch (path2[0]) {
    case "headers":
      return getHintFromSchemaObject(path2.slice(2), external);
    case "content":
      return getHintFromMediaTypeObject(path2.slice(2), external);
  }
  return "ResponseObject";
}
function getHintFromSchemaObject(path2, external) {
  switch (path2[0]) {
    case "allOf":
    case "anyOf":
    case "oneOf":
      return "SchemaMap";
  }
  if (path2.length >= 2 && external) {
    return "SchemaMap";
  }
  return "SchemaObject";
}

// src/transform/parameter-object.ts
function transformParameterObject(parameterObject, { path: path2, ctx }) {
  return parameterObject.schema ? transformSchemaObject(parameterObject.schema, { path: path2, ctx }) : "string";
}

// src/transform/request-body-object.ts
function transformRequestBodyObject(requestBodyObject, { path: path2, ctx }) {
  let { indentLv } = ctx;
  const output = ["{"];
  indentLv++;
  output.push(indent(ctx.immutableTypes ? tsReadonly("content: {") : "content: {", indentLv));
  indentLv++;
  if (!Object.keys(requestBodyObject.content).length) {
    output.push(indent(`${escStr("*/*")}: never;`, indentLv));
  }
  for (const [contentType, mediaTypeObject] of getEntries(requestBodyObject.content, ctx.alphabetize, ctx.excludeDeprecated)) {
    const c2 = getSchemaObjectComment(mediaTypeObject, indentLv);
    if (c2) output.push(indent(c2, indentLv));
    let key = escStr(contentType);
    if (ctx.immutableTypes) key = tsReadonly(key);
    if ("$ref" in mediaTypeObject) {
      output.push(
        indent(
          `${key}: ${transformSchemaObject(mediaTypeObject, {
            path: `${path2}/${contentType}`,
            ctx: { ...ctx, indentLv }
          })};`,
          indentLv
        )
      );
    } else {
      const mediaType = transformMediaTypeObject(mediaTypeObject, {
        path: `${path2}/${contentType}`,
        ctx: { ...ctx, indentLv }
      });
      output.push(indent(`${key}: ${mediaType};`, indentLv));
    }
  }
  indentLv--;
  output.push(indent("};", indentLv));
  indentLv--;
  output.push(indent("}", indentLv));
  return output.join("\n");
}

// src/transform/response-object.ts
function transformResponseObject(responseObject, { path: path2, ctx }) {
  const output = ["{"];
  let { indentLv } = ctx;
  if (responseObject.headers) {
    indentLv++;
    output.push(indent(`headers: {`, indentLv));
    indentLv++;
    for (const [name, headerObject] of getEntries(responseObject.headers, ctx.alphabetize, ctx.excludeDeprecated)) {
      const c2 = getSchemaObjectComment(headerObject, indentLv);
      if (c2) output.push(indent(c2, indentLv));
      let key = escObjKey(name);
      if (ctx.immutableTypes) key = tsReadonly(key);
      if ("$ref" in headerObject) {
        output.push(indent(`${key}: ${headerObject.$ref};`, indentLv));
      } else {
        if (!headerObject.required) key = tsOptionalProperty(key);
        output.push(
          indent(
            `${key}: ${transformHeaderObject(headerObject, {
              path: `${path2}/headers/${name}`,
              ctx: { ...ctx, indentLv }
            })};`,
            indentLv
          )
        );
      }
    }
    indentLv--;
    output.push(indent(`};`, indentLv));
    indentLv--;
  }
  if (responseObject.content) {
    indentLv++;
    output.push(indent("content: {", indentLv));
    indentLv++;
    for (const [contentType, mediaTypeObject] of getEntries(responseObject.content, ctx.alphabetize, ctx.excludeDeprecated)) {
      let key = escStr(contentType);
      if (ctx.immutableTypes) key = tsReadonly(key);
      output.push(
        indent(
          `${key}: ${transformMediaTypeObject(mediaTypeObject, {
            path: `${path2}/content/${contentType}`,
            ctx: { ...ctx, indentLv }
          })};`,
          indentLv
        )
      );
    }
    indentLv--;
    output.push(indent("};", indentLv));
    indentLv--;
  } else {
    indentLv++;
    output.push(indent("content: never;", indentLv));
    indentLv--;
  }
  output.push(indent("}", indentLv));
  return output.join("\n");
}

// src/transform/operation-object.ts
function transformOperationObject(operationObject, { path: path2, ctx, wrapObject = true }) {
  let { indentLv } = ctx;
  const output = wrapObject ? ["{"] : [];
  indentLv++;
  {
    if (operationObject.parameters) {
      const parametersArray = getParametersArray(operationObject.parameters);
      const parameterOutput = [];
      indentLv++;
      for (const paramIn of ["query", "header", "path", "cookie"]) {
        const paramInternalOutput = [];
        indentLv++;
        let paramInOptional = true;
        for (const param of parametersArray) {
          const node = "$ref" in param ? ctx.parameters[param.$ref] : param;
          if ((node == null ? void 0 : node.in) !== paramIn) continue;
          let key = escObjKey(node.name);
          const isRequired = paramIn === "path" || !!node.required;
          if (isRequired) {
            paramInOptional = false;
          } else {
            key = tsOptionalProperty(key);
          }
          const c2 = getSchemaObjectComment(param, indentLv);
          if (c2) paramInternalOutput.push(indent(c2, indentLv));
          const parameterType = "$ref" in param ? param.$ref : transformParameterObject(param, {
            path: `${path2}/parameters/${param.name}`,
            ctx: { ...ctx, indentLv }
          });
          paramInternalOutput.push(indent(`${key}: ${parameterType};`, indentLv));
        }
        indentLv--;
        if (paramInternalOutput.length) {
          const key = paramInOptional ? tsOptionalProperty(paramIn) : paramIn;
          parameterOutput.push(indent(`${key}: {`, indentLv));
          parameterOutput.push(...paramInternalOutput);
          parameterOutput.push(indent(`};`, indentLv));
        }
      }
      indentLv--;
      if (parameterOutput.length) {
        output.push(indent(`parameters: {`, indentLv));
        output.push(parameterOutput.join("\n"));
        output.push(indent("};", indentLv));
      }
    }
  }
  {
    if (operationObject.requestBody) {
      const c2 = getSchemaObjectComment(operationObject.requestBody, indentLv);
      if (c2) output.push(indent(c2, indentLv));
      let key = "requestBody";
      if (ctx.immutableTypes) key = tsReadonly(key);
      if ("$ref" in operationObject.requestBody) {
        output.push(indent(`${key}: ${transformSchemaObject(operationObject.requestBody, { path: path2, ctx })};`, indentLv));
      } else {
        if (!operationObject.requestBody.required) key = tsOptionalProperty(key);
        const requestBody = transformRequestBodyObject(operationObject.requestBody, {
          path: `${path2}/requestBody`,
          ctx: { ...ctx, indentLv }
        });
        output.push(indent(`${key}: ${requestBody};`, indentLv));
      }
    }
  }
  {
    if (operationObject.responses) {
      output.push(indent(`responses: {`, indentLv));
      indentLv++;
      for (const [responseCode, responseObject] of getEntries(operationObject.responses, ctx.alphabetize, ctx.excludeDeprecated)) {
        const key = escObjKey(responseCode);
        const c2 = getSchemaObjectComment(responseObject, indentLv);
        if (c2) output.push(indent(c2, indentLv));
        if ("$ref" in responseObject) {
          output.push(
            indent(
              `${key}: ${transformSchemaObject(responseObject, {
                path: `${path2}/responses/${responseCode}`,
                ctx
              })};`,
              indentLv
            )
          );
        } else {
          const responseType = transformResponseObject(responseObject, {
            path: `${path2}/responses/${responseCode}`,
            ctx: { ...ctx, indentLv }
          });
          output.push(indent(`${key}: ${responseType};`, indentLv));
        }
      }
      indentLv--;
      output.push(indent(`};`, indentLv));
    }
  }
  indentLv--;
  if (wrapObject) {
    output.push(indent("}", indentLv));
  }
  return output.join("\n");
}

// src/transform/path-item-object.ts
function transformPathItemObject(pathItem, { path: path2, ctx }) {
  var _a, _b;
  let { indentLv } = ctx;
  const output = [];
  output.push("{");
  indentLv++;
  for (const method of ["get", "put", "post", "delete", "options", "head", "patch", "trace"]) {
    const operationObject = pathItem[method];
    if (!operationObject) continue;
    const c2 = getSchemaObjectComment(operationObject, indentLv);
    if (c2) output.push(indent(c2, indentLv));
    const keyedParameters = {};
    if (!("$ref" in operationObject)) {
      for (const parameter of [...(_a = pathItem.parameters) != null ? _a : [], ...getParametersArray(operationObject.parameters)]) {
        keyedParameters["$ref" in parameter ? parameter.$ref : `${parameter.in}/${parameter.name}`] = parameter;
      }
    }
    if ("$ref" in operationObject) {
      output.push(indent(`${method}: ${operationObject.$ref}`, indentLv));
    } else if (operationObject.operationId) {
      const operationType = transformOperationObject({ ...operationObject, parameters: Object.values(keyedParameters) }, { path: path2, ctx: { ...ctx, indentLv: 1 } });
      ctx.operations[operationObject.operationId] = {
        operationType,
        comment: getSchemaObjectComment(operationObject, 1)
      };
      output.push(indent(`${method}: operations[${escStr(operationObject.operationId)}];`, indentLv));
    } else {
      const operationType = transformOperationObject({ ...operationObject, parameters: Object.values(keyedParameters) }, { path: path2, ctx: { ...ctx, indentLv } });
      output.push(indent(`${method}: ${operationType};`, indentLv));
    }
  }
  if ((_b = pathItem.parameters) == null ? void 0 : _b.length) {
    output.push(indent(transformOperationObject({ parameters: pathItem.parameters }, { path: path2, ctx, wrapObject: false }).trim(), indentLv));
  }
  indentLv--;
  output.push(indent("}", indentLv));
  return output.join("\n");
}

// src/transform/schema-object-map.ts
function transformSchemaObjectMap(schemaObjMap, { path: path2, ctx }) {
  if ("type" in schemaObjMap && typeof schemaObjMap.type === "string" || "allOf" in schemaObjMap && Array.isArray(schemaObjMap.allOf) || "oneOf" in schemaObjMap && Array.isArray(schemaObjMap.oneOf) || "anyOf" in schemaObjMap && Array.isArray(schemaObjMap.anyOf)) {
    return transformSchemaObject(schemaObjMap, { path: path2, ctx });
  }
  let { indentLv } = ctx;
  const output = ["{"];
  indentLv++;
  outer: for (const [name, schemaObject] of getEntries(schemaObjMap, ctx.alphabetize, ctx.excludeDeprecated)) {
    if (!schemaObject || typeof schemaObject !== "object") continue;
    const c2 = getSchemaObjectComment(schemaObject, indentLv);
    if (c2) output.push(indent(c2, indentLv));
    let key = escObjKey(name);
    if (ctx.immutableTypes || schemaObject.readOnly) key = tsReadonly(key);
    if (!("type" in schemaObject) && !("$ref" in schemaObject)) {
      for (const method of ["get", "put", "post", "delete", "options", "head", "patch", "trace"]) {
        if (method in schemaObject) {
          output.push(indent(`${key}: ${transformPathItemObject(schemaObject, { path: `${path2}${name}`, ctx: { ...ctx, indentLv } })};`, indentLv));
          continue outer;
        }
      }
    }
    if ("in" in schemaObject) {
      output.push(indent(`${key}: ${transformParameterObject(schemaObject, { path: `${path2}${name}`, ctx: { ...ctx, indentLv } })};`, indentLv));
      continue;
    }
    output.push(indent(`${key}: ${transformSchemaObject(schemaObject, { path: `${path2}${name}`, ctx: { ...ctx, indentLv } })};`, indentLv));
  }
  indentLv--;
  output.push(indent("}", indentLv));
  return output.join("\n");
}

// src/transform/schema-object.ts
function transformSchemaObject(schemaObject, options) {
  const result = defaultSchemaObjectTransform(schemaObject, options);
  if (typeof options.ctx.postTransform === "function") {
    const postResult = options.ctx.postTransform(result, options);
    if (postResult) return postResult;
  }
  return result;
}
function defaultSchemaObjectTransform(schemaObject, { path: path2, ctx }) {
  var _a, _b, _c;
  let { indentLv } = ctx;
  if (typeof schemaObject === "boolean") {
    return schemaObject ? "unknown" : "never";
  }
  if (!schemaObject || typeof schemaObject !== "object") return schemaObject;
  if (Array.isArray(schemaObject)) {
    const finalType2 = tsTupleOf(...schemaObject);
    return ctx.immutableTypes ? tsReadonly(finalType2) : finalType2;
  }
  if ("$ref" in schemaObject) {
    return schemaObject.$ref;
  }
  if (typeof ctx.transform === "function") {
    const result = ctx.transform(schemaObject, { path: path2, ctx });
    if (result) return result;
  }
  if (schemaObject.const !== null && schemaObject.const !== void 0) {
    return transformSchemaObject(escStr(schemaObject.const), {
      path: path2,
      ctx: { ...ctx, immutableTypes: false, indentLv: indentLv + 1 }
      // note: guarantee readonly happens once, here
    });
  }
  if (typeof schemaObject === "object" && !!schemaObject.enum && schemaObject.type !== "object") {
    let items = schemaObject.enum;
    if ("type" in schemaObject) {
      if (schemaObject.type === "string" || Array.isArray(schemaObject.type) && schemaObject.type.includes("string")) {
        items = items.map((t) => escStr(t));
      }
    } else {
      items = items.map((t) => escStr(t || ""));
    }
    return tsUnionOf(...items, ...schemaObject.nullable ? ["null"] : []);
  }
  const oneOf = typeof schemaObject === "object" && !schemaObject.discriminator && schemaObject.oneOf || schemaObject.enum || void 0;
  if (oneOf && !oneOf.some((t) => "$ref" in t && ctx.discriminators[t.$ref])) {
    const oneOfNormalized = oneOf.map((item) => transformSchemaObject(item, { path: path2, ctx }));
    if (schemaObject.nullable) oneOfNormalized.push("null");
    if ("type" in schemaObject && Array.isArray(schemaObject.type)) {
      const coreTypes = schemaObject.type.map((t) => transformSchemaObject({ ...schemaObject, oneOf: void 0, type: t }, { path: path2, ctx }));
      return tsUnionOf(...oneOfNormalized, ...coreTypes);
    }
    const oneOfTypes = oneOfNormalized.some((t) => typeof t === "string" && t.includes("{")) ? tsOneOf(...oneOfNormalized) : tsUnionOf(...oneOfNormalized);
    if ("type" in schemaObject && schemaObject.type === "object" && (schemaObject.properties || schemaObject.additionalProperties)) {
      return tsIntersectionOf(transformSchemaObject({ ...schemaObject, oneOf: void 0, enum: void 0 }, { path: path2, ctx }), oneOfTypes);
    }
    return oneOfTypes;
  }
  if ("type" in schemaObject) {
    if (schemaObject.type === "null") return "null";
    if (schemaObject.type === "string" || schemaObject.type === "boolean") {
      return schemaObject.nullable ? tsUnionOf(schemaObject.type, "null") : schemaObject.type;
    }
    if (schemaObject.type === "number" || schemaObject.type === "integer") {
      return schemaObject.nullable ? tsUnionOf("number", "null") : "number";
    }
    if (schemaObject.type === "array") {
      indentLv++;
      let itemType = "unknown";
      let isTupleType = false;
      if (schemaObject.prefixItems || Array.isArray(schemaObject.items)) {
        isTupleType = true;
        const result = [];
        for (const item of (_a = schemaObject.prefixItems) != null ? _a : schemaObject.items) {
          result.push(transformSchemaObject(item, { path: path2, ctx: { ...ctx, indentLv } }));
        }
        itemType = `[${result.join(", ")}]`;
      } else if (schemaObject.items) {
        itemType = transformSchemaObject(schemaObject.items, { path: path2, ctx: { ...ctx, indentLv } });
      }
      const min = typeof schemaObject.minItems === "number" && schemaObject.minItems >= 0 ? schemaObject.minItems : 0;
      const max = typeof schemaObject.maxItems === "number" && schemaObject.maxItems >= 0 && min <= schemaObject.maxItems ? schemaObject.maxItems : void 0;
      const estimateCodeSize = typeof max !== "number" ? min : (max * (max + 1) - min * (min - 1)) / 2;
      if (ctx.supportArrayLength && (min !== 0 || max !== void 0) && estimateCodeSize < 30) {
        if (typeof schemaObject.maxItems !== "number") {
          itemType = tsTupleOf(...Array.from({ length: min }).map(() => itemType), `...${tsArrayOf(itemType)}`);
          return ctx.immutableTypes || schemaObject.readOnly ? tsReadonly(itemType) : itemType;
        } else {
          return tsUnionOf(
            ...Array.from({ length: (max != null ? max : 0) - min + 1 }).map((_, i) => i + min).map((n) => {
              const t = tsTupleOf(...Array.from({ length: n }).map(() => itemType));
              return ctx.immutableTypes || schemaObject.readOnly ? tsReadonly(t) : t;
            })
          );
        }
      }
      if (!isTupleType) {
        itemType = tsArrayOf(itemType);
      }
      itemType = ctx.immutableTypes || schemaObject.readOnly ? tsReadonly(itemType) : itemType;
      return schemaObject.nullable ? tsUnionOf(itemType, "null") : itemType;
    }
    if (Array.isArray(schemaObject.type)) {
      return tsUnionOf(...schemaObject.type.map((t) => transformSchemaObject({ ...schemaObject, type: t }, { path: path2, ctx })));
    }
  }
  const coreType = [];
  for (const k of ["oneOf", "allOf", "anyOf"]) {
    if (!schemaObject[k]) continue;
    const discriminatorRef = schemaObject[k].find(
      (t) => "$ref" in t && (ctx.discriminators[t.$ref] || // explicit allOf from this node
      Object.values(ctx.discriminators).find((d) => {
        var _a2;
        return (_a2 = d.oneOf) == null ? void 0 : _a2.includes(path2);
      }))
      // implicit oneOf from parent
    );
    if (discriminatorRef && ctx.discriminators[discriminatorRef.$ref]) {
      coreType.unshift(indent(getDiscriminatorPropertyName(path2, ctx.discriminators[discriminatorRef.$ref]), indentLv + 1));
      break;
    }
  }
  for (const d of Object.values(ctx.discriminators)) {
    if ((_b = d.oneOf) == null ? void 0 : _b.includes(path2)) {
      coreType.unshift(indent(getDiscriminatorPropertyName(path2, d), indentLv + 1));
      break;
    }
  }
  if ("properties" in schemaObject && schemaObject.properties && Object.keys(schemaObject.properties).length || "additionalProperties" in schemaObject && schemaObject.additionalProperties || "$defs" in schemaObject && schemaObject.$defs) {
    indentLv++;
    for (const [k, v] of getEntries((_c = schemaObject.properties) != null ? _c : {}, ctx.alphabetize, ctx.excludeDeprecated)) {
      const c2 = getSchemaObjectComment(v, indentLv);
      if (c2) coreType.push(indent(c2, indentLv));
      let key = escObjKey(k);
      let isOptional = !Array.isArray(schemaObject.required) || !schemaObject.required.includes(k);
      if (isOptional && ctx.defaultNonNullable && "default" in v) isOptional = false;
      if (isOptional) key = tsOptionalProperty(key);
      if (ctx.immutableTypes || schemaObject.readOnly) key = tsReadonly(key);
      coreType.push(indent(`${key}: ${transformSchemaObject(v, { path: path2, ctx: { ...ctx, indentLv } })};`, indentLv));
    }
    if (schemaObject.additionalProperties || ctx.additionalProperties) {
      let addlType = "unknown";
      if (typeof schemaObject.additionalProperties === "object") {
        if (!Object.keys(schemaObject.additionalProperties).length) {
          addlType = "unknown";
        } else {
          addlType = transformSchemaObject(schemaObject.additionalProperties, {
            path: path2,
            ctx: { ...ctx, indentLv }
          });
        }
      }
      const numProperties = schemaObject.properties ? Object.keys(schemaObject.properties).length : 0;
      if (schemaObject.properties && (!schemaObject.required && numProperties || schemaObject.required && numProperties !== schemaObject.required.length)) {
        coreType.push(indent(`[key: string]: ${tsUnionOf(addlType ? addlType : "unknown", "undefined")};`, indentLv));
      } else {
        coreType.push(indent(`[key: string]: ${addlType ? addlType : "unknown"};`, indentLv));
      }
    }
    if (schemaObject.$defs && typeof schemaObject.$defs === "object" && Object.keys(schemaObject.$defs).length) {
      coreType.push(indent(`$defs: ${transformSchemaObjectMap(schemaObject.$defs, { path: `${path2}$defs/`, ctx: { ...ctx, indentLv } })};`, indentLv));
    }
    indentLv--;
  }
  let finalType = coreType.length ? `{
${coreType.join("\n")}
${indent("}", indentLv)}` : "";
  function collectCompositions(items) {
    const output = [];
    for (const item of items) {
      const itemType = transformSchemaObject(item, { path: path2, ctx: { ...ctx, indentLv } });
      if ("$ref" in item && ctx.discriminators[item.$ref]) {
        output.push(tsOmit(itemType, [ctx.discriminators[item.$ref].propertyName]));
        continue;
      }
      output.push(itemType);
    }
    return output;
  }
  if (Array.isArray(schemaObject.oneOf) && schemaObject.oneOf.length) {
    const oneOfType = tsUnionOf(...collectCompositions(schemaObject.oneOf));
    finalType = finalType ? tsIntersectionOf(finalType, oneOfType) : oneOfType;
  } else {
    if (Array.isArray(schemaObject.allOf) && schemaObject.allOf.length) {
      finalType = tsIntersectionOf(...finalType ? [finalType] : [], ...collectCompositions(schemaObject.allOf));
      if ("required" in schemaObject && Array.isArray(schemaObject.required)) {
        finalType = tsWithRequired(finalType, schemaObject.required);
      }
    }
    if (Array.isArray(schemaObject.anyOf) && schemaObject.anyOf.length) {
      const anyOfTypes = tsUnionOf(...collectCompositions(schemaObject.anyOf));
      finalType = finalType ? tsIntersectionOf(finalType, anyOfTypes) : anyOfTypes;
    }
  }
  if (schemaObject.nullable) {
    if ("type" in schemaObject) {
      finalType = tsUnionOf(finalType || "Record<string, unknown>", "null");
    } else {
      finalType = tsUnionOf(finalType || "unknown", "null");
    }
  }
  if (finalType) return finalType;
  if (!("type" in schemaObject)) return "unknown";
  return ctx.emptyObjectsUnknown ? "Record<string, unknown>" : "Record<string, never>";
}
function getDiscriminatorPropertyName(path2, discriminator) {
  let value = parseRef(path2).path.pop();
  if (discriminator.mapping) {
    const matchedValue = Object.entries(discriminator.mapping).find(([, v]) => !v.startsWith("#") && v === value || v.startsWith("#") && parseRef(v).path.pop() === value);
    if (matchedValue) value = matchedValue[0];
  }
  return `${escObjKey(discriminator.propertyName)}: ${escStr(value)};`;
}

// src/transform/media-type-object.ts
function transformMediaTypeObject(mediaTypeObject, { path: path2, ctx }) {
  if (!mediaTypeObject.schema) return "unknown";
  return transformSchemaObject(mediaTypeObject.schema, { path: path2, ctx });
}

// src/transform/header-object.ts
function transformHeaderObject(headerObject, { path: path2, ctx }) {
  if (headerObject.schema) return transformSchemaObject(headerObject.schema, { path: path2, ctx });
  if (headerObject.content) {
    let { indentLv } = ctx;
    const output = ["{"];
    indentLv++;
    for (const [contentType, mediaTypeObject] of getEntries(headerObject.content, ctx.alphabetize, ctx.excludeDeprecated)) {
      const c2 = getSchemaObjectComment(mediaTypeObject, indentLv);
      if (c2) output.push(indent(c2, indentLv));
      let key = escStr(contentType);
      if (ctx.immutableTypes) key = tsReadonly(key);
      if ("$ref" in mediaTypeObject) {
        output.push(indent(`${key}: ${transformSchemaObject(mediaTypeObject, { path: `${path2}/${contentType}`, ctx })};`, indentLv));
      } else {
        const mediaType = transformMediaTypeObject(mediaTypeObject, { path: `${path2}/${contentType}`, ctx });
        output.push(indent(`${key}: ${mediaType};`, indentLv));
      }
    }
    indentLv--;
    output.push(indent("}", indentLv));
    return output.join("\n");
  }
  return "unknown";
}

// src/transform/components-object.ts
function transformComponentsObject(components, ctx) {
  let { indentLv } = ctx;
  const output = ["{"];
  indentLv++;
  if (components.schemas) {
    const schemas = transformSchemaObjectMap(components.schemas, { path: "#/components/schemas/", ctx: { ...ctx, indentLv } });
    output.push(indent(`schemas: ${schemas};`, indentLv));
  } else {
    output.push(indent("schemas: never;", indentLv));
  }
  if (components.responses) {
    output.push(indent("responses: {", indentLv));
    indentLv++;
    for (const [name, responseObject] of getEntries(components.responses, ctx.alphabetize, ctx.excludeDeprecated)) {
      const c2 = getSchemaObjectComment(responseObject, indentLv);
      if (c2) output.push(indent(c2, indentLv));
      let key = escObjKey(name);
      if (ctx.immutableTypes) key = tsReadonly(key);
      if ("$ref" in responseObject) {
        output.push(indent(`${key}: ${transformSchemaObject(responseObject, { path: `#/components/responses/${name}`, ctx })};`, indentLv));
      } else {
        const responseType = transformResponseObject(responseObject, {
          path: `#/components/responses/${name}`,
          ctx: { ...ctx, indentLv }
        });
        output.push(indent(`${key}: ${responseType};`, indentLv));
      }
    }
    indentLv--;
    output.push(indent("};", indentLv));
  } else {
    output.push(indent("responses: never;", indentLv));
  }
  if (components.parameters) {
    const parameters = [];
    indentLv++;
    for (const [name, parameterObject] of getEntries(components.parameters, ctx.alphabetize, ctx.excludeDeprecated)) {
      const c2 = getSchemaObjectComment(parameterObject, indentLv);
      if (c2) parameters.push(indent(c2, indentLv));
      let key = escObjKey(name);
      if (ctx.immutableTypes) key = tsReadonly(key);
      if ("$ref" in parameterObject) {
        parameters.push(indent(`${key}: ${transformSchemaObject(parameterObject, { path: `#/components/parameters/${name}`, ctx })};`, indentLv));
      } else {
        if (parameterObject.in !== "path" && !parameterObject.required) {
          key = tsOptionalProperty(key);
        }
        const parameterType = transformParameterObject(parameterObject, {
          path: `#/components/parameters/${name}`,
          ctx: { ...ctx, indentLv }
        });
        parameters.push(indent(`${key}: ${parameterType};`, indentLv));
      }
    }
    indentLv--;
    output.push(indent(`parameters: {`, indentLv), ...parameters, indent("};", indentLv));
  } else {
    output.push(indent("parameters: never;", indentLv));
  }
  if (components.requestBodies) {
    output.push(indent("requestBodies: {", indentLv));
    indentLv++;
    for (const [name, requestBodyObject] of getEntries(components.requestBodies, ctx.alphabetize, ctx.excludeDeprecated)) {
      const c2 = getSchemaObjectComment(requestBodyObject, indentLv);
      if (c2) output.push(indent(c2, indentLv));
      let key = escObjKey(name);
      if ("$ref" in requestBodyObject) {
        if (ctx.immutableTypes) key = tsReadonly(key);
        output.push(
          indent(
            `${key}: ${transformSchemaObject(requestBodyObject, {
              path: `#/components/requestBodies/${name}`,
              ctx: { ...ctx, indentLv }
            })};`,
            indentLv
          )
        );
      } else {
        if (!requestBodyObject.required) key = tsOptionalProperty(key);
        if (ctx.immutableTypes) key = tsReadonly(key);
        const requestBodyType = transformRequestBodyObject(requestBodyObject, {
          path: `#/components/requestBodies/${name}`,
          ctx: { ...ctx, indentLv }
        });
        output.push(indent(`${key}: ${requestBodyType};`, indentLv));
      }
    }
    indentLv--;
    output.push(indent("};", indentLv));
  } else {
    output.push(indent("requestBodies: never;", indentLv));
  }
  if (components.headers) {
    output.push(indent("headers: {", indentLv));
    indentLv++;
    for (const [name, headerObject] of getEntries(components.headers, ctx.alphabetize, ctx.excludeDeprecated)) {
      const c2 = getSchemaObjectComment(headerObject, indentLv);
      if (c2) output.push(indent(c2, indentLv));
      let key = escObjKey(name);
      if (ctx.immutableTypes) key = tsReadonly(key);
      if ("$ref" in headerObject) {
        output.push(indent(`${key}: ${transformSchemaObject(headerObject, { path: `#/components/headers/${name}`, ctx })};`, indentLv));
      } else {
        const headerType = transformHeaderObject(headerObject, {
          path: `#/components/headers/${name}`,
          ctx: { ...ctx, indentLv }
        });
        output.push(indent(`${key}: ${headerType};`, indentLv));
      }
    }
    indentLv--;
    output.push(indent("};", indentLv));
  } else {
    output.push(indent("headers: never;", indentLv));
  }
  if (components.pathItems) {
    output.push(indent("pathItems: {", indentLv));
    indentLv++;
    for (const [name, pathItemObject] of getEntries(components.pathItems, ctx.alphabetize, ctx.excludeDeprecated)) {
      let key = escObjKey(name);
      if (ctx.immutableTypes) key = tsReadonly(key);
      if ("$ref" in pathItemObject) {
        const c2 = getSchemaObjectComment(pathItemObject, indentLv);
        if (c2) output.push(indent(c2, indentLv));
        output.push(indent(`${key}: ${transformSchemaObject(pathItemObject, { path: `#/components/pathItems/${name}`, ctx })};`, indentLv));
      } else {
        output.push(
          indent(
            `${key}: ${transformPathItemObject(pathItemObject, {
              path: `#/components/pathItems/${name}`,
              ctx: { ...ctx, indentLv }
            })};`,
            indentLv
          )
        );
      }
    }
    indentLv--;
    output.push(indent("};", indentLv));
  } else {
    output.push(indent("pathItems: never;", indentLv));
  }
  indentLv--;
  output.push(indent("}", indentLv));
  return output.join("\n");
}

// src/transform/paths-object.ts
var OPERATIONS = ["get", "post", "put", "delete", "options", "head", "patch", "trace"];
function extractPathParams(obj) {
  const params = /* @__PURE__ */ new Map();
  if (obj && "parameters" in obj) {
    for (const p of getParametersArray(obj.parameters)) {
      if ("in" in p && p.in === "path") params.set(p.name, p);
    }
  }
  return params;
}
function transformPathsObject(pathsObject, ctx) {
  let { indentLv } = ctx;
  const output = ["{"];
  indentLv++;
  for (const [url, pathItemObject] of getEntries(pathsObject, ctx.alphabetize, ctx.excludeDeprecated)) {
    if (!pathItemObject || typeof pathItemObject !== "object") continue;
    let path2 = url;
    if ("$ref" in pathItemObject) {
      const c2 = getSchemaObjectComment(pathItemObject, indentLv);
      if (c2) output.push(indent(c2, indentLv));
      output.push(indent(`${escStr(path2)}: ${pathItemObject.$ref};`, indentLv));
      continue;
    }
    const pathParams = new Map([...extractPathParams(pathItemObject), ...OPERATIONS.flatMap((op) => Array.from(extractPathParams(pathItemObject[op])))]);
    if (ctx.pathParamsAsTypes && pathParams.size) {
      for (const p of pathParams.values()) {
        const paramType = transformParameterObject(p, { path: `#/paths/${url}/parameters/${p.name}`, ctx });
        path2 = path2.replace(`{${p.name}}`, `\${${paramType}}`);
      }
      path2 = `[path: \`${path2}\`]`;
    } else {
      path2 = escStr(path2);
    }
    output.push(
      indent(
        `${path2}: ${transformPathItemObject(pathItemObject, {
          path: `#/paths/${url}`,
          ctx: { ...ctx, indentLv }
        })};`,
        indentLv
      )
    );
  }
  indentLv--;
  output.push(indent("}", indentLv));
  return output.join("\n");
}

// src/transform/webhooks-object.ts
function transformWebhooksObject(webhooksObject, ctx) {
  let { indentLv } = ctx;
  const output = ["{"];
  indentLv++;
  for (const [name, pathItemObject] of getEntries(webhooksObject, ctx.alphabetize, ctx.excludeDeprecated)) {
    output.push(
      indent(
        `${escStr(name)}: ${transformPathItemObject(pathItemObject, {
          path: `#/webhooks/${name}`,
          ctx: { ...ctx, indentLv }
        })};`,
        indentLv
      )
    );
  }
  indentLv--;
  output.push(indent("}", indentLv));
  return output.join("\n");
}

// src/transform/index.ts
function transformSchema(schema, ctx) {
  if (!schema) return {};
  const output = {};
  if (schema.paths) output.paths = transformPathsObject(schema.paths, ctx);
  else output.paths = "";
  if (schema.webhooks) output.webhooks = transformWebhooksObject(schema.webhooks, ctx);
  else output.webhooks = "";
  if (schema.components) output.components = transformComponentsObject(schema.components, ctx);
  else output.components = "";
  if (schema.$defs) output.$defs = transformSchemaObjectMap(schema.$defs, { path: "$defs/", ctx });
  else output.$defs = "";
  return output;
}

// src/transform/parameter-object-array.ts
function transformParameterObjectArray(parameterObjectArray, { path: path2, ctx }) {
  const output = [];
  const parameters = Array.isArray(parameterObjectArray) ? parameterObjectArray.map((p) => [p.name, p]) : Object.entries(parameterObjectArray);
  for (const [id, param] of parameters) {
    let key = escObjKey(id);
    if (ctx.immutableTypes) key = tsReadonly(key);
    const node = "$ref" in param ? ctx.parameters[param.$ref] : param;
    if (!node) continue;
    if (node.in !== "path" && !node.required) key = tsOptionalProperty(key);
    output.push(
      indent(
        `${key}: ${transformParameterObject(node, {
          path: `${path2}/${node.in}/${node.name}`,
          ctx: { ...ctx, indentLv: ctx.indentLv + 1 }
        })};`,
        ctx.indentLv
      )
    );
  }
  return output.join("\n");
}

// src/index.ts
var EMPTY_OBJECT_RE = /^\s*\{?\s*\}?\s*$/;
var COMMENT_HEADER = `/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

`;
async function openapiTS(schema, options = {}) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k;
  const ctx = {
    additionalProperties: (_a = options.additionalProperties) != null ? _a : false,
    alphabetize: (_b = options.alphabetize) != null ? _b : false,
    cwd: (_c = options.cwd) != null ? _c : new import_node_url2.URL(`file://${process.cwd()}/`),
    defaultNonNullable: (_d = options.defaultNonNullable) != null ? _d : false,
    discriminators: {},
    transform: typeof options.transform === "function" ? options.transform : void 0,
    postTransform: typeof options.postTransform === "function" ? options.postTransform : void 0,
    immutableTypes: (_e = options.immutableTypes) != null ? _e : false,
    emptyObjectsUnknown: (_f = options.emptyObjectsUnknown) != null ? _f : false,
    indentLv: 0,
    operations: {},
    pathParamsAsTypes: (_g = options.pathParamsAsTypes) != null ? _g : false,
    parameters: {},
    silent: (_h = options.silent) != null ? _h : false,
    supportArrayLength: (_i = options.supportArrayLength) != null ? _i : false,
    excludeDeprecated: (_j = options.excludeDeprecated) != null ? _j : false
  };
  const allSchemas = {};
  const schemaURL = typeof schema === "string" ? resolveSchema(schema) : schema;
  let rootURL = schemaURL;
  const isInlineSchema = typeof schema !== "string" && schema instanceof import_node_url2.URL === false;
  if (isInlineSchema) {
    if (ctx.cwd) {
      if (ctx.cwd instanceof import_node_url2.URL) {
        rootURL = ctx.cwd;
      } else if (typeof ctx.cwd === "string") {
        rootURL = new import_node_url2.URL(ctx.cwd, `file://${process.cwd()}/`);
      }
      rootURL = new import_node_url2.URL("root.yaml", rootURL);
    } else {
      rootURL = new import_node_url2.URL(VIRTUAL_JSON_URL);
    }
  }
  await load(schemaURL, {
    ...ctx,
    auth: options.auth,
    schemas: allSchemas,
    rootURL,
    urlCache: /* @__PURE__ */ new Set(),
    httpHeaders: options.httpHeaders,
    httpMethod: options.httpMethod,
    fetch: (_k = options.fetch) != null ? _k : getDefaultFetch()
  });
  for (const k of Object.keys(allSchemas)) {
    const subschema = allSchemas[k];
    if (typeof subschema.schema.swagger === "string") {
      error("Swagger 2.0 and older no longer supported. Please use v5.");
      process.exit(1);
    }
    if (subschema.hint === "OpenAPI3" && typeof subschema.schema.openapi === "string") {
      if (parseInt(subschema.schema.openapi) !== 3) {
        error(`Unsupported OpenAPI version "${subschema.schema.openapi}". Only 3.x is supported.`);
        process.exit(1);
      }
    }
  }
  const output = [];
  if ("commentHeader" in options) {
    if (options.commentHeader) output.push(options.commentHeader);
  } else {
    output.push(COMMENT_HEADER);
  }
  if (options.inject) output.push(options.inject);
  const rootTypes = transformSchema(allSchemas["."].schema, ctx);
  for (const k of Object.keys(rootTypes)) {
    if (rootTypes[k] && !EMPTY_OBJECT_RE.test(rootTypes[k])) {
      output.push(options.exportType ? `export type ${k} = ${rootTypes[k]};` : `export interface ${k} ${rootTypes[k]}`, "");
    } else {
      output.push(`export type ${k} = Record<string, never>;`, "");
    }
    delete rootTypes[k];
    delete allSchemas["."];
  }
  const externalKeys = Object.keys(allSchemas);
  if (externalKeys.length) {
    let indentLv = 0;
    output.push(options.exportType ? "export type external = {" : "export interface external {");
    externalKeys.sort((a, b) => a.localeCompare(b, "en", { numeric: true }));
    indentLv++;
    for (const subschemaID of externalKeys) {
      const subschema = allSchemas[subschemaID];
      const key = escObjKey(subschemaID);
      const path2 = `${subschemaID}#`;
      let subschemaOutput = "";
      let comment2;
      switch (subschema.hint) {
        case "OpenAPI3": {
          const subschemaTypes = transformSchema(subschema.schema, { ...ctx, indentLv: indentLv + 1 });
          if (!Object.keys(subschemaTypes).length) break;
          output.push(indent(`${key}: {`, indentLv));
          indentLv++;
          for (const [k, v] of getEntries(subschemaTypes, options.alphabetize, options.excludeDeprecated)) {
            if (EMPTY_OBJECT_RE.test(v)) output.push(indent(`${escObjKey(k)}: Record<string, never>;`, indentLv));
            else output.push(indent(`${escObjKey(k)}: ${v};`, indentLv));
          }
          indentLv--;
          output.push(indent("};", indentLv));
          break;
        }
        case "MediaTypeObject": {
          subschemaOutput = transformMediaTypeObject(subschema.schema, { path: path2, ctx: { ...ctx, indentLv } });
          break;
        }
        case "OperationObject": {
          comment2 = getSchemaObjectComment(subschema.schema, indentLv);
          subschemaOutput = transformOperationObject(subschema.schema, { path: path2, ctx: { ...ctx, indentLv } });
          break;
        }
        case "ParameterObject": {
          subschemaOutput = transformParameterObject(subschema.schema, { path: path2, ctx: { ...ctx, indentLv } });
          break;
        }
        case "ParameterObject[]": {
          if (typeof subschema.schema === "object" && ("schema" in subschema.schema || "type" in subschema.schema)) {
            subschemaOutput = transformSchemaObject(subschema.schema, { path: path2, ctx: { ...ctx, indentLv } });
          } else {
            subschemaOutput += "{\n";
            indentLv++;
            subschemaOutput += transformParameterObjectArray(subschema.schema, { path: path2, ctx: { ...ctx, indentLv } });
            subschemaOutput += "\n";
            indentLv--;
            subschemaOutput += indent("};", indentLv);
          }
          break;
        }
        case "RequestBodyObject": {
          subschemaOutput = `${transformRequestBodyObject(subschema.schema, { path: path2, ctx: { ...ctx, indentLv } })};`;
          break;
        }
        case "ResponseObject": {
          subschemaOutput = `${transformResponseObject(subschema.schema, { path: path2, ctx: { ...ctx, indentLv } })};`;
          break;
        }
        case "SchemaMap": {
          subschemaOutput = `${transformSchemaObjectMap(subschema.schema, { path: path2, ctx: { ...ctx, indentLv } })};`;
          break;
        }
        case "SchemaObject": {
          subschemaOutput = `${transformSchemaObject(subschema.schema, { path: path2, ctx: { ...ctx, indentLv } })};`;
          break;
        }
        default: {
          error(`Could not resolve subschema ${subschemaID}. Unknown type "${subschema.hint}".`);
          process.exit(1);
        }
      }
      if (subschemaOutput && !EMPTY_OBJECT_RE.test(subschemaOutput)) {
        if (comment2) output.push(indent(comment2, indentLv));
        output.push(indent(`${key}: ${subschemaOutput}`, indentLv));
      }
      delete allSchemas[subschemaID];
    }
    indentLv--;
    output.push(indent(`}${options.exportType ? ";" : ""}`, indentLv), "");
  } else {
    output.push(`export type external = Record<string, never>;`, "");
  }
  if (Object.keys(ctx.operations).length) {
    output.push(options.exportType ? "export type operations = {" : "export interface operations {", "");
    for (const [key, { operationType, comment: comment2 }] of Object.entries(ctx.operations)) {
      if (comment2) output.push(indent(comment2, 1));
      output.push(indent(`${escObjKey(key)}: ${operationType};`, 1));
    }
    output.push(`}${options.exportType ? ";" : ""}`, "");
  } else {
    output.push(`export type operations = Record<string, never>;`, "");
  }
  if (output.join("\n").includes("OneOf")) {
    output.splice(
      1,
      0,
      "/** OneOf type helpers */",
      "type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };",
      "type XOR<T, U> = (T | U) extends object ? (Without<T, U> & U) | (Without<U, T> & T) : T | U;",
      "type OneOf<T extends any[]> = T extends [infer Only] ? Only : T extends [infer A, infer B, ...infer Rest] ? OneOf<[XOR<A, B>, ...Rest]> : never;",
      ""
    );
  }
  if (output.join("\n").includes("WithRequired")) {
    output.splice(1, 0, "/** WithRequired type helpers */", "type WithRequired<T, K extends keyof T> = T & { [P in K]-?: T[P] };", "");
  }
  return output.join("\n");
}
var src_default = openapiTS;
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  COMMENT_HEADER
});
module.exports = module.exports.default;
