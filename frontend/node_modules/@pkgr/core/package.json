{"name": "@pkgr/core", "version": "0.2.4", "type": "module", "description": "Shared core module for `@pkgr` packages or any package else", "repository": "git+https://github.com/un-ts/pkgr.git", "homepage": "https://github.com/un-ts/pkgr/blob/master/packages/core", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://www.1stG.me)", "funding": "https://opencollective.com/pkgr", "license": "MIT", "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "main": "./lib/index.cjs", "types": "./index.d.cts", "module": "./lib/index.js", "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "require": {"types": "./index.d.cts", "default": "./lib/index.cjs"}}, "./package.json": "./package.json"}, "files": ["index.d.cts", "lib"], "publishConfig": {"access": "public"}, "sideEffects": false}