# AiClearBill Frontend

## Overview

AiClearBill is a web application for automating invoice processing with OCR. The frontend is built with React, TypeScript, and Vite.

## Architecture

The frontend follows a clean architecture pattern with the following components:

### Domain Models

Type-safe domain models in the `src/models` directory represent the core business entities:

- `Invoice.ts` - Invoice domain model and types
- `Vendor.ts` - Vendor domain model and types
- `User.ts` - User domain model and authentication types
- `Tenant.ts` - Tenant domain model and types

### Data Mappers

The data mapper pattern in `src/mappers` translates between API DTOs and domain models:

- `invoiceMapper.ts` - Transforms between API invoice data and domain Invoice model
- `vendorMapper.ts` - Transforms between API vendor data and domain Vendor model
- `userMapper.ts` - Transforms between Firebase Auth user and domain User model

### Services

Type-safe service layers in `src/services` handle API communication:

- `invoiceService.ts` - Invoice CRUD operations with proper error handling
- `vendorService.ts` - Vendor CRUD operations
- `api.ts` - Base API client with authentication support

### Custom Hooks

React hooks in `src/hooks` provide stateful access to domain entities:

- `useInvoices.ts` - Hook for working with invoices
- `useVendors.ts` - Hook for working with vendors
- `useApi.ts` - Generic hook for API operations

### Components

React components use TypeScript for prop type safety.

## TypeScript Migration Status

The codebase is transitioning from JavaScript to TypeScript. Progress:

- [x] Core domain models (Invoice, Vendor, User, Tenant)
- [x] Data mappers for core entities
- [x] Type-safe service layer
- [x] React hooks with TypeScript
- [x] AuthProvider and TenantContext
- [ ] Complete migration of all React components
- [ ] Full type checking without errors

## Development

### Prerequisites

- Node.js 18+
- npm 8+

### Setup

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Run linter
npm run lint

# Type checking
npm run typecheck
```

### Build

```bash
# Production build
npm run build
```

## Type Safety Best Practices

1. Always use TypeScript interfaces for component props
2. Use domain models in components instead of raw API data
3. Leverage the data mapper pattern for API communication
4. Use custom hooks for stateful logic
5. Add proper error handling with typed errors
