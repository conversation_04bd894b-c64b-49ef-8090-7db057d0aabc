import axios from "axios";

// Create API base URL
const API_BASE_URL = "http://localhost:8000/api";

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// API service object
export const api = {
  // Set auth token for requests
  setAuthToken(token) {
    if (token) {
      apiClient.defaults.headers.common["Authorization"] = `Bearer ${token}`;
    }
  },

  // Clear auth token
  clearAuthToken() {
    delete apiClient.defaults.headers.common["Authorization"];
  },

  /**
   * Generic request methods
   * @param {string} endpoint
   * @param {object} params
   * @returns {Promise<any>}
   */
  async get(endpoint, params = {}) {
    try {
      const response = await apiClient.get(endpoint, { params });
      return response;
    } catch (error) {
      this._handleError(error);
      throw error;
    }
  },

  /**
   * Generic request methods
   * @param {string} endpoint
   * @param {object} data
   * @param {object} config
   * @returns {Promise<any>}
   */
  async post(endpoint, data = {}, config = {}) {
    try {
      const response = await apiClient.post(endpoint, data, config);
      return response;
    } catch (error) {
      this._handleError(error);
      throw error;
    }
  },

  /**
   * Generic request methods
   * @param {string} endpoint
   * @param {object} data
   * @returns {Promise<any>}
   */
  async put(endpoint, data = {}) {
    try {
      const response = await apiClient.put(endpoint, data);
      return response;
    } catch (error) {
      this._handleError(error);
      throw error;
    }
  },

  /**
   * Generic request methods
   * @param {string} endpoint
   * @param {object} data
   * @returns {Promise<any>}
   */
  async patch(endpoint, data = {}) {
    try {
      const response = await apiClient.patch(endpoint, data);
      return response;
    } catch (error) {
      this._handleError(error);
      throw error;
    }
  },

  /**
   * Generic request methods
   * @param {string} endpoint
   * @returns {Promise<any>}
   */
  async delete(endpoint) {
    try {
      const response = await apiClient.delete(endpoint);
      return response;
    } catch (error) {
      this._handleError(error);
      throw error;
    }
  },

  /**
   * Handle common API errors
   * @param {any} error
   * @returns {void}
   */
  _handleError(error) {
    if (error.response) {
      // Server responded with a status code outside of 2xx
      console.error("API Error Response:", error.response.data);

      // Handle authentication errors
      if (error.response.status === 401) {
        // Could trigger a sign-out or token refresh here
        console.log("Authentication error, user may need to log in again");
      }
    } else if (error.request) {
      // Request was made but no response received
      console.error("API No Response:", error.request);
    } else {
      // Something else happened while setting up the request
      console.error("API Error:", error.message);
    }
  },
};

export default api;
