import { InvoiceForm } from "../types/invoiceSchema.ts";

const API_BASE = "/api/invoice";

export async function uploadInvoice(
  file: File,
  token: string,
): Promise<unknown> {
  const formData = new FormData();
  formData.append("file", file);

  const res = await fetch(`${API_BASE}/upload_invoice`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: formData,
  });
  if (!res.ok) throw new Error(await res.text());
  return await res.json();
}

export async function updateInvoice(
  invoiceId: string,
  data: Partial<InvoiceForm>,
  token: string,
): Promise<unknown> {
  const res = await fetch(`${API_BASE}/update_invoice/${invoiceId}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });
  if (!res.ok) throw new Error(await res.text());
  return await res.json();
}
export async function getInvoice(
  invoiceId: string,
  token: string,
): Promise<unknown> {
  const res = await fetch(`${API_BASE}/get_invoice/${invoiceId}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  if (!res.ok) throw new Error(await res.text());
  return await res.json();
}
