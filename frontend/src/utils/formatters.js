/**
 * Format a date string to a localized format
 * @param {string} dateString - Date string in ISO format (YYYY-MM-DD)
 * @param {string} [locale='en-US'] - Locale for formatting
 * @returns {string} Formatted date string
 */
/**
 * @param {string} dateString
 * @param {string} [locale='en-US']
 * @returns {string}
 */
export function formatDate(dateString, locale = "en-US") {
  if (!dateString) return "-";

  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat(locale, {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  } catch (error) {
    console.error("Error formatting date:", error);
    return dateString; // Return the original string if formatting fails
  }
}

/**
 * Format a number as currency
 * @param {number} amount - Amount to format
 * @param {string} [currency='USD'] - Currency code
 * @param {string} [locale='en-US'] - Locale for formatting
 * @returns {string} Formatted currency string
 */
/**
 * @param {number} amount
 * @param {string} [currency='USD']
 * @param {string} [locale='en-US']
 * @returns {string}
 */
export function formatCurrency(amount, currency = "USD", locale = "en-US") {
  if (amount === null || amount === undefined) return "-";

  try {
    return new Intl.NumberFormat(locale, {
      style: "currency",
      currency: currency,
    }).format(amount);
  } catch (error) {
    console.error("Error formatting currency:", error);
    return `${currency} ${amount}`; // Fallback format
  }
}

/**
 * Truncate a string if it exceeds the maximum length
 * @param {string} str - String to truncate
 * @param {number} [maxLength=50] - Maximum length
 * @returns {string} Truncated string
 */
/**
 * @param {string} str
 * @param {number} [maxLength=50]
 * @returns {string}
 */
export function truncateString(str, maxLength = 50) {
  if (!str) return "";
  if (str.length <= maxLength) return str;

  return `${str.substring(0, maxLength)}...`;
}

/**
 * Format a file size in bytes to a human-readable string
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
/**
 * @param {number} bytes
 * @returns {string}
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}
