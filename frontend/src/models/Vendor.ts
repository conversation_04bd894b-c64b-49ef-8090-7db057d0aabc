/**
 * Frontend domain model for Vendors
 * This is the clean model used throughout the UI components
 */
export interface Vendor {
  id: string;
  tenantId: string;
  name: string;
  defaultAccount?: string;
  defaultTaxCode?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Model for creating a new vendor
 */
export interface VendorCreate {
  name: string;
  defaultAccount?: string;
  defaultTaxCode?: string;
}

/**
 * Model for updating an existing vendor
 */
export interface VendorUpdate {
  name?: string;
  defaultAccount?: string;
  defaultTaxCode?: string;
}
