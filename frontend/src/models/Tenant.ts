/**
 * Frontend domain model for Tenants
 * This is the clean model used throughout the UI components
 */
export interface Tenant {
  id: string;
  name: string;
  description?: string;
  qboConnected: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Model for creating a new tenant
 */
export interface TenantCreate {
  name: string;
  description?: string;
}

/**
 * Model for updating an existing tenant
 */
export interface TenantUpdate {
  name?: string;
  description?: string;
}
