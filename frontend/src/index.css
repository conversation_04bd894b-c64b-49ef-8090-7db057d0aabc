/* Main styles for AIClearBill */

/* Base styles - use Bootstrap theme for most styling */
html,
body,
#root {
  height: 100%;
}

/* Layout */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Make the main content scrollable */
main {
  overflow-y: auto;
}

/* Sidebar styles */
.sidebar {
  position: sticky;
  top: 56px; /* Match navbar height */
  z-index: 100;
}

/* Invoice upload styles */
.upload-area {
  border: 2px dashed var(--bs-border-color);
  padding: 2rem;
  border-radius: 0.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.upload-area.active {
  border-color: var(--bs-primary);
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

/* Camera capture styles */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  background-color: theme("colors.background");
  color: theme("colors.textmain");
  font-family: "Inter", sans-serif;
}

/* Accent color utility */
.accent {
  color: theme("colors.accent");
}

/* Example: Use .accent-bg for accent backgrounds */
.accent-bg {
  background-color: theme("colors.accent");
  color: #fff;
}

.camera-container {
  position: relative;
  width: 100%;
  border-radius: 0.5rem;
  overflow: hidden;
}

.camera-controls {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 1rem;
}

/* Vendor and invoice list table styles */
.table-hover tbody tr:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

/* Status badges */
.badge {
  font-weight: 500;
  padding: 0.4em 0.6em;
}

/* Form validation */
.was-validated .form-control:invalid {
  border-color: var(--bs-danger);
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:valid {
  border-color: var(--bs-success);
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* PWA styles */
@media (display-mode: standalone) {
  .navbar {
    padding-top: env(safe-area-inset-top);
  }

  main {
    padding-bottom: env(safe-area-inset-bottom);
  }
}
