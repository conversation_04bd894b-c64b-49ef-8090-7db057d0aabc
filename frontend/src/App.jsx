import React, { useEffect } from "react";
import { Routes, Route, Navigate, useNavigate } from "react-router-dom";
import { useAuth } from "./components/Auth/AuthProvider.tsx";
import { useTenant } from "./contexts/TenantContext.tsx";

// Components
import { Login } from "./components/Auth/Login.tsx";
import Navbar from "./components/Layout/Navbar.tsx";
import Sidebar from "./components/Layout/Sidebar.tsx";
import InvoicesList from "./components/Invoices/InvoicesList.tsx";
import InvoiceEditor from "./components/Invoices/InvoiceEditor.tsx";
import InvoiceUpload from "./components/Invoices/InvoiceUpload.tsx";
import { VendorsList } from "./components/Vendors/VendorsList.tsx";
import VendorEditor from "./components/Vendors/VendorEditor.tsx";

// Routes that require authentication
const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" />;
  }

  return children;
};

const App = () => {
  const { user } = useAuth();
  const { currentTenant } = useTenant();
  const navigate = useNavigate();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (user && !currentTenant) {
      navigate("/select-tenant");
    }
  }, [user, currentTenant, navigate]);

  return (
    <div className="app-container min-vh-100 d-flex flex-column">
      {user && <Navbar />}

      <div className="flex-grow-1 d-flex">
        {user && currentTenant && <Sidebar />}

        <main className="flex-grow-1 p-3">
          <Routes>
            {/* Public routes */}
            <Route path="/login" element={<Login />} />

            {/* Protected routes */}
            <Route
              path="/invoices"
              element={
                <ProtectedRoute>
                  <InvoicesList />
                </ProtectedRoute>
              }
            />

            <Route
              path="/invoices/:id"
              element={
                <ProtectedRoute>
                  <InvoiceEditor />
                </ProtectedRoute>
              }
            />

            <Route
              path="/upload"
              element={
                <ProtectedRoute>
                  <InvoiceUpload />
                </ProtectedRoute>
              }
            />

            <Route
              path="/vendors"
              element={
                <ProtectedRoute>
                  <VendorsList />
                </ProtectedRoute>
              }
            />

            <Route
              path="/vendors/:id"
              element={
                <ProtectedRoute>
                  <VendorEditor />
                </ProtectedRoute>
              }
            />

            {/* Default redirect */}
            <Route
              path="*"
              element={<Navigate to={user ? "/invoices" : "/login"} />}
            />
          </Routes>
        </main>
      </div>
    </div>
  );
};

export default App;
