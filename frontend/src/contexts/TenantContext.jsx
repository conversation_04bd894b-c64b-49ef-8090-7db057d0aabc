import React, { createContext, useContext, useState, useEffect } from "react";
import { api } from "../services/api.ts";
import { useAuth } from "../components/Auth/AuthProvider.tsx";

// Create context
const TenantContext = createContext();

/**
 * @returns {import('./TenantContext').TenantContextValue}
 */
export function useTenant() {
  return useContext(TenantContext);
}

/**
 * @param {{ children: React.ReactNode }} props
 * @returns {JSX.Element}
 */
export function TenantProvider({ children }) {
  const [tenants, setTenants] = useState([]);
  const [currentTenant, setCurrentTenant] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user, authToken } = useAuth();

  // Fetch tenants when user is authenticated
  useEffect(() => {
    if (!user || !authToken) {
      setTenants([]);
      setCurrentTenant(null);
      setLoading(false);
      return;
    }

    const fetchTenants = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch tenants the user has access to
        const response = await api.get("/v1/tenants/");
        setTenants(response.data);

        // Set current tenant (first one by default, or from localStorage)
        if (response.data.length > 0) {
          const savedTenantId = localStorage.getItem("currentTenantId");

          if (savedTenantId) {
            const savedTenant = response.data.find(
              (t) => t.id === savedTenantId,
            );
            if (savedTenant) {
              setCurrentTenant(savedTenant);
            } else {
              setCurrentTenant(response.data[0]);
            }
          } else {
            setCurrentTenant(response.data[0]);
          }
        } else {
          setCurrentTenant(null);
        }
      } catch (err) {
        console.error("Error fetching tenants:", err);
        setError("Failed to load tenants");
        setTenants([]);
        setCurrentTenant(null);
      } finally {
        setLoading(false);
      }
    };

    fetchTenants();
  }, [user, authToken]);

  // Save current tenant to localStorage
  useEffect(() => {
    if (currentTenant) {
      localStorage.setItem("currentTenantId", currentTenant.id);
    }
  }, [currentTenant]);

  // Create new tenant
  const createTenant = async (tenantData) => {
    try {
      setError(null);

      const response = await api.post("/v1/tenants/", tenantData);

      // Add to tenants list
      setTenants((prev) => [...prev, response.data]);

      // Set as current tenant if it's the only one
      if (tenants.length === 0) {
        setCurrentTenant(response.data);
      }

      return response.data;
    } catch (err) {
      console.error("Error creating tenant:", err);
      setError("Failed to create tenant");
      throw err;
    }
  };

  // Update tenant
  const updateTenant = async (tenantId, tenantData) => {
    try {
      setError(null);

      const response = await api.patch(`/v1/tenants/${tenantId}`, tenantData);

      // Update tenants list
      setTenants((prev) =>
        prev.map((t) => (t.id === tenantId ? response.data : t)),
      );

      // Update current tenant if it's the one being updated
      if (currentTenant && currentTenant.id === tenantId) {
        setCurrentTenant(response.data);
      }

      return response.data;
    } catch (err) {
      console.error("Error updating tenant:", err);
      setError("Failed to update tenant");
      throw err;
    }
  };

  // Delete tenant
  const deleteTenant = async (tenantId) => {
    try {
      setError(null);

      await api.delete(`/v1/tenants/${tenantId}`);

      // Remove from tenants list
      setTenants((prev) => prev.filter((t) => t.id !== tenantId));

      // Update current tenant if it's the one being deleted
      if (currentTenant && currentTenant.id === tenantId) {
        const newCurrentTenant = tenants.find((t) => t.id !== tenantId);
        setCurrentTenant(newCurrentTenant || null);
      }

      return true;
    } catch (err) {
      console.error("Error deleting tenant:", err);
      setError("Failed to delete tenant");
      throw err;
    }
  };

  // Context value
  const value = {
    tenants,
    currentTenant,
    setCurrentTenant,
    loading,
    error,
    createTenant,
    updateTenant,
    deleteTenant,
  };

  return (
    <TenantContext.Provider value={value}>{children}</TenantContext.Provider>
  );
}
