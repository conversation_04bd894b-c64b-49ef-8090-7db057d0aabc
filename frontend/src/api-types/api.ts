/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  "/api/v1/{tenant_id}/invoices/": {
    /**
     * Get Invoices
     * @description List invoices with optional filters
     */
    get: operations["get_invoices_api_v1__tenant_id__invoices__get"];
    /**
     * Upload Invoice
     * @description Upload a new invoice file and start OCR processing
     */
    post: operations["upload_invoice_api_v1__tenant_id__invoices__post"];
  };
  "/api/v1/{tenant_id}/invoices/{invoice_id}": {
    /**
     * Get Invoice By Id
     * @description Get a specific invoice by ID
     */
    get: operations["get_invoice_by_id_api_v1__tenant_id__invoices__invoice_id__get"];
    /**
     * Delete Invoice By Id
     * @description Delete an invoice
     */
    delete: operations["delete_invoice_by_id_api_v1__tenant_id__invoices__invoice_id__delete"];
    /**
     * Update Invoice By Id
     * @description Update invoice data
     */
    patch: operations["update_invoice_by_id_api_v1__tenant_id__invoices__invoice_id__patch"];
  };
  "/api/v1/{tenant_id}/invoices/{invoice_id}/process": {
    /**
     * Process Invoice
     * @description Manually trigger OCR processing for an invoice
     */
    post: operations["process_invoice_api_v1__tenant_id__invoices__invoice_id__process_post"];
  };
  "/api/v1/{tenant_id}/vendors/": {
    /**
     * Get Vendors
     * @description List vendors with optional filters
     */
    get: operations["get_vendors_api_v1__tenant_id__vendors__get"];
    /**
     * Create New Vendor
     * @description Create a new vendor
     */
    post: operations["create_new_vendor_api_v1__tenant_id__vendors__post"];
  };
  "/api/v1/{tenant_id}/vendors/{vendor_id}": {
    /**
     * Get Vendor By Id
     * @description Get a specific vendor by ID
     */
    get: operations["get_vendor_by_id_api_v1__tenant_id__vendors__vendor_id__get"];
    /**
     * Delete Vendor By Id
     * @description Delete a vendor
     */
    delete: operations["delete_vendor_by_id_api_v1__tenant_id__vendors__vendor_id__delete"];
    /**
     * Update Vendor By Id
     * @description Update vendor data
     */
    patch: operations["update_vendor_by_id_api_v1__tenant_id__vendors__vendor_id__patch"];
  };
  "/api/v1/tenants/": {
    /**
     * Get Tenants
     * @description List tenants with optional filters (admin only)
     */
    get: operations["get_tenants_api_v1_tenants__get"];
    /**
     * Create New Tenant
     * @description Create a new tenant (admin only)
     */
    post: operations["create_new_tenant_api_v1_tenants__post"];
  };
  "/api/v1/tenants/{tenant_id}": {
    /**
     * Get Tenant By Id
     * @description Get a specific tenant by ID (users can only access their own tenant)
     */
    get: operations["get_tenant_by_id_api_v1_tenants__tenant_id__get"];
    /**
     * Delete Tenant By Id
     * @description Delete a tenant (admin only)
     */
    delete: operations["delete_tenant_by_id_api_v1_tenants__tenant_id__delete"];
    /**
     * Update Tenant By Id
     * @description Update tenant data (admin only)
     */
    patch: operations["update_tenant_by_id_api_v1_tenants__tenant_id__patch"];
  };
  "/api/v1/{tenant_id}/qbo/auth-url": {
    /**
     * Get Auth Url
     * @description Get QuickBooks OAuth authorization URL
     */
    get: operations["get_auth_url_api_v1__tenant_id__qbo_auth_url_get"];
  };
  "/api/v1/{tenant_id}/qbo/auth-callback": {
    /**
     * Handle Auth Callback
     * @description Handle QuickBooks OAuth callback - exchange code for tokens
     */
    post: operations["handle_auth_callback_api_v1__tenant_id__qbo_auth_callback_post"];
  };
  "/api/v1/{tenant_id}/qbo/refresh-tokens": {
    /**
     * Refresh Tokens
     * @description Refresh QBO access tokens
     */
    post: operations["refresh_tokens_api_v1__tenant_id__qbo_refresh_tokens_post"];
  };
  "/api/v1/{tenant_id}/qbo/push-invoice/{invoice_id}": {
    /**
     * Push Invoice
     * @description Push invoice to QuickBooks Online
     */
    post: operations["push_invoice_api_v1__tenant_id__qbo_push_invoice__invoice_id__post"];
  };
  "/api/v1/{tenant_id}/qbo/accounts": {
    /**
     * Get Accounts
     * @description Get QuickBooks chart of accounts
     */
    get: operations["get_accounts_api_v1__tenant_id__qbo_accounts_get"];
  };
  "/api/v1/{tenant_id}/qbo/tax-codes": {
    /**
     * Get Tax Codes
     * @description Get QuickBooks tax codes
     */
    get: operations["get_tax_codes_api_v1__tenant_id__qbo_tax_codes_get"];
  };
  "/health": {
    /** Health Check */
    get: operations["health_check_health_get"];
  };
}

export type webhooks = Record<string, never>;

export interface components {
  schemas: {
    /** Body_upload_invoice_api_v1__tenant_id__invoices__post */
    Body_upload_invoice_api_v1__tenant_id__invoices__post: {
      /**
       * File
       * Format: binary
       */
      file: string;
    };
    /** HTTPValidationError */
    HTTPValidationError: {
      /** Detail */
      detail?: components["schemas"]["ValidationError"][];
    };
    /** InvoiceFields */
    InvoiceFields: {
      /** Vendorid */
      vendorId?: string | null;
      /** Date */
      date?: string | null;
      /** Number */
      number?: string | null;
      /** Memo */
      memo?: string | null;
      /** Vatlines */
      vatLines?: components["schemas"]["VATLine"][] | null;
      /**
       * Validated
       * @default false
       */
      validated?: boolean;
    };
    /** InvoiceResponse */
    InvoiceResponse: {
      /** Id */
      id: string;
      /** Tenantid */
      tenantId: string;
      /** Fileurl */
      fileUrl: string;
      /** Sha256 */
      sha256?: string | null;
      fields?: components["schemas"]["InvoiceFields"] | null;
      /** Status */
      status: string;
      /** Qbotxnid */
      qboTxnId?: string | null;
      /**
       * Createdat
       * Format: date-time
       */
      createdAt: string;
      /**
       * Updatedat
       * Format: date-time
       */
      updatedAt: string;
    };
    /** InvoiceUpdateModel */
    InvoiceUpdateModel: {
      fields?: components["schemas"]["InvoiceFields"] | null;
      /** Status */
      status?: string | null;
      /** Qbotxnid */
      qboTxnId?: string | null;
    };
    /** QBOAccount */
    QBOAccount: {
      /** Id */
      id: string;
      /** Name */
      name: string;
      /** Account Type */
      account_type: string;
      /** Fully Qualified Name */
      fully_qualified_name: string;
    };
    /** QBOAuthCode */
    QBOAuthCode: {
      /** Code */
      code: string;
      /** Realm Id */
      realm_id: string;
      /** State */
      state?: string | null;
    };
    /** QBOAuthResponse */
    QBOAuthResponse: {
      /** Auth Url */
      auth_url: string;
    };
    /** QBOPushResponse */
    QBOPushResponse: {
      /** Success */
      success: boolean;
      /** Qbo Txn Id */
      qbo_txn_id?: string | null;
      /** Error */
      error?: string | null;
    };
    /** QBOTaxCode */
    QBOTaxCode: {
      /** Id */
      id: string;
      /** Name */
      name: string;
      /** Description */
      description?: string | null;
      /** Active */
      active: boolean;
      /** Tax Rate */
      tax_rate?: number | null;
    };
    /** QBOTokens */
    QBOTokens: {
      /** Access Token */
      access_token: string;
      /** Refresh Token */
      refresh_token: string;
      /**
       * Expires At
       * Format: date-time
       */
      expires_at: string;
    };
    /** QBOTokensResponse */
    QBOTokensResponse: {
      /** Access Token */
      access_token: string;
      /** Refresh Token */
      refresh_token: string;
      /**
       * Expires At
       * Format: date-time
       */
      expires_at: string;
      /** Realm Id */
      realm_id: string;
    };
    /** TenantCreate */
    TenantCreate: {
      /** Name */
      name: string;
      /** Qborealmid */
      qboRealmId?: string | null;
      qboTokens?: components["schemas"]["QBOTokens"] | null;
    };
    /** TenantResponse */
    TenantResponse: {
      /** Id */
      id: string;
      /** Name */
      name: string;
      /** Qborealmid */
      qboRealmId?: string | null;
      qboTokens?: components["schemas"]["QBOTokens"] | null;
      /**
       * Createdat
       * Format: date-time
       */
      createdAt: string;
      /**
       * Updatedat
       * Format: date-time
       */
      updatedAt: string;
    };
    /** TenantUpdate */
    TenantUpdate: {
      /** Name */
      name?: string | null;
      /** Qborealmid */
      qboRealmId?: string | null;
      qboTokens?: components["schemas"]["QBOTokens"] | null;
    };
    /** VATLine */
    VATLine: {
      /** Amount */
      amount: number;
      /** Taxcode */
      taxCode: string;
      /** Taxrate */
      taxRate?: number | null;
    };
    /** ValidationError */
    ValidationError: {
      /** Location */
      loc: (string | number)[];
      /** Message */
      msg: string;
      /** Error Type */
      type: string;
    };
    /** VendorCreate */
    VendorCreate: {
      /** Name */
      name: string;
      /** Defaultaccount */
      defaultAccount?: string | null;
      /** Defaulttaxcode */
      defaultTaxCode?: string | null;
    };
    /** VendorResponse */
    VendorResponse: {
      /** Id */
      id: string;
      /** Tenantid */
      tenantId: string;
      /** Name */
      name: string;
      /** Defaultaccount */
      defaultAccount?: string | null;
      /** Defaulttaxcode */
      defaultTaxCode?: string | null;
      /**
       * Createdat
       * Format: date-time
       */
      createdAt: string;
      /**
       * Updatedat
       * Format: date-time
       */
      updatedAt: string;
    };
    /** VendorUpdate */
    VendorUpdate: {
      /** Name */
      name?: string | null;
      /** Defaultaccount */
      defaultAccount?: string | null;
      /** Defaulttaxcode */
      defaultTaxCode?: string | null;
    };
  };
  responses: never;
  parameters: never;
  requestBodies: never;
  headers: never;
  pathItems: never;
}

export type $defs = Record<string, never>;

export type external = Record<string, never>;

export interface operations {
  /**
   * Get Invoices
   * @description List invoices with optional filters
   */
  get_invoices_api_v1__tenant_id__invoices__get: {
    parameters: {
      query?: {
        /** @description Filter by status */
        status?: string | null;
        /** @description Filter by vendor ID */
        vendor_id?: string | null;
        limit?: number;
        offset?: number;
      };
      path: {
        tenant_id: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["InvoiceResponse"][];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Upload Invoice
   * @description Upload a new invoice file and start OCR processing
   */
  upload_invoice_api_v1__tenant_id__invoices__post: {
    parameters: {
      path: {
        tenant_id: string | null;
      };
    };
    requestBody: {
      content: {
        "multipart/form-data": components["schemas"]["Body_upload_invoice_api_v1__tenant_id__invoices__post"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["InvoiceResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Get Invoice By Id
   * @description Get a specific invoice by ID
   */
  get_invoice_by_id_api_v1__tenant_id__invoices__invoice_id__get: {
    parameters: {
      path: {
        tenant_id: string | null;
        invoice_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["InvoiceResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Delete Invoice By Id
   * @description Delete an invoice
   */
  delete_invoice_by_id_api_v1__tenant_id__invoices__invoice_id__delete: {
    parameters: {
      path: {
        tenant_id: string | null;
        invoice_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Update Invoice By Id
   * @description Update invoice data
   */
  update_invoice_by_id_api_v1__tenant_id__invoices__invoice_id__patch: {
    parameters: {
      path: {
        tenant_id: string | null;
        invoice_id: string;
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["InvoiceUpdateModel"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["InvoiceResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Process Invoice
   * @description Manually trigger OCR processing for an invoice
   */
  process_invoice_api_v1__tenant_id__invoices__invoice_id__process_post: {
    parameters: {
      path: {
        tenant_id: string | null;
        invoice_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Get Vendors
   * @description List vendors with optional filters
   */
  get_vendors_api_v1__tenant_id__vendors__get: {
    parameters: {
      query?: {
        /** @description Filter by vendor name */
        name?: string | null;
        limit?: number;
        offset?: number;
      };
      path: {
        tenant_id: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["VendorResponse"][];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Create New Vendor
   * @description Create a new vendor
   */
  create_new_vendor_api_v1__tenant_id__vendors__post: {
    parameters: {
      path: {
        tenant_id: string | null;
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["VendorCreate"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["VendorResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Get Vendor By Id
   * @description Get a specific vendor by ID
   */
  get_vendor_by_id_api_v1__tenant_id__vendors__vendor_id__get: {
    parameters: {
      path: {
        tenant_id: string | null;
        vendor_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["VendorResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Delete Vendor By Id
   * @description Delete a vendor
   */
  delete_vendor_by_id_api_v1__tenant_id__vendors__vendor_id__delete: {
    parameters: {
      path: {
        tenant_id: string | null;
        vendor_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Update Vendor By Id
   * @description Update vendor data
   */
  update_vendor_by_id_api_v1__tenant_id__vendors__vendor_id__patch: {
    parameters: {
      path: {
        tenant_id: string | null;
        vendor_id: string;
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["VendorUpdate"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["VendorResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Get Tenants
   * @description List tenants with optional filters (admin only)
   */
  get_tenants_api_v1_tenants__get: {
    parameters: {
      query?: {
        /** @description Filter by tenant name */
        name?: string | null;
        limit?: number;
        offset?: number;
        tenant_id?: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["TenantResponse"][];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Create New Tenant
   * @description Create a new tenant (admin only)
   */
  create_new_tenant_api_v1_tenants__post: {
    parameters: {
      query?: {
        tenant_id?: string | null;
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["TenantCreate"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["TenantResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Get Tenant By Id
   * @description Get a specific tenant by ID (users can only access their own tenant)
   */
  get_tenant_by_id_api_v1_tenants__tenant_id__get: {
    parameters: {
      path: {
        tenant_id: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["TenantResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Delete Tenant By Id
   * @description Delete a tenant (admin only)
   */
  delete_tenant_by_id_api_v1_tenants__tenant_id__delete: {
    parameters: {
      path: {
        tenant_id: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": unknown;
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Update Tenant By Id
   * @description Update tenant data (admin only)
   */
  update_tenant_by_id_api_v1_tenants__tenant_id__patch: {
    parameters: {
      path: {
        tenant_id: string | null;
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["TenantUpdate"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["TenantResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Get Auth Url
   * @description Get QuickBooks OAuth authorization URL
   */
  get_auth_url_api_v1__tenant_id__qbo_auth_url_get: {
    parameters: {
      path: {
        tenant_id: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["QBOAuthResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Handle Auth Callback
   * @description Handle QuickBooks OAuth callback - exchange code for tokens
   */
  handle_auth_callback_api_v1__tenant_id__qbo_auth_callback_post: {
    parameters: {
      path: {
        tenant_id: string | null;
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["QBOAuthCode"];
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["QBOTokensResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Refresh Tokens
   * @description Refresh QBO access tokens
   */
  refresh_tokens_api_v1__tenant_id__qbo_refresh_tokens_post: {
    parameters: {
      path: {
        tenant_id: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["QBOTokensResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Push Invoice
   * @description Push invoice to QuickBooks Online
   */
  push_invoice_api_v1__tenant_id__qbo_push_invoice__invoice_id__post: {
    parameters: {
      path: {
        tenant_id: string | null;
        invoice_id: string;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["QBOPushResponse"];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Get Accounts
   * @description Get QuickBooks chart of accounts
   */
  get_accounts_api_v1__tenant_id__qbo_accounts_get: {
    parameters: {
      path: {
        tenant_id: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["QBOAccount"][];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /**
   * Get Tax Codes
   * @description Get QuickBooks tax codes
   */
  get_tax_codes_api_v1__tenant_id__qbo_tax_codes_get: {
    parameters: {
      path: {
        tenant_id: string | null;
      };
    };
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": components["schemas"]["QBOTaxCode"][];
        };
      };
      /** @description Validation Error */
      422: {
        content: {
          "application/json": components["schemas"]["HTTPValidationError"];
        };
      };
    };
  };
  /** Health Check */
  health_check_health_get: {
    responses: {
      /** @description Successful Response */
      200: {
        content: {
          "application/json": unknown;
        };
      };
    };
  };
}
