{"openapi": "3.1.0", "info": {"title": "AIClearBill API", "description": "Multi-tenant OCR-powered invoice processing API", "version": "1.0.0"}, "paths": {"/api/v1/{tenant_id}/invoices/": {"post": {"tags": ["invoices"], "summary": "Upload Invoice", "description": "Upload a new invoice file and start OCR processing", "operationId": "upload_invoice_api_v1__tenant_id__invoices__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_invoice_api_v1__tenant_id__invoices__post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["invoices"], "summary": "Get Invoices", "description": "List invoices with optional filters", "operationId": "get_invoices_api_v1__tenant_id__invoices__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by status", "title": "Status"}, "description": "Filter by status"}, {"name": "vendor_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by vendor ID", "title": "Vendor Id"}, "description": "Filter by vendor ID"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 20, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/InvoiceResponse"}, "title": "Response Get Invoices Api V1  Tenant Id  Invoices  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/{tenant_id}/invoices/{invoice_id}": {"get": {"tags": ["invoices"], "summary": "Get Invoice By Id", "description": "Get a specific invoice by ID", "operationId": "get_invoice_by_id_api_v1__tenant_id__invoices__invoice_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}, {"name": "invoice_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Invoice Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["invoices"], "summary": "Update Invoice By Id", "description": "Update invoice data", "operationId": "update_invoice_by_id_api_v1__tenant_id__invoices__invoice_id__patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}, {"name": "invoice_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Invoice Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceUpdateModel"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["invoices"], "summary": "Delete Invoice By Id", "description": "Delete an invoice", "operationId": "delete_invoice_by_id_api_v1__tenant_id__invoices__invoice_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}, {"name": "invoice_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Invoice Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/{tenant_id}/invoices/{invoice_id}/process": {"post": {"tags": ["invoices"], "summary": "Process Invoice", "description": "Manually trigger OCR processing for an invoice", "operationId": "process_invoice_api_v1__tenant_id__invoices__invoice_id__process_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}, {"name": "invoice_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Invoice Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/{tenant_id}/vendors/": {"post": {"tags": ["vendors"], "summary": "Create <PERSON>or", "description": "Create a new vendor", "operationId": "create_new_vendor_api_v1__tenant_id__vendors__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VendorCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VendorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["vendors"], "summary": "Get Vendors", "description": "List vendors with optional filters", "operationId": "get_vendors_api_v1__tenant_id__vendors__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}, {"name": "name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by vendor name", "title": "Name"}, "description": "Filter by vendor name"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 50, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VendorResponse"}, "title": "Response Get Vendors Api V1  Tenant Id  Vendors  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/{tenant_id}/vendors/{vendor_id}": {"get": {"tags": ["vendors"], "summary": "Get Vendor By Id", "description": "Get a specific vendor by ID", "operationId": "get_vendor_by_id_api_v1__tenant_id__vendors__vendor_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}, {"name": "vendor_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Vendor Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VendorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["vendors"], "summary": "Update Vendor By Id", "description": "Update vendor data", "operationId": "update_vendor_by_id_api_v1__tenant_id__vendors__vendor_id__patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}, {"name": "vendor_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Vendor Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VendorUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VendorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["vendors"], "summary": "Delete Vendor By Id", "description": "Delete a vendor", "operationId": "delete_vendor_by_id_api_v1__tenant_id__vendors__vendor_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}, {"name": "vendor_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Vendor Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tenants/": {"post": {"tags": ["tenants"], "summary": "Create New Tenant", "description": "Create a new tenant (admin only)", "operationId": "create_new_tenant_api_v1_tenants__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["tenants"], "summary": "Get Tenants", "description": "List tenants with optional filters (admin only)", "operationId": "get_tenants_api_v1_tenants__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by tenant name", "title": "Name"}, "description": "Filter by tenant name"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 20, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "tenant_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TenantResponse"}, "title": "Response Get Tenants Api V1 Tenants  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tenants/{tenant_id}": {"get": {"tags": ["tenants"], "summary": "Get Tenant By Id", "description": "Get a specific tenant by ID (users can only access their own tenant)", "operationId": "get_tenant_by_id_api_v1_tenants__tenant_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["tenants"], "summary": "Update Tenant By Id", "description": "Update tenant data (admin only)", "operationId": "update_tenant_by_id_api_v1_tenants__tenant_id__patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["tenants"], "summary": "Delete Tenant By Id", "description": "Delete a tenant (admin only)", "operationId": "delete_tenant_by_id_api_v1_tenants__tenant_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/{tenant_id}/qbo/auth-url": {"get": {"tags": ["quickbooks"], "summary": "Get Auth Url", "description": "Get QuickBooks OAuth authorization URL", "operationId": "get_auth_url_api_v1__tenant_id__qbo_auth_url_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QBOAuthResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/{tenant_id}/qbo/auth-callback": {"post": {"tags": ["quickbooks"], "summary": "<PERSON><PERSON>", "description": "Handle QuickBooks OAuth callback - exchange code for tokens", "operationId": "handle_auth_callback_api_v1__tenant_id__qbo_auth_callback_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QBOAuthCode"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QBOTokensResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/{tenant_id}/qbo/refresh-tokens": {"post": {"tags": ["quickbooks"], "summary": "Ref<PERSON>", "description": "Refresh QBO access tokens", "operationId": "refresh_tokens_api_v1__tenant_id__qbo_refresh_tokens_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QBOTokensResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/{tenant_id}/qbo/push-invoice/{invoice_id}": {"post": {"tags": ["quickbooks"], "summary": "Push Invoice", "description": "Push invoice to QuickBooks Online", "operationId": "push_invoice_api_v1__tenant_id__qbo_push_invoice__invoice_id__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}, {"name": "invoice_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Invoice Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QBOPushResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/{tenant_id}/qbo/accounts": {"get": {"tags": ["quickbooks"], "summary": "Get Accounts", "description": "Get QuickBooks chart of accounts", "operationId": "get_accounts_api_v1__tenant_id__qbo_accounts_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/QBOAccount"}, "title": "Response Get Accounts Api V1  Tenant Id  Qbo Accounts Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/{tenant_id}/qbo/tax-codes": {"get": {"tags": ["quickbooks"], "summary": "Get Tax Codes", "description": "Get QuickBooks tax codes", "operationId": "get_tax_codes_api_v1__tenant_id__qbo_tax_codes_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/QBOTaxCode"}, "title": "Response Get Tax Codes Api V1  Tenant Id  Qbo Tax Codes Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/health": {"get": {"summary": "Health Check", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"Body_upload_invoice_api_v1__tenant_id__invoices__post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_invoice_api_v1__tenant_id__invoices__post"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "InvoiceFields": {"properties": {"vendorId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Vendorid"}, "date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Date"}, "number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Number"}, "memo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Memo"}, "vatLines": {"anyOf": [{"items": {"$ref": "#/components/schemas/VATLine"}, "type": "array"}, {"type": "null"}], "title": "Vatlines"}, "validated": {"type": "boolean", "title": "Validated", "default": false}}, "type": "object", "title": "InvoiceFields"}, "InvoiceResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "tenantId": {"type": "string", "title": "Tenantid"}, "fileUrl": {"type": "string", "title": "<PERSON><PERSON><PERSON>"}, "sha256": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sha256"}, "fields": {"anyOf": [{"$ref": "#/components/schemas/InvoiceFields"}, {"type": "null"}]}, "status": {"type": "string", "title": "Status"}, "qboTxnId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Qbotxnid"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"type": "string", "format": "date-time", "title": "Updatedat"}}, "type": "object", "required": ["id", "tenantId", "fileUrl", "status", "createdAt", "updatedAt"], "title": "InvoiceResponse"}, "InvoiceUpdateModel": {"properties": {"fields": {"anyOf": [{"$ref": "#/components/schemas/InvoiceFields"}, {"type": "null"}]}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}, "qboTxnId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Qbotxnid"}}, "type": "object", "title": "InvoiceUpdateModel"}, "QBOAccount": {"properties": {"id": {"type": "string", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "account_type": {"type": "string", "title": "Account Type"}, "fully_qualified_name": {"type": "string", "title": "Fully Qualified Name"}}, "type": "object", "required": ["id", "name", "account_type", "fully_qualified_name"], "title": "QBOAccount"}, "QBOAuthCode": {"properties": {"code": {"type": "string", "title": "Code"}, "realm_id": {"type": "string", "title": "Realm Id"}, "state": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "State"}}, "type": "object", "required": ["code", "realm_id"], "title": "QBOAuthCode"}, "QBOAuthResponse": {"properties": {"auth_url": {"type": "string", "title": "Auth Url"}}, "type": "object", "required": ["auth_url"], "title": "QBOAuthResponse"}, "QBOPushResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "qbo_txn_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Qbo Txn Id"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error"}}, "type": "object", "required": ["success"], "title": "QBOPushResponse"}, "QBOTaxCode": {"properties": {"id": {"type": "string", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "active": {"type": "boolean", "title": "Active"}, "tax_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Tax Rate"}}, "type": "object", "required": ["id", "name", "active"], "title": "QBOTaxCode"}, "QBOTokens": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "refresh_token": {"type": "string", "title": "Refresh <PERSON>"}, "expires_at": {"type": "string", "format": "date-time", "title": "Expires At"}}, "type": "object", "required": ["access_token", "refresh_token", "expires_at"], "title": "QBOTokens"}, "QBOTokensResponse": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "refresh_token": {"type": "string", "title": "Refresh <PERSON>"}, "expires_at": {"type": "string", "format": "date-time", "title": "Expires At"}, "realm_id": {"type": "string", "title": "Realm Id"}}, "type": "object", "required": ["access_token", "refresh_token", "expires_at", "realm_id"], "title": "QBOTokensResponse"}, "TenantCreate": {"properties": {"name": {"type": "string", "title": "Name"}, "qboRealmId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "qboTokens": {"anyOf": [{"$ref": "#/components/schemas/QBOTokens"}, {"type": "null"}]}}, "type": "object", "required": ["name"], "title": "TenantCreate"}, "TenantResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "qboRealmId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "qboTokens": {"anyOf": [{"$ref": "#/components/schemas/QBOTokens"}, {"type": "null"}]}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"type": "string", "format": "date-time", "title": "Updatedat"}}, "type": "object", "required": ["id", "name", "createdAt", "updatedAt"], "title": "TenantResponse"}, "TenantUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "qboRealmId": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "qboTokens": {"anyOf": [{"$ref": "#/components/schemas/QBOTokens"}, {"type": "null"}]}}, "type": "object", "title": "TenantUpdate"}, "VATLine": {"properties": {"amount": {"type": "number", "title": "Amount"}, "taxCode": {"type": "string", "title": "Taxcode"}, "taxRate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Taxrate"}}, "type": "object", "required": ["amount", "taxCode"], "title": "VATLine"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "VendorCreate": {"properties": {"name": {"type": "string", "title": "Name"}, "defaultAccount": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "De<PERSON>ultac<PERSON>unt"}, "defaultTaxCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Defaulttaxcode"}}, "type": "object", "required": ["name"], "title": "VendorCreate"}, "VendorResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "tenantId": {"type": "string", "title": "Tenantid"}, "name": {"type": "string", "title": "Name"}, "defaultAccount": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "De<PERSON>ultac<PERSON>unt"}, "defaultTaxCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Defaulttaxcode"}, "createdAt": {"type": "string", "format": "date-time", "title": "Createdat"}, "updatedAt": {"type": "string", "format": "date-time", "title": "Updatedat"}}, "type": "object", "required": ["id", "tenantId", "name", "createdAt", "updatedAt"], "title": "VendorResponse"}, "VendorUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "defaultAccount": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "De<PERSON>ultac<PERSON>unt"}, "defaultTaxCode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Defaulttaxcode"}}, "type": "object", "title": "VendorUpdate"}}, "securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer"}}}}