import type { paths } from "../api-types/api.ts";

// API configuration
// Remove API_BASE_URL as Nginx proxy will handle routing
// const API_BASE_URL =
//   import.meta.env.VITE_API_BASE_URL || "http://localhost:8000";

// Error type
export class ApiError extends Error {
  status: number;
  data: unknown;

  constructor(status: number, message: string, data?: unknown) {
    super(message);
    this.name = "ApiError";
    this.status = status;
    this.data = data;
  }
}

// Generic fetch function with type safety
async function fetchApi<T>(url: string, options: RequestInit = {}): Promise<T> {
  const token = localStorage.getItem("auth_token");

  const headers = {
    "Content-Type": "application/json",
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
    ...options.headers,
  };

  // Use relative path for fetch, Nginx proxy handles the rest
  const response = await fetch(url, {
    ...options,
    headers,
  });

  const data = response.headers
    .get("Content-Type")
    ?.includes("application/json")
    ? await response.json()
    : await response.text();

  if (!response.ok) {
    throw new ApiError(
      response.status,
      response.statusText || "An error occurred",
      data,
    );
  }

  return data as T;
}

// Type-safe API client
export const apiClient = {
  // Invoices
  invoices: {
    list: async (
      tenantId: string,
      params?: {
        status?: string;
        vendor_id?: string;
        limit?: number;
        offset?: number;
      },
    ): Promise<
      paths["/api/v1/{tenant_id}/invoices/"]["get"]["responses"]["200"]["content"]["application/json"]
    > => {
      const queryParams = new URLSearchParams();
      if (params?.status) queryParams.append("status", params.status);
      if (params?.vendor_id) queryParams.append("vendor_id", params.vendor_id);
      if (params?.limit) queryParams.append("limit", params.limit.toString());
      if (params?.offset)
        queryParams.append("offset", params.offset.toString());

      const queryString = queryParams.toString()
        ? `?${queryParams.toString()}`
        : "";

      return fetchApi<
        paths["/api/v1/{tenant_id}/invoices/"]["get"]["responses"]["200"]["content"]["application/json"]
      >(`/api/v1/${tenantId}/invoices/${queryString}`);
    },

    getById: async (
      tenantId: string,
      invoiceId: string,
    ): Promise<
      paths["/api/v1/{tenant_id}/invoices/{invoice_id}"]["get"]["responses"]["200"]["content"]["application/json"]
    > => {
      return fetchApi<
        paths["/api/v1/{tenant_id}/invoices/{invoice_id}"]["get"]["responses"]["200"]["content"]["application/json"]
      >(`/api/v1/${tenantId}/invoices/${invoiceId}`);
    },

    create: async (
      tenantId: string,
      formData: FormData,
    ): Promise<
      paths["/api/v1/{tenant_id}/invoices/"]["post"]["responses"]["200"]["content"]["application/json"]
    > => {
      return fetchApi<
        paths["/api/v1/{tenant_id}/invoices/"]["post"]["responses"]["200"]["content"]["application/json"]
      >(`/api/v1/${tenantId}/invoices/`, {
        method: "POST",
        body: formData,
        headers: {
          // Don't set Content-Type here, it will be set automatically for FormData
        },
      });
    },

    update: async (
      tenantId: string,
      invoiceId: string,
      data: paths["/api/v1/{tenant_id}/invoices/{invoice_id}"]["patch"]["requestBody"]["content"]["application/json"],
    ): Promise<
      paths["/api/v1/{tenant_id}/invoices/{invoice_id}"]["patch"]["responses"]["200"]["content"]["application/json"]
    > => {
      return fetchApi<
        paths["/api/v1/{tenant_id}/invoices/{invoice_id}"]["patch"]["responses"]["200"]["content"]["application/json"]
      >(`/api/v1/${tenantId}/invoices/${invoiceId}`, {
        method: "PATCH",
        body: JSON.stringify(data),
      });
    },

    delete: async (
      tenantId: string,
      invoiceId: string,
    ): Promise<
      paths["/api/v1/{tenant_id}/invoices/{invoice_id}"]["delete"]["responses"]["200"]["content"]["application/json"]
    > => {
      return fetchApi<
        paths["/api/v1/{tenant_id}/invoices/{invoice_id}"]["delete"]["responses"]["200"]["content"]["application/json"]
      >(`/api/v1/${tenantId}/invoices/${invoiceId}`, {
        method: "DELETE",
      });
    },

    process: async (
      tenantId: string,
      invoiceId: string,
    ): Promise<
      paths["/api/v1/{tenant_id}/invoices/{invoice_id}/process"]["post"]["responses"]["200"]["content"]["application/json"]
    > => {
      return fetchApi<
        paths["/api/v1/{tenant_id}/invoices/{invoice_id}/process"]["post"]["responses"]["200"]["content"]["application/json"]
      >(`/api/v1/${tenantId}/invoices/${invoiceId}/process`, {
        method: "POST",
      });
    },
  },

  // Add other API endpoints here (vendors, auth, etc.)
};

export default apiClient;
