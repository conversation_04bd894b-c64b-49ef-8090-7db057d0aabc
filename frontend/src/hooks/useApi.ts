import { useState, useCallback } from "react";
import apiClient, { ApiError } from "../api/client.ts";
import type { paths } from "../api-types/api.ts";

// Interface for the return value of useApi
interface UseApiReturn<T, P extends unknown[]> {
  data: T | null;
  error: ApiError | null;
  loading: boolean;
  execute: (...args: P) => Promise<T>;
}

// Generic hook for API calls
export function useApi<T, P extends unknown[]>(
  apiFunction: (...args: P) => Promise<T>,
): UseApiReturn<T, P> {
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<ApiError | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const execute = useCallback(
    async (...args: P) => {
      try {
        setLoading(true);
        setError(null);
        const result = await apiFunction(...args);
        setData(result);
        return result;
      } catch (err) {
        const apiError =
          err instanceof ApiError
            ? err
            : new ApiError(500, "Unknown error occurred");
        setError(apiError);
        throw apiError;
      } finally {
        setLoading(false);
      }
    },
    [apiFunction],
  );

  return {
    data,
    error,
    loading,
    execute,
  };
}

// Type aliases for cleaner invoice types
type InvoiceListResponse =
  paths["/api/v1/{tenant_id}/invoices/"]["get"]["responses"]["200"]["content"]["application/json"];
type InvoiceGetResponse =
  paths["/api/v1/{tenant_id}/invoices/{invoice_id}"]["get"]["responses"]["200"]["content"]["application/json"];
type InvoiceCreateResponse =
  paths["/api/v1/{tenant_id}/invoices/"]["post"]["responses"]["200"]["content"]["application/json"];
type InvoiceUpdateData =
  paths["/api/v1/{tenant_id}/invoices/{invoice_id}"]["patch"]["requestBody"]["content"]["application/json"];
type InvoiceUpdateResponse =
  paths["/api/v1/{tenant_id}/invoices/{invoice_id}"]["patch"]["responses"]["200"]["content"]["application/json"];
type InvoiceDeleteResponse =
  paths["/api/v1/{tenant_id}/invoices/{invoice_id}"]["delete"]["responses"]["200"]["content"]["application/json"];
type InvoiceProcessResponse =
  paths["/api/v1/{tenant_id}/invoices/{invoice_id}/process"]["post"]["responses"]["200"]["content"]["application/json"];
type InvoiceListParams = Parameters<typeof apiClient.invoices.list>[1];

// Interface for the return value of useInvoices
interface UseInvoicesReturn {
  list: (params?: InvoiceListParams) => Promise<InvoiceListResponse>;
  get: (invoiceId: string) => Promise<InvoiceGetResponse>;
  create: (formData: FormData) => Promise<InvoiceCreateResponse>;
  update: (
    invoiceId: string,
    data: InvoiceUpdateData,
  ) => Promise<InvoiceUpdateResponse>;
  delete: (invoiceId: string) => Promise<InvoiceDeleteResponse>;
  process: (invoiceId: string) => Promise<InvoiceProcessResponse>;
  // Expose the state hooks for more granular control if needed
  listState: UseApiReturn<InvoiceListResponse, [string, InvoiceListParams?]>;
  getState: UseApiReturn<InvoiceGetResponse, [string, string]>;
  createState: UseApiReturn<InvoiceCreateResponse, [string, FormData]>;
  updateState: UseApiReturn<
    InvoiceUpdateResponse,
    [string, string, InvoiceUpdateData]
  >;
  deleteState: UseApiReturn<InvoiceDeleteResponse, [string, string]>;
  processState: UseApiReturn<InvoiceProcessResponse, [string, string]>;
}

// Specific hooks for different API endpoints
export function useInvoices(tenantId: string): UseInvoicesReturn {
  const listInvoices = useApi(apiClient.invoices.list);
  const getInvoice = useApi(apiClient.invoices.getById);
  const createInvoice = useApi(apiClient.invoices.create);
  const updateInvoice = useApi(apiClient.invoices.update);
  const deleteInvoice = useApi(apiClient.invoices.delete);
  const processInvoice = useApi(apiClient.invoices.process);

  return {
    list: (params?: InvoiceListParams) =>
      listInvoices.execute(tenantId, params),
    get: (invoiceId: string) => getInvoice.execute(tenantId, invoiceId),
    create: (formData: FormData) => createInvoice.execute(tenantId, formData),
    update: (invoiceId: string, data: InvoiceUpdateData) =>
      updateInvoice.execute(tenantId, invoiceId, data),
    delete: (invoiceId: string) => deleteInvoice.execute(tenantId, invoiceId),
    process: (invoiceId: string) => processInvoice.execute(tenantId, invoiceId),
    // Expose the state hooks
    listState: listInvoices,
    getState: getInvoice,
    createState: createInvoice,
    updateState: updateInvoice,
    deleteState: deleteInvoice,
    processState: processInvoice,
  };
}

// Add other specific hooks like useVendors, useAuth etc. as needed
