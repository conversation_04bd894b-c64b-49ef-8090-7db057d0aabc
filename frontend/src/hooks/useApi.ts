import { useState, useCallback } from 'react';
import apiClient, { ApiError } from '../api/client';

// Generic hook for API calls
export function useApi<T, P extends any[]>(
  apiFunction: (...args: P) => Promise<T>
) {
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<ApiError | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const execute = useCallback(
    async (...args: P) => {
      try {
        setLoading(true);
        setError(null);
        const result = await apiFunction(...args);
        setData(result);
        return result;
      } catch (err) {
        const apiError = err instanceof ApiError 
          ? err 
          : new ApiError(500, 'Unknown error occurred');
        setError(apiError);
        throw apiError;
      } finally {
        setLoading(false);
      }
    },
    [apiFunction]
  );

  return {
    data,
    error,
    loading,
    execute,
  };
}

// Specific hooks for different API endpoints
export function useInvoices(tenantId: string) {
  const listInvoices = useApi(apiClient.invoices.list);
  const getInvoice = useApi(apiClient.invoices.getById);
  const createInvoice = useApi(apiClient.invoices.create);
  const updateInvoice = useApi(apiClient.invoices.update);
  const deleteInvoice = useApi(apiClient.invoices.delete);
  const processInvoice = useApi(apiClient.invoices.process);

  return {
    list: (params?: Parameters<typeof apiClient.invoices.list>[1]) => 
      listInvoices.execute(tenantId, params),
    get: (invoiceId: string) => 
      getInvoice.execute(tenantId, invoiceId),
    create: (formData: FormData) => 
      createInvoice.execute(tenantId, formData),
    update: (invoiceId: string, data: Parameters<typeof apiClient.invoices.update>[2]) => 
      updateInvoice.execute(tenantId, invoiceId, data),
    delete: (invoiceId: string) => 
      deleteInvoice.execute(tenantId, invoiceId),
    process: (invoiceId: string) => 
      processInvoice.execute(tenantId, invoiceId),
    
    // States
    listState: {
      data: listInvoices.data,
      error: listInvoices.error,
      loading: listInvoices.loading,
    },
    getState: {
      data: getInvoice.data,
      error: getInvoice.error,
      loading: getInvoice.loading,
    },
    createState: {
      data: createInvoice.data,
      error: createInvoice.error,
      loading: createInvoice.loading,
    },
    updateState: {
      data: updateInvoice.data,
      error: updateInvoice.error,
      loading: updateInvoice.loading,
    },
    deleteState: {
      data: deleteInvoice.data,
      error: deleteInvoice.error,
      loading: deleteInvoice.loading,
    },
    processState: {
      data: processInvoice.data,
      error: processInvoice.error,
      loading: processInvoice.loading,
    },
  };
}

// Add more hooks for other API endpoints as needed
