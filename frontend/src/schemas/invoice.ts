import { z } from "zod";

// VAT Line schema
export const vatLineSchema = z.object({
  amount: z.number().positive("Amount must be positive"),
  taxCode: z.string().min(1, "Tax code is required"),
  taxRate: z.number().optional(),
});

export type VATLine = z.infer<typeof vatLineSchema>;

// Invoice fields schema
export const invoiceFieldsSchema = z.object({
  vendorId: z.string().optional(),
  date: z.string().optional().or(z.date()),
  number: z.string().optional(),
  memo: z.string().optional(),
  vatLines: z.array(vatLineSchema).optional(),
  validated: z.boolean().default(false),
});

export type InvoiceFields = z.infer<typeof invoiceFieldsSchema>;

// Invoice update schema
export const invoiceUpdateSchema = z.object({
  fields: invoiceFieldsSchema.optional(),
  status: z.enum(["PENDING", "PROCESSING", "READY", "ERROR"]).optional(),
  qboTxnId: z.string().optional(),
});

export type InvoiceUpdate = z.infer<typeof invoiceUpdateSchema>;

// Invoice response schema
export const invoiceResponseSchema = z.object({
  id: z.string(),
  tenantId: z.string(),
  fileUrl: z.string(),
  sha256: z.string().optional().nullable(),
  fields: invoiceFieldsSchema.optional().nullable(),
  status: z.string(),
  qboTxnId: z.string().optional().nullable(),
  createdAt: z.string().or(z.date()),
  updatedAt: z.string().or(z.date()),
});

export type InvoiceResponse = z.infer<typeof invoiceResponseSchema>;

// Invoice upload form schema
export const invoiceUploadSchema = z.object({
  file: z.instanceof(File, { message: "Please select a file" }),
});

export type InvoiceUpload = z.infer<typeof invoiceUploadSchema>;

// Validation helper functions
export const validateInvoiceUpdate = (
  data: unknown,
): { success: boolean; data?: InvoiceUpdate; error?: z.ZodError } => {
  try {
    const result = invoiceUpdateSchema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
};

export const validateInvoiceUpload = (
  data: unknown,
): { success: boolean; data?: InvoiceUpload; error?: z.ZodError } => {
  try {
    const result = invoiceUploadSchema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
};
