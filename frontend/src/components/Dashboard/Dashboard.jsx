import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON><PERSON>onte<PERSON> } from "@shadcn/ui/tabs";

import PurchasesTab from "./PurchasesTab.tsx";
import ClientBillsTab from "./ClientBillsTab.tsx";
import QuickbooksTab from "./QuickbooksTab.tsx";
import TransactionsTab from "./TransactionsTab.tsx";
import ScanBillStep from "./ScanBillStep.tsx";

export default function Dashboard() {
  // TODO: Replace with actual user token logic
  const token = window.localStorage.getItem("token") || "";

  return (
    <div className="min-h-screen bg-background text-textmain font-sans flex flex-col">
      <Tabs defaultValue="scan" className="w-full max-w-5xl mx-auto mt-10">
        <TabsList className="flex justify-center gap-4">
          <TabsTrigger value="scan">Scan Bill</TabsTrigger>
          <TabsTrigger value="purchases">Purchases</TabsTrigger>
          <TabsTrigger value="clients">Client Bills</TabsTrigger>
          <TabsTrigger value="quickbooks">QuickBooks</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
        </TabsList>
        <TabsContent value="scan">
          <ScanBillStep token={token} />
        </TabsContent>
        <TabsContent value="purchases">
          <PurchasesTab />
        </TabsContent>
        <TabsContent value="clients">
          <ClientBillsTab />
        </TabsContent>
        <TabsContent value="quickbooks">
          <QuickbooksTab />
        </TabsContent>
        <TabsContent value="transactions">
          <TransactionsTab />
        </TabsContent>
      </Tabs>
    </div>
  );
}
