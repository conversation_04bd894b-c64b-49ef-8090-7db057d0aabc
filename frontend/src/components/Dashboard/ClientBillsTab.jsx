import React from "react";
// TODO: Import shared types and Zod schema for client bills if available

/**
 * @returns {JSX.Element}
 */
export default function ClientBillsTab() {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-lg font-bold text-accent mb-4">Client Bills</h2>
      {/* Minimal table/list of client bills with approve/edit actions */}
      <p className="text-textmain">
        No client bills yet. Upload or scan a bill to get started.
      </p>
    </div>
  );
}
