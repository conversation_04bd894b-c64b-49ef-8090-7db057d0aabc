import React from "react";
// TODO: Import shared types and Zod schema for purchases if available

/**
 * @returns {JSX.Element}
 */
export default function PurchasesTab() {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-lg font-bold text-accent mb-4">Purchases</h2>
      {/* Minimal table/list of purchase bills with approve/edit actions */}
      <p className="text-textmain">
        No purchase bills yet. Upload or scan a bill to get started.
      </p>
    </div>
  );
}
