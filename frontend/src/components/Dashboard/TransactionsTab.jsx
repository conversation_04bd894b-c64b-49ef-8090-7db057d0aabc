import React from "react";
// TODO: Import shared types and Zod schema for transactions if available

/**
 * @returns {JSX.Element}
 */
export default function TransactionsTab() {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-lg font-bold text-accent mb-4">Transactions</h2>
      {/* Minimal table/list of transactions with status indicators */}
      <p className="text-textmain">No transactions yet.</p>
    </div>
  );
}
