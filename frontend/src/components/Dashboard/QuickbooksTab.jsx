import React from "react";

/**
 * @returns {JSX.Element}
 */
export default function QuickbooksTab() {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-lg font-bold text-accent mb-4">
        QuickBooks Integration
      </h2>
      {/* Connect/Disconnect QuickBooks, show connection status */}
      <button className="accent-bg rounded px-4 py-2 font-bold">
        Connect QuickBooks
      </button>
      <p className="text-textmain mt-4">Not connected yet.</p>
    </div>
  );
}
