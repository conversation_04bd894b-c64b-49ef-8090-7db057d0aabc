import React from "react";
import { Link, useLocation } from "react-router-dom";
import { useTenant } from "../../contexts/TenantContext.tsx";

const Sidebar = () => {
  const location = useLocation();
  const { currentTenant } = useTenant();

  // Check if the current path matches the menu item
  const isActive = (path) => {
    return location.pathname.startsWith(path);
  };

  // If no tenant is selected, don't show the sidebar
  if (!currentTenant) {
    return null;
  }

  return (
    <div
      className="sidebar bg-dark text-light"
      style={{ width: "250px", minHeight: "100vh" }}
    >
      <div className="p-3 border-bottom border-secondary">
        <h5 className="mb-0">
          <i className="fas fa-building me-2"></i>
          {currentTenant.name}
        </h5>
      </div>

      <div className="p-3">
        <div className="mb-3">
          <small className="text-uppercase text-muted">Main</small>
        </div>

        <ul className="nav flex-column">
          <li className="nav-item">
            <Link
              to="/invoices"
              className={`nav-link ${isActive("/invoices") ? "active" : "text-light"}`}
            >
              <i className="fas fa-file-invoice me-2"></i>
              Invoices
            </Link>
          </li>
          <li className="nav-item">
            <Link
              to="/vendors"
              className={`nav-link ${isActive("/vendors") ? "active" : "text-light"}`}
            >
              <i className="fas fa-building me-2"></i>
              Vendors
            </Link>
          </li>
          <li className="nav-item">
            <Link
              to="/upload"
              className={`nav-link ${isActive("/upload") ? "active" : "text-light"}`}
            >
              <i className="fas fa-upload me-2"></i>
              Upload Invoice
            </Link>
          </li>
        </ul>

        <div className="mb-3 mt-4">
          <small className="text-uppercase text-muted">QuickBooks</small>
        </div>

        <ul className="nav flex-column">
          <li className="nav-item">
            <Link
              to="/qbo-settings"
              className={`nav-link ${isActive("/qbo-settings") ? "active" : "text-light"}`}
            >
              <i className="fas fa-cog me-2"></i>
              QBO Settings
            </Link>
          </li>
        </ul>
      </div>

      <div className="mt-auto p-3 border-top border-secondary">
        <div className="d-grid">
          <Link to="/upload" className="btn btn-primary">
            <i className="fas fa-plus me-2"></i>
            New Invoice
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
