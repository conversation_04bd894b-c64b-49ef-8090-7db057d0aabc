import { FC, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../Auth/AuthProvider.tsx";
import { useTenant } from "../../contexts/TenantContext.tsx";
import { Tenant } from "../../models/Tenant.ts";

/**
 * Navbar component for application header with navigation and user controls
 */
const Navbar: FC = () => {
  const { user, signOut } = useAuth();
  const { currentTenant, tenants, setCurrentTenant } = useTenant();
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [showUserDropdown, setShowUserDropdown] = useState<boolean>(false);
  const navigate = useNavigate();

  const handleSignOut = async (): Promise<void> => {
    try {
      await signOut();
      navigate("/login");
    } catch (error) {
      console.error("Sign out error:", error);
    }
  };

  const handleTenantChange = (tenant: Tenant): void => {
    setCurrentTenant(tenant);
    setShowDropdown(false);
  };

  return (
    <nav className="navbar navbar-expand-lg navbar-dark bg-dark sticky-top shadow-sm">
      <div className="container-fluid">
        <Link className="navbar-brand" to="/">
          <span className="me-2">AIClearBill</span>
          <small className="badge bg-secondary">OCR Invoice Processor</small>
        </Link>

        <button
          className="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarContent"
        >
          <span className="navbar-toggler-icon"></span>
        </button>

        <div className="collapse navbar-collapse" id="navbarContent">
          <ul className="navbar-nav me-auto">
            <li className="nav-item">
              <Link className="nav-link" to="/invoices">
                <i className="fas fa-file-invoice me-1"></i> Invoices
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" to="/vendors">
                <i className="fas fa-building me-1"></i> Vendors
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" to="/upload">
                <i className="fas fa-upload me-1"></i> Upload
              </Link>
            </li>
          </ul>

          {currentTenant && (
            <div className="d-flex me-3">
              <div className="dropdown">
                <button
                  className="btn btn-outline-light dropdown-toggle"
                  type="button"
                  onClick={() => setShowDropdown(!showDropdown)}
                >
                  <i className="fas fa-building me-2"></i>
                  {currentTenant.name}
                </button>
                <ul className={`dropdown-menu ${showDropdown ? "show" : ""}`}>
                  {tenants.map((tenant) => (
                    <li key={tenant.id}>
                      <button
                        className="dropdown-item"
                        onClick={() => handleTenantChange(tenant)}
                      >
                        {tenant.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          <div className="d-flex">
            <div className="dropdown">
              <button
                className="btn btn-link text-light dropdown-toggle"
                type="button"
                onClick={() => setShowUserDropdown(!showUserDropdown)}
              >
                <i className="fas fa-user-circle me-1"></i>
                {user?.email || "User"}
              </button>
              <ul
                className={`dropdown-menu dropdown-menu-end ${showUserDropdown ? "show" : ""}`}
              >
                <li>
                  <button className="dropdown-item" onClick={handleSignOut}>
                    <i className="fas fa-sign-out-alt me-2"></i>
                    Sign Out
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
