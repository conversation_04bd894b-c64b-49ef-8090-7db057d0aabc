// Component imports
import { useEffect, useState } from "react";
import { useInvoices } from "../hooks/useApi.ts";
import {
  invoiceResponseSchema,
  type InvoiceResponse,
} from "../schemas/invoice.ts";
import { z } from "zod";

interface InvoiceListProps {
  tenantId: string;
}

export default function InvoiceList({
  tenantId,
}: InvoiceListProps): JSX.Element {
  const { list, listState } = useInvoices(tenantId);
  const [invoices, setInvoices] = useState<InvoiceResponse[]>([]);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  useEffect(() => {
    const fetchInvoices = async () => {
      try {
        const data = await list();

        // Validate the response data with Zod
        const validationResult = z.array(invoiceResponseSchema).safeParse(data);

        if (validationResult.success) {
          setInvoices(validationResult.data);
          setValidationErrors([]);
        } else {
          console.error("Validation errors:", validationResult.error);
          setValidationErrors(
            validationResult.error.errors.map(
              (err) => `${err.path.join(".")}: ${err.message}`,
            ),
          );
        }
      } catch (error) {
        console.error("Error fetching invoices:", error);
      }
    };

    fetchInvoices();
  }, [list, tenantId]);

  if (listState.loading) {
    return <div>Loading invoices...</div>;
  }

  if (listState.error) {
    return (
      <div className="error-container">
        <h3>Error loading invoices</h3>
        <p>{listState.error.message}</p>
        {listState.error.status === 401 && (
          <p>Please log in to view invoices.</p>
        )}
      </div>
    );
  }

  if (validationErrors.length > 0) {
    return (
      <div className="error-container">
        <h3>Data validation errors</h3>
        <ul>
          {validationErrors.map((error, index) => (
            <li key={index}>{error}</li>
          ))}
        </ul>
      </div>
    );
  }

  return (
    <div className="invoice-list">
      <h2>Invoices</h2>

      {invoices.length === 0 ? (
        <p>No invoices found.</p>
      ) : (
        <table className="invoice-table">
          <thead>
            <tr>
              <th>Invoice #</th>
              <th>Vendor</th>
              <th>Date</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {invoices.map((invoice) => (
              <tr key={invoice.id}>
                <td>{invoice.fields?.number || "N/A"}</td>
                <td>{invoice.fields?.vendorId || "N/A"}</td>
                <td>
                  {invoice.fields?.date
                    ? new Date(invoice.fields.date).toLocaleDateString()
                    : "N/A"}
                </td>
                <td>{invoice.status}</td>
                <td>
                  <button>View</button>
                  <button>Edit</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
}
