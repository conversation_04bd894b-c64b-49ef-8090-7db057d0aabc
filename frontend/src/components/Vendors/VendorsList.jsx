import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { api } from "../../services/api.ts";
import { useTenant } from "../../contexts/TenantContext.tsx";

export const VendorsList = () => {
  const [vendors, setVendors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const { currentTenant } = useTenant();
  const navigate = useNavigate();

  useEffect(() => {
    if (!currentTenant) return;

    const fetchVendors = async () => {
      try {
        setLoading(true);
        const params = new URLSearchParams();

        if (searchQuery) {
          params.append("name", searchQuery);
        }

        const response = await api.get(
          `/v1/${currentTenant.id}/vendors/?${params.toString()}`,
        );
        setVendors(response.data);
      } catch (err) {
        console.error("Error fetching vendors:", err);
        setError("Failed to load vendors. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchVendors();
  }, [currentTenant, searchQuery]);

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleEditVendor = (id) => {
    navigate(`/vendors/${id}`);
  };

  if (!currentTenant) {
    return (
      <div className="alert alert-warning">
        Please select a tenant to view vendors.
      </div>
    );
  }

  return (
    <div className="container-fluid px-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1 className="h3">Vendors</h1>
        <Link to="/vendors/new" className="btn btn-primary">
          <i className="fas fa-plus me-2"></i>Add Vendor
        </Link>
      </div>

      <div className="card mb-4">
        <div className="card-header">
          <div className="input-group">
            <span className="input-group-text">
              <i className="fas fa-search"></i>
            </span>
            <input
              type="text"
              className="form-control"
              placeholder="Search vendors..."
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>
        </div>
        <div className="card-body">
          {error && <div className="alert alert-danger">{error}</div>}

          {loading ? (
            <div className="d-flex justify-content-center my-5">
              <div className="spinner-border" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : vendors.length === 0 ? (
            <div className="text-center my-5">
              <p className="mb-3">No vendors found</p>
              <Link to="/vendors/new" className="btn btn-primary">
                Add your first vendor
              </Link>
            </div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Default Account</th>
                    <th>Default Tax Code</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {vendors.map((vendor) => (
                    <tr
                      key={vendor.id}
                      onClick={() => handleEditVendor(vendor.id)}
                      style={{ cursor: "pointer" }}
                    >
                      <td>{vendor.name}</td>
                      <td>{vendor.defaultAccount || "-"}</td>
                      <td>{vendor.defaultTaxCode || "-"}</td>
                      <td>
                        <div className="btn-group" role="group">
                          <button
                            className="btn btn-sm btn-outline-primary"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(`/vendors/${vendor.id}`);
                            }}
                          >
                            <i className="fas fa-edit"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
