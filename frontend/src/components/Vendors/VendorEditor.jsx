import React, { useState, useEffect } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { api } from "../../services/api.ts";
import { useTenant } from "../../contexts/TenantContext.tsx";

const VendorEditor = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { currentTenant } = useTenant();
  const isNewVendor = id === "new";

  // Get suggested name from location state if creating a new vendor
  const suggestedName = location.state?.name || "";

  const [vendor, setVendor] = useState(null);
  const [loading, setLoading] = useState(!isNewVendor);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    name: suggestedName,
    defaultAccount: "",
    defaultTaxCode: "",
  });
  const [accounts, setAccounts] = useState([]);
  const [taxCodes, setTaxCodes] = useState([]);
  const [qboConnected, setQboConnected] = useState(false);

  // Fetch vendor data and QBO data
  useEffect(() => {
    if (!currentTenant) return;

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch QBO accounts and tax codes
        try {
          const accountsResponse = await api.get(
            `/v1/${currentTenant.id}/qbo/accounts`,
          );
          setAccounts(accountsResponse.data);

          const taxCodesResponse = await api.get(
            `/v1/${currentTenant.id}/qbo/tax-codes`,
          );
          setTaxCodes(taxCodesResponse.data);

          setQboConnected(true);
        } catch (err) {
          console.log("QBO data not available, QBO may not be connected");
          setQboConnected(false);
        }

        // Fetch vendor data if editing
        if (!isNewVendor) {
          const vendorResponse = await api.get(
            `/v1/${currentTenant.id}/vendors/${id}`,
          );
          setVendor(vendorResponse.data);

          // Initialize form data
          setFormData({
            name: vendorResponse.data.name || "",
            defaultAccount: vendorResponse.data.defaultAccount || "",
            defaultTaxCode: vendorResponse.data.defaultTaxCode || "",
          });
        }
      } catch (err) {
        console.error("Error fetching data:", err);
        if (!isNewVendor) {
          setError("Failed to load vendor data. Please try again.");
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentTenant, id, isNewVendor]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Save vendor
  const handleSave = async () => {
    if (!currentTenant) return;

    try {
      setSaving(true);
      setError(null);

      if (!formData.name.trim()) {
        setError("Vendor name is required");
        return;
      }

      // Prepare API request
      let response;

      if (isNewVendor) {
        // Create new vendor
        response = await api.post(`/v1/${currentTenant.id}/vendors/`, formData);
      } else {
        // Update existing vendor
        response = await api.patch(
          `/v1/${currentTenant.id}/vendors/${id}`,
          formData,
        );
      }

      // Navigate back to vendors list
      navigate("/vendors");
    } catch (err) {
      console.error("Error saving vendor:", err);
      setError("Failed to save vendor. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="d-flex justify-content-center my-5">
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid px-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1 className="h3">{isNewVendor ? "Add Vendor" : "Edit Vendor"}</h1>
        <div>
          <button
            className="btn btn-outline-secondary me-2"
            onClick={() => navigate("/vendors")}
          >
            Cancel
          </button>
          <button
            className="btn btn-primary"
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? "Saving..." : "Save"}
          </button>
        </div>
      </div>

      {error && <div className="alert alert-danger mb-4">{error}</div>}

      <div className="card">
        <div className="card-header">Vendor Information</div>
        <div className="card-body">
          <div className="mb-3">
            <label htmlFor="name" className="form-label">
              Vendor Name *
            </label>
            <input
              type="text"
              className="form-control"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="mb-3">
            <label htmlFor="defaultAccount" className="form-label">
              Default Account
            </label>
            {qboConnected ? (
              <select
                className="form-select"
                id="defaultAccount"
                name="defaultAccount"
                value={formData.defaultAccount}
                onChange={handleInputChange}
              >
                <option value="">Select Account</option>
                {accounts.map((account) => (
                  <option key={account.id} value={account.id}>
                    {account.fully_qualified_name} ({account.account_type})
                  </option>
                ))}
              </select>
            ) : (
              <div className="input-group">
                <input
                  type="text"
                  className="form-control"
                  id="defaultAccount"
                  name="defaultAccount"
                  value={formData.defaultAccount}
                  onChange={handleInputChange}
                  placeholder="QBO account ID"
                />
                <span className="input-group-text bg-warning text-dark">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  QBO not connected
                </span>
              </div>
            )}
            <div className="form-text">
              This account will be automatically selected for this vendor&apos;s
              invoices.
            </div>
          </div>

          <div className="mb-3">
            <label htmlFor="defaultTaxCode" className="form-label">
              Default Tax Code
            </label>
            {qboConnected ? (
              <select
                className="form-select"
                id="defaultTaxCode"
                name="defaultTaxCode"
                value={formData.defaultTaxCode}
                onChange={handleInputChange}
              >
                <option value="">Select Tax Code</option>
                {taxCodes.map((code) => (
                  <option key={code.id} value={code.id}>
                    {code.name} {code.tax_rate ? `(${code.tax_rate}%)` : ""}
                  </option>
                ))}
              </select>
            ) : (
              <div className="input-group">
                <input
                  type="text"
                  className="form-control"
                  id="defaultTaxCode"
                  name="defaultTaxCode"
                  value={formData.defaultTaxCode}
                  onChange={handleInputChange}
                  placeholder="QBO tax code ID"
                />
                <span className="input-group-text bg-warning text-dark">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  QBO not connected
                </span>
              </div>
            )}
            <div className="form-text">
              This tax code will be automatically selected for this
              vendor&apos;s invoices.
            </div>
          </div>
        </div>
        <div className="card-footer">
          <div className="text-muted small">* Required fields</div>
        </div>
      </div>

      {!qboConnected && (
        <div className="alert alert-warning mt-4">
          <i className="fas fa-exclamation-triangle me-2"></i>
          <strong>QuickBooks Online is not connected.</strong> Some features may
          not work correctly.
          <br />
          Connect QuickBooks to get account and tax code information.
        </div>
      )}
    </div>
  );
};

export default VendorEditor;
