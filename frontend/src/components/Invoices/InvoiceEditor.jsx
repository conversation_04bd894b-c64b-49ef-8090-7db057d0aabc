import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { api } from "../../services/api.ts";
import { useTenant } from "../../contexts/TenantContext.tsx";
import { InvoiceSchema } from "../../types/invoiceSchema.ts";

const InvoiceEditor = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentTenant } = useTenant();

  const [invoice, setInvoice] = useState(null);
  const [vendors, setVendors] = useState([]);
  const [taxCodes, setTaxCodes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [pushing, setPushing] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    vendor: "",
    amount: 0,
    status: "PENDING",
    createdAt: "",
  });
  const [validationErrors, setValidationErrors] = useState(null);

  // Fetch the invoice, vendors, and tax codes on component mount
  useEffect(() => {
    if (!currentTenant || !id) return;

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch invoice data
        const invoiceResponse = await api.get(
          `/v1/${currentTenant.id}/invoices/${id}`,
        );
        setInvoice(invoiceResponse.data);

        // Initialize form data from invoice
        if (invoiceResponse.data.fields) {
          setFormData({
            vendorId: invoiceResponse.data.fields.vendorId || "",
            date: invoiceResponse.data.fields.date || "",
            number: invoiceResponse.data.fields.number || "",
            memo: invoiceResponse.data.fields.memo || "",
            vatLines: invoiceResponse.data.fields.vatLines || [],
            validated: invoiceResponse.data.fields.validated || false,
          });
        }

        // Fetch vendors for dropdown
        const vendorsResponse = await api.get(
          `/v1/${currentTenant.id}/vendors/`,
        );
        setVendors(vendorsResponse.data);

        // Fetch tax codes if QBO is connected
        try {
          const taxCodesResponse = await api.get(
            `/v1/${currentTenant.id}/qbo/tax-codes`,
          );
          setTaxCodes(taxCodesResponse.data);
        } catch (err) {
          console.log("Tax codes not available, QBO may not be connected");
          // Non-critical error, don't show to user
        }
      } catch (err) {
        console.error("Error fetching invoice data:", err);
        setError("Failed to load invoice data. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentTenant, id]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle VAT line changes
  const handleVatLineChange = (index, field, value) => {
    setFormData((prev) => {
      const updatedVatLines = [...prev.vatLines];
      updatedVatLines[index] = {
        ...updatedVatLines[index],
        [field]: value,
      };
      return {
        ...prev,
        vatLines: updatedVatLines,
      };
    });
  };

  // Add new VAT line
  const addVatLine = () => {
    setFormData((prev) => ({
      ...prev,
      vatLines: [...prev.vatLines, { amount: 0, taxCode: "" }],
    }));
  };

  // Remove VAT line
  const removeVatLine = (index) => {
    setFormData((prev) => {
      const updatedVatLines = [...prev.vatLines];
      updatedVatLines.splice(index, 1);
      return {
        ...prev,
        vatLines: updatedVatLines,
      };
    });
  };

  // Save invoice changes
  const handleSave = async () => {
    if (!currentTenant || !id) return;
    setSaving(true);
    setError(null);
    setValidationErrors(null);
    // Zod validation
    const result = InvoiceSchema.safeParse(formData);
    if (!result.success) {
      setValidationErrors(result.error.flatten().fieldErrors);
      setSaving(false);
      return;
    }
    try {
      // Prepare data for API
      const updateData = {
        ...formData,
        validated: true,
      };
      await api.patch(`/v1/${currentTenant.id}/invoices/${id}`, updateData);
      navigate("/invoices");
    } catch (err) {
      console.error("Error saving invoice:", err);
      setError("Failed to save invoice. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  // Push invoice to QuickBooks Online
  const handlePushToQBO = async () => {
    if (!currentTenant || !id) return;

    try {
      setPushing(true);
      setError(null);

      // First save the invoice to ensure it's validated
      await handleSave();

      // Push to QBO
      const response = await api.post(
        `/v1/${currentTenant.id}/qbo/push-invoice/${id}`,
      );

      if (response.data.success) {
        // Success - update local state and redirect
        setInvoice((prev) => ({
          ...prev,
          status: "PUSHED",
          qboTxnId: response.data.qbo_txn_id,
        }));

        navigate("/invoices");
      } else {
        throw new Error(
          response.data.error || "Unknown error when pushing to QBO",
        );
      }
    } catch (err) {
      console.error("Error pushing to QBO:", err);
      setError(`Failed to push to QuickBooks: ${err.message}`);
    } finally {
      setPushing(false);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="d-flex justify-content-center my-5">
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  // Show error state
  if (!invoice) {
    return (
      <div className="alert alert-danger">
        Invoice not found or error loading data.
        <button
          className="btn btn-primary ms-3"
          onClick={() => navigate("/invoices")}
        >
          Back to Invoices
        </button>
      </div>
    );
  }

  return (
    <div className="container-fluid px-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1 className="h3">Edit Invoice</h1>
        <div>
          <button
            className="btn btn-outline-secondary me-2"
            onClick={() => navigate("/invoices")}
          >
            Cancel
          </button>
          <button
            className="btn btn-success me-2"
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? "Saving..." : "Save"}
          </button>
          {invoice.status === "READY" && (
            <button
              className="btn btn-primary"
              onClick={handlePushToQBO}
              disabled={pushing}
            >
              {pushing ? "Pushing..." : "Push to QuickBooks"}
            </button>
          )}
        </div>
      </div>

      {error && <div className="alert alert-danger mb-4">{error}</div>}
      {validationErrors && (
        <div className="alert alert-warning mb-4">
          <ul>
            {Object.entries(validationErrors).map(([field, msgs]) =>
              msgs.map((msg, idx) => (
                <li key={field + idx}>
                  {field}: {msg}
                </li>
              )),
            )}
          </ul>
        </div>
      )}

      <div className="row">
        <div className="col-md-6">
          <div className="card mb-4">
            <div className="card-header">Invoice Details</div>
            <div className="card-body">
              <div className="mb-3">
                <label htmlFor="vendorId" className="form-label">
                  Vendor
                </label>
                <select
                  id="vendorId"
                  name="vendorId"
                  className="form-select"
                  value={formData.vendorId}
                  onChange={handleInputChange}
                >
                  <option value="">Select Vendor</option>
                  {vendors.map((vendor) => (
                    <option key={vendor.id} value={vendor.id}>
                      {vendor.name}
                    </option>
                  ))}
                </select>
                {invoice.fields?.newVendorName && !formData.vendorId && (
                  <div className="alert alert-info mt-2">
                    <strong>New vendor detected: </strong>
                    {invoice.fields.newVendorName}
                    <button
                      className="btn btn-sm btn-primary ms-2"
                      onClick={() =>
                        navigate("/vendors/new", {
                          state: { name: invoice.fields.newVendorName },
                        })
                      }
                    >
                      Create Vendor
                    </button>
                  </div>
                )}
              </div>

              <div className="mb-3">
                <label htmlFor="date" className="form-label">
                  Invoice Date
                </label>
                <input
                  type="date"
                  id="date"
                  name="date"
                  className="form-control"
                  value={formData.date}
                  onChange={handleInputChange}
                />
              </div>

              <div className="mb-3">
                <label htmlFor="number" className="form-label">
                  Invoice Number
                </label>
                <input
                  type="text"
                  id="number"
                  name="number"
                  className="form-control"
                  value={formData.number}
                  onChange={handleInputChange}
                />
              </div>

              <div className="mb-3">
                <label htmlFor="memo" className="form-label">
                  Memo / Description
                </label>
                <textarea
                  id="memo"
                  name="memo"
                  className="form-control"
                  rows="3"
                  value={formData.memo}
                  onChange={handleInputChange}
                ></textarea>
              </div>
            </div>
          </div>
        </div>

        <div className="col-md-6">
          <div className="card mb-4">
            <div className="card-header d-flex justify-content-between align-items-center">
              <span>VAT / Tax Details</span>
              <button className="btn btn-sm btn-primary" onClick={addVatLine}>
                Add Line
              </button>
            </div>
            <div className="card-body">
              {formData.vatLines.length === 0 ? (
                <div className="text-center py-4">
                  <p>No VAT lines found</p>
                  <button className="btn btn-primary" onClick={addVatLine}>
                    Add VAT Line
                  </button>
                </div>
              ) : (
                formData.vatLines.map((line, index) => (
                  <div key={index} className="card mb-3">
                    <div className="card-body">
                      <div className="row g-2">
                        <div className="col-md-5">
                          <label className="form-label">Amount</label>
                          <div className="input-group">
                            <span className="input-group-text">$</span>
                            <input
                              type="number"
                              className="form-control"
                              value={line.amount}
                              onChange={(e) =>
                                handleVatLineChange(
                                  index,
                                  "amount",
                                  parseFloat(e.target.value),
                                )
                              }
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div className="col-md-5">
                          <label className="form-label">Tax Code</label>
                          <select
                            className="form-select"
                            value={line.taxCode}
                            onChange={(e) =>
                              handleVatLineChange(
                                index,
                                "taxCode",
                                e.target.value,
                              )
                            }
                          >
                            <option value="">Select Tax Code</option>
                            {taxCodes.map((code) => (
                              <option key={code.id} value={code.id}>
                                {code.name}{" "}
                                {code.tax_rate ? `(${code.tax_rate}%)` : ""}
                              </option>
                            ))}
                          </select>
                        </div>
                        <div className="col-md-2 d-flex align-items-end">
                          <button
                            className="btn btn-outline-danger"
                            onClick={() => removeVatLine(index)}
                          >
                            <i className="fas fa-trash"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          <div className="card mb-4">
            <div className="card-header">Invoice Preview</div>
            <div className="card-body">
              {invoice.fileUrl ? (
                <div className="text-center">
                  <img
                    src={invoice.fileUrl}
                    alt="Invoice Preview"
                    className="img-fluid border"
                    style={{ maxHeight: "300px" }}
                  />
                  <div className="mt-2">
                    <a
                      href={invoice.fileUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="btn btn-sm btn-outline-primary"
                    >
                      Open Full Size
                    </a>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p>No preview available</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {invoice.status === "PUSHED" && (
        <div className="alert alert-success">
          <i className="fas fa-check-circle me-2"></i>
          This invoice has been pushed to QuickBooks.
          {invoice.qboTxnId && (
            <span className="ms-2">Transaction ID: {invoice.qboTxnId}</span>
          )}
        </div>
      )}
    </div>
  );
};

export default InvoiceEditor;
