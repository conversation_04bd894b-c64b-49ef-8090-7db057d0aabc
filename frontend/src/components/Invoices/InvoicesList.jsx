import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { api } from "../../services/api.ts";
import { useTenant } from "../../contexts/TenantContext.tsx";
import { formatDate } from "../../utils/formatters.ts";

const InvoicesList = () => {
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filterStatus, setFilterStatus] = useState("");
  const [searchVendor, setSearchVendor] = useState("");
  const { currentTenant } = useTenant();
  const navigate = useNavigate();

  useEffect(() => {
    if (!currentTenant) return;

    const fetchInvoices = async () => {
      try {
        setLoading(true);
        const params = new URLSearchParams();

        if (filterStatus) {
          params.append("status", filterStatus);
        }

        if (searchVendor) {
          params.append("vendor_id", searchVendor);
        }

        const response = await api.get(
          `/v1/${currentTenant.id}/invoices/?${params.toString()}`,
        );
        setInvoices(response.data);
      } catch (err) {
        console.error("Error fetching invoices:", err);
        setError("Failed to load invoices. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchInvoices();
  }, [currentTenant, filterStatus, searchVendor]);

  const handleStatusFilter = (status) => {
    setFilterStatus(status);
  };

  const handleViewInvoice = (id) => {
    navigate(`/invoices/${id}`);
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case "PENDING":
        return "bg-warning text-dark";
      case "READY":
        return "bg-success";
      case "PUSHED":
        return "bg-info";
      default:
        return "bg-secondary";
    }
  };

  if (!currentTenant) {
    return (
      <div className="alert alert-warning">
        Please select a tenant to view invoices.
      </div>
    );
  }

  return (
    <div className="container-fluid px-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1 className="h3">Invoices</h1>
        <Link to="/upload" className="btn btn-primary">
          <i className="fas fa-plus me-2"></i>Upload Invoice
        </Link>
      </div>

      <div className="card mb-4">
        <div className="card-header">
          <div className="row align-items-center">
            <div className="col-md-8">
              <div className="btn-group" role="group">
                <button
                  type="button"
                  className={`btn ${!filterStatus ? "btn-primary" : "btn-outline-secondary"}`}
                  onClick={() => handleStatusFilter("")}
                >
                  All
                </button>
                <button
                  type="button"
                  className={`btn ${filterStatus === "PENDING" ? "btn-primary" : "btn-outline-secondary"}`}
                  onClick={() => handleStatusFilter("PENDING")}
                >
                  Pending
                </button>
                <button
                  type="button"
                  className={`btn ${filterStatus === "READY" ? "btn-primary" : "btn-outline-secondary"}`}
                  onClick={() => handleStatusFilter("READY")}
                >
                  Ready
                </button>
                <button
                  type="button"
                  className={`btn ${filterStatus === "PUSHED" ? "btn-primary" : "btn-outline-secondary"}`}
                  onClick={() => handleStatusFilter("PUSHED")}
                >
                  Pushed to QBO
                </button>
              </div>
            </div>
            <div className="col-md-4">
              <input
                type="text"
                className="form-control"
                placeholder="Search by vendor"
                value={searchVendor}
                onChange={(e) => setSearchVendor(e.target.value)}
              />
            </div>
          </div>
        </div>
        <div className="card-body">
          {error && <div className="alert alert-danger">{error}</div>}

          {loading ? (
            <div className="d-flex justify-content-center my-5">
              <div className="spinner-border" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : invoices.length === 0 ? (
            <div className="text-center my-5">
              <p className="mb-3">No invoices found</p>
              <Link to="/upload" className="btn btn-primary">
                Upload your first invoice
              </Link>
            </div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover">
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Invoice #</th>
                    <th>Vendor</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {invoices.map((invoice) => (
                    <tr
                      key={invoice.id}
                      onClick={() => handleViewInvoice(invoice.id)}
                      style={{ cursor: "pointer" }}
                    >
                      <td>
                        {invoice.fields?.date
                          ? formatDate(invoice.fields.date)
                          : "-"}
                      </td>
                      <td>{invoice.fields?.number || "-"}</td>
                      <td>
                        {invoice.fields?.vendorId ||
                          invoice.fields?.newVendorName ||
                          "-"}
                      </td>
                      <td>
                        <span
                          className={`badge ${getStatusBadgeClass(invoice.status)}`}
                        >
                          {invoice.status}
                        </span>
                      </td>
                      <td>
                        <div className="btn-group" role="group">
                          <button
                            className="btn btn-sm btn-outline-primary"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(`/invoices/${invoice.id}`);
                            }}
                          >
                            <i className="fas fa-edit"></i>
                          </button>
                          {invoice.status === "READY" && (
                            <button
                              className="btn btn-sm btn-outline-success"
                              onClick={(e) => {
                                e.stopPropagation();
                                // Push to QBO logic would go here
                              }}
                              title="Push to QuickBooks"
                            >
                              <i className="fas fa-cloud-upload-alt"></i>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default InvoicesList;
