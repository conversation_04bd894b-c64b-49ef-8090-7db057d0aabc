import React, { useRef, useState, useEffect } from "react";

/**
 * InvoiceCamera component
 *
 * @param {{ onCapture: (file: File) => void }} props
 * @returns {JSX.Element}
 */
/**
 * @param {{ onCapture: (file: File) => void }} props
 * @returns {JSX.Element}
 */
function InvoiceCamera({ onCapture }) {
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const [stream, setStream] = useState(null);
  const [error, setError] = useState(null);
  const [cameraReady, setCameraReady] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [facingMode, setFacingMode] = useState("environment"); // 'environment' or 'user'

  // Initialize camera on component mount
  useEffect(() => {
    // Check if running on mobile
    setIsMobile(/iPhone|iPad|iPod|Android/i.test(navigator.userAgent));

    // Start camera
    startCamera();

    // Clean up on unmount
    return () => {
      stopCamera();
    };
  }, [facingMode]);

  // Start camera
  const startCamera = async () => {
    try {
      setError(null);

      // Stop any existing stream
      if (stream) {
        stopCamera();
      }

      // Get video constraints
      const constraints = {
        video: {
          facingMode: facingMode,
          width: { ideal: 1280 },
          height: { ideal: 720 },
        },
      };

      // Request camera access
      const mediaStream =
        await navigator.mediaDevices.getUserMedia(constraints);
      setStream(mediaStream);

      // Set video source
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        videoRef.current.onloadedmetadata = () => {
          setCameraReady(true);
        };
      }
    } catch (err) {
      console.error("Error accessing camera:", err);
      setCameraReady(false);

      // Handle specific errors
      if (err.name === "NotAllowedError") {
        setError(
          "Camera access denied. Please allow camera access to use this feature.",
        );
      } else if (err.name === "NotFoundError") {
        setError(
          "No camera found. Please ensure your device has a camera and try again.",
        );
      } else {
        setError(`Error accessing camera: ${err.message}`);
      }
    }
  };

  // Stop camera
  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach((track) => track.stop());
      setStream(null);
    }
    setCameraReady(false);
  };

  // Switch camera (front/back)
  const switchCamera = () => {
    setFacingMode((prevMode) =>
      prevMode === "environment" ? "user" : "environment",
    );
  };

  // Capture image
  const captureImage = () => {
    if (!cameraReady || !videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw video frame to canvas
    const context = canvas.getContext("2d");
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert to blob and pass to parent component
    canvas.toBlob(
      (blob) => {
        if (onCapture && blob) {
          onCapture(blob);
        }
      },
      "image/jpeg",
      0.95,
    );

    // Stop camera after capture
    stopCamera();
  };

  return (
    <div className="invoice-camera">
      {error && <div className="alert alert-danger">{error}</div>}

      <div className="position-relative">
        {/* Video preview */}
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted
          className={`w-100 border rounded ${!cameraReady ? "d-none" : ""}`}
          style={{ maxHeight: "500px", backgroundColor: "#000" }}
        ></video>

        {/* Camera not ready state */}
        {!cameraReady && !error && (
          <div
            className="border rounded d-flex justify-content-center align-items-center bg-dark"
            style={{ height: "300px" }}
          >
            <div className="text-center">
              <div className="spinner-border text-light mb-3" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="text-light">Initializing camera...</p>
            </div>
          </div>
        )}

        {/* Hidden canvas for captures */}
        <canvas ref={canvasRef} className="d-none"></canvas>

        {/* Camera controls */}
        {cameraReady && (
          <div className="camera-controls position-absolute bottom-0 w-100 d-flex justify-content-center p-3">
            <div className="btn-group">
              {isMobile && (
                <button
                  className="btn btn-outline-light"
                  onClick={switchCamera}
                  title="Switch Camera"
                >
                  <i className="fas fa-sync-alt"></i>
                </button>
              )}
              <button
                className="btn btn-light rounded-circle mx-2"
                onClick={captureImage}
                style={{ width: "60px", height: "60px" }}
              >
                <i className="fas fa-camera"></i>
              </button>
              <button
                className="btn btn-outline-light"
                onClick={stopCamera}
                title="Cancel"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
          </div>
        )}
      </div>

      {!cameraReady && error && (
        <div className="text-center mt-3">
          <button className="btn btn-primary" onClick={startCamera}>
            Retry Camera Access
          </button>
        </div>
      )}
    </div>
  );
}

export default InvoiceCamera;
