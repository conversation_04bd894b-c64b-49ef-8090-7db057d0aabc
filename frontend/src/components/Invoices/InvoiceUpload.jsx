import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { api } from "../../services/api.ts";
import { useTenant } from "../../contexts/TenantContext.tsx";
import InvoiceCamera from "./InvoiceCamera.tsx";

const InvoiceUpload = () => {
  const [file, setFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState(null);
  const [dragActive, setDragActive] = useState(false);
  const [uploadMethod, setUploadMethod] = useState("file"); // 'file' or 'camera'
  const { currentTenant } = useTenant();
  const navigate = useNavigate();

  // Handle file selection
  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    handleFile(selectedFile);
  };

  // Handle file processing
  const handleFile = (selectedFile) => {
    if (!selectedFile) return;

    // Validate file type
    const validTypes = ["image/jpeg", "image/png", "application/pdf"];
    if (!validTypes.includes(selectedFile.type)) {
      setError("Invalid file type. Please upload a PDF, JPEG, or PNG file.");
      return;
    }

    // Validate file size (max 10MB)
    if (selectedFile.size > 10 * 1024 * 1024) {
      setError("File is too large. Maximum file size is 10MB.");
      return;
    }

    setFile(selectedFile);
    setError(null);

    // Generate preview for images
    if (selectedFile.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreview(e.target.result);
      };
      reader.readAsDataURL(selectedFile);
    } else {
      // For PDFs, just show a placeholder
      setPreview(null);
    }
  };

  // Handle drag and drop events
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  // Handle camera capture
  const handleCameraCapture = (capturedImageBlob) => {
    // Convert blob to File object
    const capturedFile = new File([capturedImageBlob], "camera-capture.jpg", {
      type: "image/jpeg",
    });
    handleFile(capturedFile);
  };

  // Handle upload to server
  const handleUpload = async () => {
    if (!file || !currentTenant) return;

    try {
      setUploading(true);
      setError(null);

      // Create form data
      const formData = new FormData();
      formData.append("file", file);

      // Upload the file
      const response = await api.post(
        `/v1/${currentTenant.id}/invoices/`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        },
      );

      // Navigate to the invoice editor
      navigate(`/invoices/${response.data.id}`);
    } catch (err) {
      console.error("Error uploading invoice:", err);
      setError("Failed to upload invoice. Please try again.");
    } finally {
      setUploading(false);
    }
  };

  if (!currentTenant) {
    return (
      <div className="alert alert-warning">
        Please select a tenant to upload invoices.
      </div>
    );
  }

  return (
    <div className="container-fluid px-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1 className="h3">Upload Invoice</h1>
        <button
          className="btn btn-outline-secondary"
          onClick={() => navigate("/invoices")}
        >
          Cancel
        </button>
      </div>

      <div className="card mb-4">
        <div className="card-header">
          <ul className="nav nav-tabs card-header-tabs">
            <li className="nav-item">
              <button
                className={`nav-link ${uploadMethod === "file" ? "active" : ""}`}
                onClick={() => setUploadMethod("file")}
              >
                <i className="fas fa-upload me-2"></i>
                Upload File
              </button>
            </li>
            <li className="nav-item">
              <button
                className={`nav-link ${uploadMethod === "camera" ? "active" : ""}`}
                onClick={() => setUploadMethod("camera")}
              >
                <i className="fas fa-camera me-2"></i>
                Use Camera
              </button>
            </li>
          </ul>
        </div>
        <div className="card-body">
          {error && <div className="alert alert-danger mb-4">{error}</div>}

          {uploadMethod === "file" ? (
            <div
              className={`border rounded p-5 text-center ${dragActive ? "bg-light border-primary" : ""}`}
              onDragEnter={handleDrag}
              onDragOver={handleDrag}
              onDragLeave={handleDrag}
              onDrop={handleDrop}
            >
              <div className="mb-4">
                <i className="fas fa-file-invoice fa-4x text-muted"></i>
              </div>
              <p className="mb-4">
                Drag & drop your invoice file here, or click to browse
              </p>
              <label className="btn btn-primary mb-3">
                Browse Files
                <input
                  type="file"
                  className="d-none"
                  accept=".pdf,.jpg,.jpeg,.png"
                  onChange={handleFileChange}
                />
              </label>
              <p className="text-muted small">
                Supported formats: PDF, JPEG, PNG (Max size: 10MB)
              </p>
            </div>
          ) : (
            <InvoiceCamera onCapture={handleCameraCapture} />
          )}

          {(file || preview) && (
            <div className="mt-4">
              <h5>Selected File</h5>
              <div className="d-flex align-items-center">
                <div className="me-3">
                  {preview ? (
                    <img
                      src={preview}
                      alt="Preview"
                      className="img-thumbnail"
                      style={{ maxHeight: "200px", maxWidth: "200px" }}
                    />
                  ) : (
                    <i className="fas fa-file-pdf fa-4x text-danger"></i>
                  )}
                </div>
                <div>
                  <p className="mb-1">{file?.name}</p>
                  <p className="text-muted small mb-0">
                    {file?.type} - {(file?.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="card-footer">
          <button
            className="btn btn-primary"
            disabled={!file || uploading}
            onClick={handleUpload}
          >
            {uploading ? (
              <>
                <span
                  className="spinner-border spinner-border-sm me-2"
                  role="status"
                  aria-hidden="true"
                ></span>
                Uploading...
              </>
            ) : (
              "Upload Invoice"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default InvoiceUpload;
