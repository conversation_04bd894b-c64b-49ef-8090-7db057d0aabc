name: CI

on:
  push:
    branches: [main, master]
  pull_request:
    branches: [main, master]

jobs:
  lint-and-typecheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Install dependencies
        run: npm ci
        working-directory: ./frontend
      - name: TypeScript strict check
        run: npx tsc --noEmit
        working-directory: ./frontend
      - name: ESLint strict check
        run: npx eslint . --ext .js,.jsx,.ts,.tsx
        working-directory: ./frontend
