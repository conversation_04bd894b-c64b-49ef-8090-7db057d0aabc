{"name": "billsnapp-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "generate-api-types": "node scripts/generate-api-types.js", "prebuild": "npm run generate-api-types"}, "dependencies": {"@radix-ui/react-icons": "^1.3.0", "@shadcn/ui": "0.0.4", "@tanstack/react-table": "^8.10.0", "clsx": "^2.0.0", "lucide-react": "^0.340.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.0", "tailwindcss": "^3.4.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.15", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.3.1", "eslint-plugin-react": "^7.37.5", "openapi-typescript": "^6.7.6", "postcss": "^8.4.24", "prettier": "^3.5.3", "typescript": "^5.8.3", "vite": "^4.5.0", "zod": "^3.24.4"}}