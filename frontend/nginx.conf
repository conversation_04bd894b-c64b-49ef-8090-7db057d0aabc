server {
    listen 80;
    server_name localhost; # Adjust if you have a domain

    # Serve React App
    root /usr/share/nginx/html;
    index index.html index.htm;

    location / {
        # Try serving file directly, then directory, then fallback to index.html for SPA routing
        try_files $uri $uri/ /index.html;
    }

    # Proxy API requests to the backend service
    location /api/ {
        # The backend service name from docker-compose.yml
        proxy_pass http://backend:8000; # Pass to the root of the backend service

        # Add appropriate headers for backend
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Ensure redirects work correctly
        proxy_redirect off;
    }

    # Optional: Add error pages, logs, etc.
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
