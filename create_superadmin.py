"""
<PERSON><PERSON><PERSON> to create a superadmin account for testing.
Run this script once to set up the first superadmin account.
"""

from datetime import datetime

from werkzeug.security import generate_password_hash

from api.models.system_metrics import SuperAdminUser
from app import app, db


def create_admin():
    """Create a default superadmin account"""
    with app.app_context():
        # Check if any superadmin account exists
        if SuperAdminUser.query.count() > 0:
            print("SuperAdmin account already exists!")
            return

        # Create superadmin account
        admin = SuperAdminUser(
            username="admin",
            email="<EMAIL>",
            password_hash=generate_password_hash("Admin123!@#"),
            first_name="Super",
            last_name="Admin",
            created_at=datetime.utcnow(),
        )
        db.session.add(admin)
        db.session.commit()

        print(f"SuperAdmin account created successfully!")
        print(f"Username: admin")
        print(f"Password: Admin123!@#")
        print(f"Login at: /loginadmin/login")


if __name__ == "__main__":
    create_admin()
