#!/usr/bin/env node
/**
 * check-env-vars.js
 * 
 * CI guard that ensures all environment variables referenced in the codebase
 * are documented in .env.sample to prevent "missing API_KEY" surprises.
 * 
 * Usage:
 *   node scripts/check-env-vars.js
 * 
 * Exit codes:
 *   0 - All environment variables are documented
 *   1 - Missing environment variables detected
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const dotenv = require('dotenv');

// Configuration
const ENV_SAMPLE_PATH = path.join(process.cwd(), '.env.sample');
const IGNORE_DIRS = ['node_modules', 'dist', '.git', '.turbo', '.next', '.nuxt', 'coverage'];
const IGNORE_VARS = [
  'NODE_ENV', // Common runtime flag, often checked but not in .env
  'DEBUG',    // Debug flag
  'CI',       // CI environment flag
  'TEST',     // Test environment flag
  'GITHUB_'   // GitHub Actions environment variables
];

// ANSI color codes for output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bold: '\x1b[1m'
};

// Load .env.sample file
let envSampleContent;
try {
  envSampleContent = fs.readFileSync(ENV_SAMPLE_PATH, 'utf8');
  console.log(`${colors.green}✓${colors.reset} Loaded ${colors.bold}.env.sample${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}✗${colors.reset} Error loading .env.sample: ${error.message}`);
  process.exit(1);
}

// Parse .env.sample to get all documented variables
const parsedEnvSample = dotenv.parse(envSampleContent);
const documentedVars = new Set(Object.keys(parsedEnvSample));

// Add commented variables (they're still documented)
const commentedVarMatches = envSampleContent.matchAll(/^\s*#\s*([A-Z0-9_]+)=/gm);
for (const match of commentedVarMatches) {
  documentedVars.add(match[1]);
}

console.log(`${colors.green}✓${colors.reset} Found ${colors.bold}${documentedVars.size}${colors.reset} documented variables in .env.sample`);

// Regular expressions for finding environment variables
const patterns = {
  javascript: /process\.env\.([A-Z0-9_]+)/g,
  vite: /import\.meta\.env\.([A-Z0-9_]+)/g,
  python: /os\.(?:getenv|environ(?:\[['"](.*?)['"]\]|\[[^"']+\]))/g,
  terraform: /var\.([a-zA-Z0-9_]+)/g,
  githubActions: /\$\{\{\s*(?:secrets|env)\.([A-Z0-9_]+)\s*\}\}/g,
  dockerCompose: /\$\{?([A-Z0-9_]+)\}?/g
};

// Function to recursively scan directories
function scanDirectory(dir, foundVars = new Map()) {
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    
    // Skip ignored directories
    if (entry.isDirectory() && !IGNORE_DIRS.includes(entry.name)) {
      scanDirectory(fullPath, foundVars);
      continue;
    }
    
    // Skip directories and non-text files
    if (!entry.isFile()) continue;
    
    // Skip files that are likely not relevant
    const ext = path.extname(entry.name).toLowerCase();
    if (!['.js', '.jsx', '.ts', '.tsx', '.py', '.tf', '.yml', '.yaml', '.json', '.env'].includes(ext)) {
      continue;
    }
    
    try {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // Apply appropriate patterns based on file type
      if (['.js', '.jsx', '.ts', '.tsx'].includes(ext)) {
        findMatches(content, patterns.javascript, fullPath, foundVars);
        findMatches(content, patterns.vite, fullPath, foundVars);
      } else if (ext === '.py') {
        findPythonEnvVars(content, fullPath, foundVars);
      } else if (ext === '.tf') {
        findMatches(content, patterns.terraform, fullPath, foundVars);
      } else if (['.yml', '.yaml'].includes(ext)) {
        findMatches(content, patterns.githubActions, fullPath, foundVars);
        findMatches(content, patterns.dockerCompose, fullPath, foundVars);
      }
    } catch (error) {
      console.error(`${colors.yellow}⚠${colors.reset} Error reading ${fullPath}: ${error.message}`);
    }
  }
  
  return foundVars;
}

// Find matches using a regex pattern
function findMatches(content, pattern, filePath, foundVars) {
  const matches = content.matchAll(pattern);
  for (const match of matches) {
    const varName = match[1];
    if (varName && !IGNORE_VARS.some(prefix => varName.startsWith(prefix))) {
      if (!foundVars.has(varName)) {
        foundVars.set(varName, []);
      }
      foundVars.get(varName).push(filePath);
    }
  }
}

// Special handling for Python environment variables
function findPythonEnvVars(content, filePath, foundVars) {
  // Handle os.getenv("VAR_NAME", default)
  const getenvMatches = content.matchAll(/os\.getenv\(["']([A-Z0-9_]+)["']/g);
  for (const match of getenvMatches) {
    const varName = match[1];
    if (varName && !IGNORE_VARS.some(prefix => varName.startsWith(prefix))) {
      if (!foundVars.has(varName)) {
        foundVars.set(varName, []);
      }
      foundVars.get(varName).push(filePath);
    }
  }
  
  // Handle os.environ["VAR_NAME"] or os.environ.get("VAR_NAME")
  const environMatches = content.matchAll(/os\.environ(?:\[["']([A-Z0-9_]+)["']\]|\.get\(["']([A-Z0-9_]+)["']\))/g);
  for (const match of environMatches) {
    const varName = match[1] || match[2];
    if (varName && !IGNORE_VARS.some(prefix => varName.startsWith(prefix))) {
      if (!foundVars.has(varName)) {
        foundVars.set(varName, []);
      }
      foundVars.get(varName).push(filePath);
    }
  }
}

// Main execution
console.log(`${colors.blue}ℹ${colors.reset} Scanning repository for environment variable references...`);
const foundVars = scanDirectory(process.cwd());
console.log(`${colors.green}✓${colors.reset} Found ${colors.bold}${foundVars.size}${colors.reset} environment variables referenced in code`);

// Find undocumented variables
const undocumentedVars = new Map();
for (const [varName, files] of foundVars.entries()) {
  if (!documentedVars.has(varName)) {
    undocumentedVars.set(varName, files);
  }
}

// Report results
if (undocumentedVars.size > 0) {
  console.error(`\n${colors.red}✗${colors.reset} ${colors.bold}${undocumentedVars.size} undocumented environment variables found:${colors.reset}\n`);
  
  for (const [varName, files] of undocumentedVars.entries()) {
    console.error(`  ${colors.red}${varName}${colors.reset}`);
    console.error(`    Referenced in:`);
    // Show up to 3 files for brevity
    const displayFiles = files.slice(0, 3);
    for (const file of displayFiles) {
      const relativePath = path.relative(process.cwd(), file);
      console.error(`    - ${relativePath}`);
    }
    if (files.length > 3) {
      console.error(`    - ... and ${files.length - 3} more files`);
    }
  }
  
  console.error(`\n${colors.yellow}⚠${colors.reset} Please add these variables to ${colors.bold}.env.sample${colors.reset} with appropriate documentation.`);
  process.exit(1);
} else {
  console.log(`\n${colors.green}✓${colors.reset} ${colors.bold}All environment variables are properly documented in .env.sample!${colors.reset}`);
  process.exit(0);
}
