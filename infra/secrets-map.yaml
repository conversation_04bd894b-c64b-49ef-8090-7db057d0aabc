# AiClearBill Secrets Mapping
# Maps GitHub Actions secrets and GCP Secret Manager secrets to environment variables
# Used for documentation and infrastructure automation

# GitHub Actions secrets used in CI/CD
github_actions:
  secrets:
    # Authentication & Access
    GCP_SA_KEY:
      description: "Google Cloud service account key (JSON) used for CI/CD authentication"
      used_in: ["infrastructure", "build-api", "build-ocr-processor", "build-web"]
    
    GCP_PROJECT_ID:
      description: "Google Cloud project ID"
      maps_to: 
        - env: "PROJECT_ID"
          context: "GitHub Actions environment variable"
    
    DEPLOY_TOKEN:
      description: "Deployment authentication token"
      used_in: ["deploy"]
    
    # Firebase configuration
    FIREBASE_SERVICE_ACCOUNT:
      description: "Firebase service account for deployment"
      used_in: ["build-web"]
    
    FIREBASE_API_KEY:
      description: "Firebase JS SDK API key"
      maps_to:
        - env: "VITE_FIREBASE_API_KEY"
          context: "Web app build"
    
    FIREBASE_AUTH_DOMAIN:
      description: "Firebase authentication domain"
      maps_to:
        - env: "VITE_FIREBASE_AUTH_DOMAIN"
          context: "Web app build"
    
    FIREBASE_PROJECT_ID:
      description: "Firebase project ID"
      maps_to:
        - env: "VITE_FIREBASE_PROJECT_ID"
          context: "Web app build"
    
    FIREBASE_STORAGE_BUCKET:
      description: "Firebase storage bucket"
      maps_to:
        - env: "VITE_FIREBASE_STORAGE_BUCKET"
          context: "Web app build"
    
    FIREBASE_MESSAGING_SENDER_ID:
      description: "Firebase messaging sender ID"
      maps_to:
        - env: "VITE_FIREBASE_MESSAGING_SENDER_ID"
          context: "Web app build"
    
    FIREBASE_APP_ID:
      description: "Firebase application ID"
      maps_to:
        - env: "VITE_FIREBASE_APP_ID"
          context: "Web app build"
    
    # Testing & Analytics
    CODECOV_TOKEN:
      description: "Codecov.io token for coverage reporting"
      used_in: ["test"]

# Google Cloud Secret Manager secrets
gcp_secret_manager:
  # API Service secrets
  api_secrets:
    firebase-service-account:
      description: "Firebase Admin SDK service account (JSON)"
      maps_to:
        - env: "FIREBASE_SERVICE_ACCOUNT_PATH"
          context: "API service"
          note: "In Cloud Run, the secret is mounted as a file"
    
    qbo-client-id:
      description: "QuickBooks Online OAuth client ID"
      maps_to:
        - env: "QBO_CLIENT_ID"
          context: "API service"
    
    qbo-client-secret:
      description: "QuickBooks Online OAuth client secret"
      maps_to:
        - env: "QBO_CLIENT_SECRET"
          context: "API service"
    
    xero-client-id:
      description: "Xero OAuth client ID"
      maps_to:
        - env: "XERO_CLIENT_ID"
          context: "API service"
    
    xero-client-secret:
      description: "Xero OAuth client secret"
      maps_to:
        - env: "XERO_CLIENT_SECRET"
          context: "API service"
  
  # Python Backend secrets
  python_backend_secrets:
    gemini-api-key:
      description: "Google Gemini API key"
      maps_to:
        - env: "GEMINI_API_KEY"
          context: "Python backend"
    
    mongo-connection-string:
      description: "MongoDB Atlas connection string (with credentials)"
      maps_to:
        - env: "MONGO_URI"
          context: "Python backend"
  
  # Web Frontend secrets
  web_secrets:
    firebase-config:
      description: "Firebase JS SDK configuration (JSON)"
      maps_to:
        - env: "VITE_FIREBASE_API_KEY"
          context: "Web frontend"
          json_field: "apiKey"
        - env: "VITE_FIREBASE_AUTH_DOMAIN"
          context: "Web frontend"
          json_field: "authDomain"
        - env: "VITE_FIREBASE_PROJECT_ID"
          context: "Web frontend"
          json_field: "projectId"
        - env: "VITE_FIREBASE_STORAGE_BUCKET"
          context: "Web frontend"
          json_field: "storageBucket"
        - env: "VITE_FIREBASE_MESSAGING_SENDER_ID"
          context: "Web frontend"
          json_field: "messagingSenderId"
        - env: "VITE_FIREBASE_APP_ID"
          context: "Web frontend"
          json_field: "appId"
        - env: "VITE_FIREBASE_MEASUREMENT_ID"
          context: "Web frontend"
          json_field: "measurementId"

# Secret rotation policy
rotation_policy:
  firebase_service_account: "90 days"
  qbo_oauth_credentials: "180 days"
  xero_oauth_credentials: "180 days"
  gemini_api_key: "90 days"
  mongodb_credentials: "180 days"
