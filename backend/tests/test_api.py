import os
import sys
from unittest.mock import AsyncMock, patch

import pytest
from fastapi.testclient import TestClient

# Add parent directory to path to make imports work
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Now import the modules
from api.v1.auth import get_current_user
from main import app
import models.db as db_module


# Create test client
client = TestClient(app)

# Mock authenticated user for testing
mock_user = {
    "_id": "test_user_id",
    "email": "<EMAIL>",
    "tenantId": "test_tenant_id",
    "roles": ["user"],
}

# Create a mock database
class MockCursor:
    def __init__(self, items=None):
        self.items = items or []
        self.sort_args = None
        self.skip_value = 0
        self.limit_value = 0

    def sort(self, *args, **kwargs):
        self.sort_args = args
        return self

    def skip(self, skip_value):
        self.skip_value = skip_value
        return self

    def limit(self, limit_value):
        self.limit_value = limit_value
        return self

    async def to_list(self, length=None):
        return self.items

class MockCollection:
    def __init__(self, items=None):
        self.items = items or []

    async def find(self, *args, **kwargs):
        return MockCursor(self.items)

    async def find_one(self, *args, **kwargs):
        if not self.items:
            return None
        return self.items[0]

    async def create_index(self, *args, **kwargs):
        return None

    async def command(self, *args, **kwargs):
        return {"ok": 1}

    async def insert_one(self, document):
        class Result:
            @property
            def inserted_id(self):
                return "mock_id"
        return Result()

    async def find_one_and_update(self, filter_doc, update_doc, return_document=None):
        if not self.items:
            return None
        return self.items[0]

    async def delete_one(self, filter_doc):
        class Result:
            @property
            def deleted_count(self):
                return 1
        return Result()

class MockDatabase:
    def __init__(self):
        # Initialize with mock data
        self.invoices = MockCollection([
            {
                "_id": "invoice1",
                "tenantId": "test_tenant_id",
                "fileUrl": "http://example.com/invoice1.pdf",
                "status": "READY",
                "fields": {
                    "vendorId": "vendor1",
                    "date": "2023-01-15",
                    "number": "INV-12345",
                    "validated": True,
                },
                "createdAt": "2023-01-15T12:00:00",
                "updatedAt": "2023-01-15T12:05:00",
            }
        ])

        self.vendors = MockCollection([
            {
                "_id": "vendor1",
                "tenantId": "test_tenant_id",
                "name": "ACME Corp",
                "defaultAccount": "Expenses",
                "defaultTaxCode": "STANDARD",
                "createdAt": "2023-01-01T12:00:00",
                "updatedAt": "2023-01-01T12:00:00",
            }
        ])

        self.users = MockCollection()
        self.tenants = MockCollection()

    async def command(self, *args, **kwargs):
        return {"ok": 1}

# Override the dependency
@pytest.fixture(autouse=True)
def override_dependency():
    """Override the authentication dependency"""
    # Override auth dependency
    app.dependency_overrides[get_current_user] = lambda: mock_user

    yield

    # Clean up
    app.dependency_overrides = {}


def test_health_check():
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}


@pytest.mark.asyncio
async def test_list_invoices():
    """Test listing invoices"""

    # Mock the invoice list
    mock_invoices = [
        {
            "_id": "invoice1",
            "tenantId": "test_tenant_id",
            "fileUrl": "http://example.com/invoice1.pdf",
            "status": "READY",
            "fields": {
                "vendorId": "vendor1",
                "date": "2023-01-15",
                "number": "INV-12345",
                "validated": True,
            },
            "createdAt": "2023-01-15T12:00:00",
            "updatedAt": "2023-01-15T12:05:00",
        }
    ]

    # Mock the list_invoices function
    with patch(
        "models.invoice.list_invoices", new=AsyncMock(return_value=mock_invoices)
    ):
        with patch(
            "models.invoice.invoice_to_json",
            side_effect=lambda x: {
                "id": x["_id"],
                "tenantId": x["tenantId"],
                "fileUrl": x["fileUrl"],
                "status": x["status"],
                "fields": x["fields"],
                "createdAt": x["createdAt"],
                "updatedAt": x["updatedAt"],
            },
        ):
            # Make the request
            response = client.get("/api/v1/test_tenant_id/invoices/")

            # Check the response
            assert response.status_code == 200
            results = response.json()
            assert len(results) == 1
            assert results[0]["id"] == "invoice1"
            assert results[0]["status"] == "READY"


@pytest.mark.asyncio
async def test_get_invoice():
    """Test getting a specific invoice"""

    # Mock the invoice
    mock_invoice = {
        "_id": "invoice1",
        "tenantId": "test_tenant_id",
        "fileUrl": "http://example.com/invoice1.pdf",
        "status": "READY",
        "fields": {
            "vendorId": "vendor1",
            "date": "2023-01-15",
            "number": "INV-12345",
            "validated": True,
        },
        "createdAt": "2023-01-15T12:00:00",
        "updatedAt": "2023-01-15T12:05:00",
    }

    # Mock the get_invoice function
    with patch("models.invoice.get_invoice", new=AsyncMock(return_value=mock_invoice)):
        with patch(
            "models.invoice.invoice_to_json",
            side_effect=lambda x: {
                "id": x["_id"],
                "tenantId": x["tenantId"],
                "fileUrl": x["fileUrl"],
                "status": x["status"],
                "fields": x["fields"],
                "createdAt": x["createdAt"],
                "updatedAt": x["updatedAt"],
            },
        ):
            # Make the request
            response = client.get("/api/v1/test_tenant_id/invoices/invoice1")

            # Check the response
            assert response.status_code == 200
            result = response.json()
            assert result["id"] == "invoice1"
            assert result["status"] == "READY"
            assert result["fields"]["number"] == "INV-12345"


@pytest.mark.asyncio
async def test_list_vendors():
    """Test listing vendors"""

    # Mock the vendor list
    mock_vendors = [
        {
            "_id": "vendor1",
            "tenantId": "test_tenant_id",
            "name": "ACME Corp",
            "defaultAccount": "Expenses",
            "defaultTaxCode": "STANDARD",
            "createdAt": "2023-01-01T12:00:00",
            "updatedAt": "2023-01-01T12:00:00",
        }
    ]

    # Mock the list_vendors function
    with patch("models.vendor.list_vendors", new=AsyncMock(return_value=mock_vendors)):
        with patch(
            "models.vendor.vendor_to_json",
            side_effect=lambda x: {
                "id": x["_id"],
                "tenantId": x["tenantId"],
                "name": x["name"],
                "defaultAccount": x["defaultAccount"],
                "defaultTaxCode": x["defaultTaxCode"],
                "createdAt": x["createdAt"],
                "updatedAt": x["updatedAt"],
            },
        ):
            # Make the request
            response = client.get("/api/v1/test_tenant_id/vendors/")

            # Check the response
            assert response.status_code == 200
            results = response.json()
            assert len(results) == 1
            assert results[0]["id"] == "vendor1"
            assert results[0]["name"] == "ACME Corp"
