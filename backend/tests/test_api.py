import os
import sys
from unittest.mock import AsyncMock, patch, MagicMock

import pytest
from fastapi.testclient import TestClient

# Add parent directory to path to make imports work
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Now import the modules
from backend.api.v1.auth import get_current_user
from main import app


# Create test client
client = TestClient(app)

# Mock authenticated user for testing
mock_user = {
    "_id": "test_user_id",
    "email": "<EMAIL>",
    "tenantId": "test_tenant_id",
    "roles": ["user"],
}



# Override the dependency
@pytest.fixture(autouse=True)
def override_dependency():
    """Override the authentication dependency"""
    # Override auth dependency
    app.dependency_overrides[get_current_user] = lambda: mock_user

    yield

    # Clean up
    app.dependency_overrides = {}


def test_health_check():
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}


@pytest.mark.asyncio
async def test_list_invoices():
    """Test listing invoices"""

    # Mock the invoice list
    mock_invoices = [
        {
            "_id": "invoice1",
            "tenantId": "test_tenant_id",
            "fileUrl": "http://example.com/invoice1.pdf",
            "status": "READY",
            "fields": {
                "vendorId": "vendor1",
                "date": "2023-01-15",
                "number": "INV-12345",
                "validated": True,
            },
            "createdAt": "2023-01-15T12:00:00",
            "updatedAt": "2023-01-15T12:05:00",
        }
    ]

    # Mock the database module
    with patch("api.v1.invoices.list_invoices", new=AsyncMock(return_value=mock_invoices)):
        with patch(
            "api.v1.invoices.invoice_to_json",
            side_effect=lambda x: {
                "id": x["_id"],
                "tenantId": x["tenantId"],
                "fileUrl": x["fileUrl"],
                "status": x["status"],
                "fields": x["fields"],
                "createdAt": x["createdAt"],
                "updatedAt": x["updatedAt"],
            },
        ):
            # Make the request
            response = client.get("/api/v1/test_tenant_id/invoices/")

            # Check the response
            assert response.status_code == 200
            results = response.json()
            assert len(results) == 1
            assert results[0]["id"] == "invoice1"
            assert results[0]["status"] == "READY"


@pytest.mark.asyncio
async def test_get_invoice():
    """Test getting a specific invoice"""

    # Mock the invoice
    mock_invoice = {
        "_id": "invoice1",
        "tenantId": "test_tenant_id",
        "fileUrl": "http://example.com/invoice1.pdf",
        "status": "READY",
        "fields": {
            "vendorId": "vendor1",
            "date": "2023-01-15",
            "number": "INV-12345",
            "validated": True,
        },
        "createdAt": "2023-01-15T12:00:00",
        "updatedAt": "2023-01-15T12:05:00",
    }

    # Mock the get_invoice function
    with patch("api.v1.invoices.get_invoice", new=AsyncMock(return_value=mock_invoice)):
        with patch(
            "api.v1.invoices.invoice_to_json",
            side_effect=lambda x: {
                "id": x["_id"],
                "tenantId": x["tenantId"],
                "fileUrl": x["fileUrl"],
                "status": x["status"],
                "fields": x["fields"],
                "createdAt": x["createdAt"],
                "updatedAt": x["updatedAt"],
            },
        ):
            # Make the request
            response = client.get("/api/v1/test_tenant_id/invoices/invoice1")

            # Check the response
            assert response.status_code == 200
            result = response.json()
            assert result["id"] == "invoice1"
            assert result["status"] == "READY"
            assert result["fields"]["number"] == "INV-12345"


@pytest.mark.asyncio
async def test_list_vendors():
    """Test listing vendors"""

    # Mock the vendor list
    mock_vendors = [
        {
            "_id": "vendor1",
            "tenantId": "test_tenant_id",
            "name": "ACME Corp",
            "defaultAccount": "Expenses",
            "defaultTaxCode": "STANDARD",
            "createdAt": "2023-01-01T12:00:00",
            "updatedAt": "2023-01-01T12:00:00",
        }
    ]

    # Mock the list_vendors function
    with patch("api.v1.vendors.list_vendors", new=AsyncMock(return_value=mock_vendors)):
        with patch(
            "api.v1.vendors.vendor_to_json",
            side_effect=lambda x: {
                "id": x["_id"],
                "tenantId": x["tenantId"],
                "name": x["name"],
                "defaultAccount": x["defaultAccount"],
                "defaultTaxCode": x["defaultTaxCode"],
                "createdAt": x["createdAt"],
                "updatedAt": x["updatedAt"],
            },
        ):
            # Make the request
            response = client.get("/api/v1/test_tenant_id/vendors/")

            # Check the response
            assert response.status_code == 200
            results = response.json()
            assert len(results) == 1
            assert results[0]["id"] == "vendor1"
            assert results[0]["name"] == "ACME Corp"
