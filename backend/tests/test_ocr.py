import os
import sys
from unittest.mock import MagicMock, patch

import pytest

from services.ocr import process_image_with_gemini, process_image_with_tesseract

# Add backend directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


# Sample image content
SAMPLE_IMAGE = b"sample_image_content"


@pytest.mark.asyncio
async def test_process_image_with_gemini_success():
    """Test successful OCR processing with Gemini"""

    # Mock the Gemini API response
    mock_response = MagicMock()
    mock_response.text = """
    {
        "vendor_name": "ACME Corp",
        "invoice_date": "2023-01-15",
        "invoice_number": "INV-12345",
        "memo": "Office supplies",
        "vat_info": [
            {"amount": 120.00, "tax_code": "STANDARD", "tax_rate": 20.0}
        ]
    }
    """

    mock_model = MagicMock()
    mock_model.generate_content.return_value = mock_response

    with patch("google.generativeai.GenerativeModel", return_value=mock_model):
        with patch("google.generativeai.configure") as mock_configure:
            # Run the function
            result = await process_image_with_gemini(SAMPLE_IMAGE)

            # Check that the result has the expected structure
            assert "extracted" in result
            assert "raw_text" in result
            assert result["source"] == "gemini"

            # Check extracted data
            extracted = result["extracted"]
            assert extracted["vendor_name"] == "ACME Corp"
            assert extracted["invoice_date"] == "2023-01-15"
            assert extracted["invoice_number"] == "INV-12345"
            assert extracted["memo"] == "Office supplies"
            assert len(extracted["vat_info"]) == 1
            assert extracted["vat_info"][0]["amount"] == 120.00


@pytest.mark.asyncio
async def test_process_image_with_gemini_failure_fallback():
    """Test fallback to Tesseract when Gemini fails"""

    # Force Gemini to fail
    with patch(
        "google.generativeai.GenerativeModel", side_effect=Exception("API error")
    ):
        with patch("google.generativeai.configure"):
            # Mock Tesseract success
            with patch("services.ocr.process_image_with_tesseract") as mock_tesseract:
                # Setup Tesseract mock return value
                mock_tesseract.return_value = {
                    "extracted": {
                        "vendor_name": "ACME Corp",
                        "invoice_date": "2023-01-15",
                        "invoice_number": "INV-12345",
                        "memo": "Office supplies",
                        "vat_info": [],
                    },
                    "raw_text": "Sample OCR text",
                    "source": "tesseract",
                }

                # Run the function
                result = await process_image_with_gemini(SAMPLE_IMAGE)

                # Check that Tesseract was called
                mock_tesseract.assert_called_once_with(SAMPLE_IMAGE)

                # Check that the result came from Tesseract
                assert result["source"] == "tesseract"


@pytest.mark.asyncio
async def test_process_image_with_tesseract():
    """Test OCR processing with Tesseract"""

    # Mock PIL Image
    mock_image = MagicMock()

    # Mock pytesseract
    mock_text = "ACME Corp\nInvoice #INV-12345\nDate: 15/01/2023\nDescription: Office supplies\nTotal: $120.00\nVAT (20%): $20.00"

    with patch("PIL.Image.open", return_value=mock_image):
        with patch("pytesseract.image_to_string", return_value=mock_text):
            with patch("services.ocr.extract_info_from_text") as mock_extract:
                # Setup mock extraction
                mock_extract.return_value = (
                    "ACME Corp",  # vendor_name
                    "2023-01-15",  # invoice_date
                    "INV-12345",  # invoice_number
                    "Office supplies",  # memo
                    [
                        {"amount": 20.0, "tax_code": "VAT20", "tax_rate": 20.0}
                    ],  # vat_info
                )

                # Run the function
                result = await process_image_with_tesseract(SAMPLE_IMAGE)

                # Check extraction was called
                mock_extract.assert_called_once()

                # Check result
                assert result["source"] == "tesseract"
                assert result["extracted"]["vendor_name"] == "ACME Corp"
                assert result["extracted"]["invoice_date"] == "2023-01-15"
                assert result["extracted"]["invoice_number"] == "INV-12345"
                assert len(result["extracted"]["vat_info"]) == 1
