import io
import logging
import os
import tempfile
import uuid

import aiofiles
import pdf2image
from fastapi import UploadFile

# Configure logging
logger = logging.getLogger(__name__)

# File upload configuration
UPLOAD_DIR = os.environ.get("UPLOAD_DIR", "uploads")


async def ensure_upload_dir():
    """Ensure upload directory exists"""
    os.makedirs(UPLOAD_DIR, exist_ok=True)


async def save_uploaded_file(file: UploadFile, tenant_id: str) -> str:
    """
    Save an uploaded file to disk
    """
    await ensure_upload_dir()

    # Create tenant directory
    tenant_dir = os.path.join(UPLOAD_DIR, tenant_id)
    os.makedirs(tenant_dir, exist_ok=True)

    # Generate unique filename
    file_extension = os.path.splitext(file.filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(tenant_dir, unique_filename)

    # Save file
    async with aiofiles.open(file_path, "wb") as out_file:
        content = await file.read()
        await out_file.write(content)

    return file_path


async def read_file(file_path: str) -> bytes:
    """
    Read file content
    """
    async with aiofiles.open(file_path, "rb") as in_file:
        return await in_file.read()


async def convert_pdf_to_image(pdf_content: bytes, page_number: int = 0) -> bytes:
    """
    Convert PDF to image
    """
    try:
        # Save PDF content to temporary file
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
            tmp_file.write(pdf_content)
            tmp_path = tmp_file.name

        try:
            # Convert PDF to images
            images = pdf2image.convert_from_path(
                tmp_path, first_page=page_number + 1, last_page=page_number + 1
            )

            if not images:
                raise ValueError("Failed to convert PDF to image")

            # Convert PIL Image to bytes
            img_byte_arr = io.BytesIO()
            images[0].save(img_byte_arr, format="JPEG", quality=85)
            return img_byte_arr.getvalue()

        finally:
            # Clean up temporary file
            os.unlink(tmp_path)

    except Exception as e:
        logger.error(f"Error converting PDF to image: {str(e)}")
        raise ValueError(f"Failed to convert PDF to image: {str(e)}")


async def delete_file(file_path: str) -> bool:
    """
    Delete file from disk
    """
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
        return False
    except Exception as e:
        logger.error(f"Error deleting file: {str(e)}")
        return False
