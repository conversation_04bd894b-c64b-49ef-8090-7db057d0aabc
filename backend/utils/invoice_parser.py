import logging
from datetime import datetime
from typing import Any, Dict

from backend.models.vendor import get_vendor_by_name

# Configure logging
logger = logging.getLogger(__name__)


async def normalize_invoice_data(
    tenant_id: str, extracted_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Normalize and enrich extracted invoice data
    """
    try:
        # Initialize normalized data
        normalized = {}

        # Extract and normalize vendor
        vendor_name = extracted_data.get("vendor_name")
        if vendor_name:
            # Try to find vendor by name
            vendor = await get_vendor_by_name(tenant_id, vendor_name)

            if vendor:
                # Use existing vendor
                normalized["vendorId"] = str(vendor["_id"])
            else:
                # Mark as new vendor
                normalized["newVendorName"] = vendor_name

        # Normalize invoice date
        invoice_date = extracted_data.get("invoice_date")
        if invoice_date:
            try:
                # Parse and validate date
                date_obj = datetime.fromisoformat(invoice_date)
                normalized["date"] = date_obj.strftime("%Y-%m-%d")
            except (ValueError, TypeError):
                # Invalid date, ignore
                pass

        # Normalize invoice number
        if extracted_data.get("invoice_number"):
            normalized["number"] = str(extracted_data["invoice_number"])

        # Normalize memo
        if extracted_data.get("memo"):
            normalized["memo"] = str(extracted_data["memo"])

        # Normalize VAT lines
        if extracted_data.get("vat_info") and isinstance(
            extracted_data["vat_info"], list
        ):
            normalized["vatLines"] = []
            for vat_item in extracted_data["vat_info"]:
                if isinstance(vat_item, dict) and "amount" in vat_item:
                    vat_line = {
                        "amount": float(vat_item["amount"]),
                        "taxCode": vat_item.get("tax_code", "STANDARD"),
                    }

                    if "tax_rate" in vat_item:
                        vat_line["taxRate"] = float(vat_item["tax_rate"])

                    normalized["vatLines"].append(vat_line)

        # Set validation status
        normalized["validated"] = False

        return normalized

    except Exception as e:
        logger.error(f"Error normalizing invoice data: {str(e)}")
        # Return minimal valid structure in case of error
        return {"validated": False}
