# FastAPI
DEBUG=true
LOG_LEVEL=debug

# MongoDB
MONGO_URI=mongodb://localhost:27017
MONGO_DB_NAME=aiclearbill

# Redis (for Celery)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Firebase
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
# Optional: Firebase credentials as JSON string or path to JSON file
FIREBASE_CREDENTIALS=

# QuickBooks Online
QBO_CLIENT_ID=your-client-id
QBO_CLIENT_SECRET=your-client-secret
QBO_REDIRECT_URI=https://your-domain.com/api/v1/qbo/callback
QBO_ENVIRONMENT=sandbox  # or production

# Google Gemini API
GEMINI_API_KEY=your-gemini-api-key

# File Upload
UPLOAD_DIR=uploads
