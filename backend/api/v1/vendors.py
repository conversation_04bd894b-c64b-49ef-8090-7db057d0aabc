import logging
from datetime import datetime
from typing import List, Optional

from api.v1.auth import get_current_user
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel

from models.vendor import (
    create_vendor,
    delete_vendor,
    get_vendor,
    list_vendors,
    update_vendor,
    vendor_to_json,
)

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter()


# Models
class VendorCreate(BaseModel):
    name: str
    defaultAccount: Optional[str] = None
    defaultTaxCode: Optional[str] = None


class VendorUpdate(BaseModel):
    name: Optional[str] = None
    defaultAccount: Optional[str] = None
    defaultTaxCode: Optional[str] = None


class VendorResponse(BaseModel):
    id: str
    tenantId: str
    name: str
    defaultAccount: Optional[str] = None
    defaultTaxCode: Optional[str] = None
    createdAt: datetime
    updatedAt: datetime


@router.post("/", response_model=VendorResponse)
async def create_new_vendor(
    tenant_id: str,
    vendor: VendorCreate,
    user=Depends(get_current_user),
):
    """
    Create a new vendor
    """
    try:
        vendor_id = await create_vendor(
            tenant_id=tenant_id,
            name=vendor.name,
            default_account=vendor.defaultAccount,
            default_tax_code=vendor.defaultTaxCode,
            created_by=user["_id"],
        )

        new_vendor = await get_vendor(vendor_id)
        return vendor_to_json(new_vendor)

    except Exception as e:
        logger.error(f"Error creating vendor: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to create vendor: {str(e)}"
        )


@router.get("/", response_model=List[VendorResponse])
async def get_vendors(
    tenant_id: str,
    name: Optional[str] = Query(None, description="Filter by vendor name"),
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    user=Depends(get_current_user),
):
    """
    List vendors with optional filters
    """
    try:
        filters = {}
        if name:
            filters["name"] = {
                "$regex": name,
                "$options": "i",
            }  # Case-insensitive search

        vendors = await list_vendors(
            tenant_id=tenant_id,
            filters=filters,
            limit=limit,
            offset=offset,
        )

        return [vendor_to_json(vendor) for vendor in vendors]

    except Exception as e:
        logger.error(f"Error listing vendors: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list vendors: {str(e)}")


@router.get("/{vendor_id}", response_model=VendorResponse)
async def get_vendor_by_id(
    tenant_id: str,
    vendor_id: str,
    user=Depends(get_current_user),
):
    """
    Get a specific vendor by ID
    """
    try:
        vendor = await get_vendor(vendor_id)

        if not vendor or vendor.get("tenantId") != tenant_id:
            raise HTTPException(status_code=404, detail="Vendor not found")

        return vendor_to_json(vendor)

    except Exception as e:
        logger.error(f"Error getting vendor: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get vendor: {str(e)}")


@router.patch("/{vendor_id}", response_model=VendorResponse)
async def update_vendor_by_id(
    tenant_id: str,
    vendor_id: str,
    vendor_data: VendorUpdate,
    user=Depends(get_current_user),
):
    """
    Update vendor data
    """
    try:
        # Get existing vendor
        existing_vendor = await get_vendor(vendor_id)

        if not existing_vendor or existing_vendor.get("tenantId") != tenant_id:
            raise HTTPException(status_code=404, detail="Vendor not found")

        # Update vendor
        update_data = vendor_data.dict(exclude_unset=True)
        updated_vendor = await update_vendor(
            vendor_id=vendor_id, update_data=update_data, updated_by=user["_id"]
        )

        return vendor_to_json(updated_vendor)

    except Exception as e:
        logger.error(f"Error updating vendor: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to update vendor: {str(e)}"
        )


@router.delete("/{vendor_id}")
async def delete_vendor_by_id(
    tenant_id: str,
    vendor_id: str,
    user=Depends(get_current_user),
):
    """
    Delete a vendor
    """
    try:
        # Check if vendor exists
        existing_vendor = await get_vendor(vendor_id)

        if not existing_vendor or existing_vendor.get("tenantId") != tenant_id:
            raise HTTPException(status_code=404, detail="Vendor not found")

        # Delete vendor
        await delete_vendor(vendor_id)

        return {"detail": "Vendor deleted successfully"}

    except Exception as e:
        logger.error(f"Error deleting vendor: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to delete vendor: {str(e)}"
        )
