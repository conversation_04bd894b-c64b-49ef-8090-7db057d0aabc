import logging
from datetime import datetime
from typing import List, Optional

from api.v1.auth import get_current_user
from fastapi import APIRouter, Depends, File, HTTPException, Query, UploadFile
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from utils.file_utils import save_uploaded_file
from workers.tasks import process_invoice_task

from models.invoice import (
    create_invoice,
    delete_invoice,
    get_invoice,
    invoice_to_json,
    list_invoices,
    update_invoice,
)

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter()


# Models
class VATLine(BaseModel):
    amount: float
    taxCode: str
    taxRate: Optional[float] = None


class InvoiceFields(BaseModel):
    vendorId: Optional[str] = None
    date: Optional[datetime] = None
    number: Optional[str] = None
    memo: Optional[str] = None
    vatLines: Optional[List[VATLine]] = None
    validated: bool = False


class InvoiceResponse(BaseModel):
    id: str
    tenantId: str
    fileUrl: str
    sha256: Optional[str] = None
    fields: Optional[InvoiceFields] = None
    status: str
    qboTxnId: Optional[str] = None
    createdAt: datetime
    updatedAt: datetime


class InvoiceUpdateModel(BaseModel):
    fields: Optional[InvoiceFields] = None
    status: Optional[str] = None
    qboTxnId: Optional[str] = None


@router.post("/", response_model=InvoiceResponse)
async def upload_invoice(
    tenant_id: str,
    file: UploadFile = File(...),
    user=Depends(get_current_user),
):
    """
    Upload a new invoice file and start OCR processing
    """
    try:
        # Save file and get the file path
        file_path = await save_uploaded_file(file, tenant_id)

        # Create initial invoice record
        invoice_id = await create_invoice(
            tenant_id=tenant_id,
            file_url=file_path,
            status="PENDING",
            created_by=user["_id"],
        )

        # Start async processing job
        process_invoice_task.delay(str(invoice_id), tenant_id, file_path)

        # Get the created invoice
        invoice = await get_invoice(invoice_id)

        return invoice_to_json(invoice)

    except Exception as e:
        logger.error(f"Error uploading invoice: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to upload invoice: {str(e)}"
        )


@router.get("/", response_model=List[InvoiceResponse])
async def get_invoices(
    tenant_id: str,
    status: Optional[str] = Query(None, description="Filter by status"),
    vendor_id: Optional[str] = Query(None, description="Filter by vendor ID"),
    limit: int = Query(20, ge=1, le=100),
    offset: int = Query(0, ge=0),
    user=Depends(get_current_user),
):
    """
    List invoices with optional filters
    """
    try:
        filters = {}
        if status:
            filters["status"] = status
        if vendor_id:
            filters["fields.vendorId"] = vendor_id

        invoices = await list_invoices(
            tenant_id=tenant_id,
            filters=filters,
            limit=limit,
            offset=offset,
        )

        return [invoice_to_json(invoice) for invoice in invoices]

    except Exception as e:
        logger.error(f"Error listing invoices: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to list invoices: {str(e)}"
        )


@router.get("/{invoice_id}", response_model=InvoiceResponse)
async def get_invoice_by_id(
    tenant_id: str,
    invoice_id: str,
    user=Depends(get_current_user),
):
    """
    Get a specific invoice by ID
    """
    try:
        invoice = await get_invoice(invoice_id)

        if not invoice or invoice.get("tenantId") != tenant_id:
            raise HTTPException(status_code=404, detail="Invoice not found")

        return invoice_to_json(invoice)

    except Exception as e:
        logger.error(f"Error getting invoice: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get invoice: {str(e)}")


@router.patch("/{invoice_id}", response_model=InvoiceResponse)
async def update_invoice_by_id(
    tenant_id: str,
    invoice_id: str,
    invoice_data: InvoiceUpdateModel,
    user=Depends(get_current_user),
):
    """
    Update invoice data
    """
    try:
        # Get existing invoice
        existing_invoice = await get_invoice(invoice_id)

        if not existing_invoice or existing_invoice.get("tenantId") != tenant_id:
            raise HTTPException(status_code=404, detail="Invoice not found")

        # Update invoice
        update_data = invoice_data.dict(exclude_unset=True)
        updated_invoice = await update_invoice(
            invoice_id=invoice_id, update_data=update_data, updated_by=user["_id"]
        )

        return invoice_to_json(updated_invoice)

    except Exception as e:
        logger.error(f"Error updating invoice: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to update invoice: {str(e)}"
        )


@router.delete("/{invoice_id}")
async def delete_invoice_by_id(
    tenant_id: str,
    invoice_id: str,
    user=Depends(get_current_user),
):
    """
    Delete an invoice
    """
    try:
        # Check if invoice exists
        existing_invoice = await get_invoice(invoice_id)

        if not existing_invoice or existing_invoice.get("tenantId") != tenant_id:
            raise HTTPException(status_code=404, detail="Invoice not found")

        # Delete invoice
        await delete_invoice(invoice_id)

        return JSONResponse(content={"detail": "Invoice deleted successfully"})

    except Exception as e:
        logger.error(f"Error deleting invoice: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to delete invoice: {str(e)}"
        )


@router.post("/{invoice_id}/process")
async def process_invoice(
    tenant_id: str,
    invoice_id: str,
    user=Depends(get_current_user),
):
    """
    Manually trigger OCR processing for an invoice
    """
    try:
        # Get existing invoice
        existing_invoice = await get_invoice(invoice_id)

        if not existing_invoice or existing_invoice.get("tenantId") != tenant_id:
            raise HTTPException(status_code=404, detail="Invoice not found")

        # Update status to PENDING
        await update_invoice(
            invoice_id=invoice_id,
            update_data={"status": "PENDING"},
            updated_by=user["_id"],
        )

        # Start processing task
        process_invoice_task.delay(
            str(invoice_id), tenant_id, existing_invoice.get("fileUrl")
        )

        return JSONResponse(content={"detail": "Invoice processing started"})

    except Exception as e:
        logger.error(f"Error processing invoice: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to process invoice: {str(e)}"
        )
