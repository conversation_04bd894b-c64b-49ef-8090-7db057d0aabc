import logging
from datetime import datetime
from typing import Optional

from api.v1.auth import get_current_user
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from models.invoice import get_invoice, update_invoice
from models.tenant import get_tenant, update_tenant
from services.quickbooks import (
    exchange_code_for_tokens,
    get_qbo_auth_url,
    get_qbo_chart_of_accounts,
    get_qbo_tax_codes,
    push_invoice_to_qbo,
    refresh_qbo_tokens,
)

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter()


# Models
class QBOAuthResponse(BaseModel):
    auth_url: str


class QBOTokensResponse(BaseModel):
    access_token: str
    refresh_token: str
    expires_at: datetime
    realm_id: str


class QBOAuthCode(BaseModel):
    code: str
    realm_id: str
    state: Optional[str] = None


class QBOPushResponse(BaseModel):
    success: bool
    qbo_txn_id: Optional[str] = None
    error: Optional[str] = None


class QBOAccount(BaseModel):
    id: str
    name: str
    account_type: str
    fully_qualified_name: str


class QBOTaxCode(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    active: bool
    tax_rate: Optional[float] = None


@router.get("/auth-url", response_model=QBOAuthResponse)
async def get_auth_url(
    tenant_id: str,
    user=Depends(get_current_user),
):
    """
    Get QuickBooks OAuth authorization URL
    """
    try:
        # Get redirect URI from environment variable with fallback
        auth_url = get_qbo_auth_url(tenant_id)
        return {"auth_url": auth_url}

    except Exception as e:
        logger.error(f"Error getting QBO auth URL: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get QBO auth URL: {str(e)}"
        )


@router.post("/auth-callback", response_model=QBOTokensResponse)
async def handle_auth_callback(
    tenant_id: str,
    qbo_auth: QBOAuthCode,
    user=Depends(get_current_user),
):
    """
    Handle QuickBooks OAuth callback - exchange code for tokens
    """
    try:
        # Exchange code for tokens
        tokens = await exchange_code_for_tokens(qbo_auth.code, qbo_auth.realm_id)

        # Update tenant with QBO tokens
        await update_tenant(
            tenant_id=tenant_id,
            update_data={"qboRealmId": qbo_auth.realm_id, "qboTokens": tokens},
            updated_by=user["_id"],
        )

        return {
            "access_token": tokens["access_token"],
            "refresh_token": tokens["refresh_token"],
            "expires_at": tokens["expires_at"],
            "realm_id": qbo_auth.realm_id,
        }

    except Exception as e:
        logger.error(f"Error handling QBO auth callback: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to handle QBO auth callback: {str(e)}"
        )


@router.post("/refresh-tokens", response_model=QBOTokensResponse)
async def refresh_tokens(
    tenant_id: str,
    user=Depends(get_current_user),
):
    """
    Refresh QBO access tokens
    """
    try:
        # Get tenant
        tenant = await get_tenant(tenant_id)

        if not tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")

        if not tenant.get("qboTokens") or not tenant.get("qboRealmId"):
            raise HTTPException(
                status_code=400, detail="QBO not configured for this tenant"
            )

        # Refresh tokens
        new_tokens = await refresh_qbo_tokens(
            tenant["qboTokens"]["refresh_token"], tenant["qboRealmId"]
        )

        # Update tenant with new tokens
        await update_tenant(
            tenant_id=tenant_id,
            update_data={"qboTokens": new_tokens},
            updated_by=user["_id"],
        )

        return {
            "access_token": new_tokens["access_token"],
            "refresh_token": new_tokens["refresh_token"],
            "expires_at": new_tokens["expires_at"],
            "realm_id": tenant["qboRealmId"],
        }

    except Exception as e:
        logger.error(f"Error refreshing QBO tokens: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to refresh QBO tokens: {str(e)}"
        )


@router.post("/push-invoice/{invoice_id}", response_model=QBOPushResponse)
async def push_invoice(
    tenant_id: str,
    invoice_id: str,
    user=Depends(get_current_user),
):
    """
    Push invoice to QuickBooks Online
    """
    try:
        # Get invoice
        invoice = await get_invoice(invoice_id)

        if not invoice or invoice.get("tenantId") != tenant_id:
            raise HTTPException(status_code=404, detail="Invoice not found")

        if invoice.get("status") != "READY":
            raise HTTPException(
                status_code=400, detail="Invoice not ready to be pushed"
            )

        # Get tenant for QBO credentials
        tenant = await get_tenant(tenant_id)

        if not tenant or not tenant.get("qboTokens") or not tenant.get("qboRealmId"):
            raise HTTPException(
                status_code=400, detail="QBO not configured for this tenant"
            )

        # Push to QBO
        qbo_result = await push_invoice_to_qbo(
            invoice=invoice,
            qbo_tokens=tenant["qboTokens"],
            realm_id=tenant["qboRealmId"],
        )

        # Update invoice status
        await update_invoice(
            invoice_id=invoice_id,
            update_data={"status": "PUSHED", "qboTxnId": qbo_result["txn_id"]},
            updated_by=user["_id"],
        )

        return {"success": True, "qbo_txn_id": qbo_result["txn_id"]}

    except Exception as e:
        logger.error(f"Error pushing invoice to QBO: {str(e)}")
        return {"success": False, "error": str(e)}


@router.get("/accounts", response_model=list[QBOAccount])
async def get_accounts(
    tenant_id: str,
    user=Depends(get_current_user),
):
    """
    Get QuickBooks chart of accounts
    """
    try:
        # Get tenant
        tenant = await get_tenant(tenant_id)

        if not tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")

        if not tenant.get("qboTokens") or not tenant.get("qboRealmId"):
            raise HTTPException(
                status_code=400, detail="QBO not configured for this tenant"
            )

        # Get accounts
        accounts = await get_qbo_chart_of_accounts(
            tenant["qboTokens"]["access_token"], tenant["qboRealmId"]
        )

        return accounts

    except Exception as e:
        logger.error(f"Error getting QBO accounts: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get QBO accounts: {str(e)}"
        )


@router.get("/tax-codes", response_model=list[QBOTaxCode])
async def get_tax_codes(
    tenant_id: str,
    user=Depends(get_current_user),
):
    """
    Get QuickBooks tax codes
    """
    try:
        # Get tenant
        tenant = await get_tenant(tenant_id)

        if not tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")

        if not tenant.get("qboTokens") or not tenant.get("qboRealmId"):
            raise HTTPException(
                status_code=400, detail="QBO not configured for this tenant"
            )

        # Get tax codes
        tax_codes = await get_qbo_tax_codes(
            tenant["qboTokens"]["access_token"], tenant["qboRealmId"]
        )

        return tax_codes

    except Exception as e:
        logger.error(f"Error getting QBO tax codes: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get QBO tax codes: {str(e)}"
        )
