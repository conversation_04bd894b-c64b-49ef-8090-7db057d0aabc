import logging
from datetime import datetime
from typing import List, Optional

from backend.api.v1.auth import get_current_user, verify_admin_role
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel

from backend.models.tenant import (
    create_tenant,
    delete_tenant,
    get_tenant,
    list_tenants,
    tenant_to_json,
    update_tenant,
)

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter()


# Models
class QBOTokens(BaseModel):
    access_token: str
    refresh_token: str
    expires_at: datetime


class TenantCreate(BaseModel):
    name: str
    qboRealmId: Optional[str] = None
    qboTokens: Optional[QBOTokens] = None


class TenantUpdate(BaseModel):
    name: Optional[str] = None
    qboRealmId: Optional[str] = None
    qboTokens: Optional[QBOTokens] = None


class TenantResponse(BaseModel):
    id: str
    name: str
    qboRealmId: Optional[str] = None
    qboTokens: Optional[QBOTokens] = None
    createdAt: datetime
    updatedAt: datetime


@router.post("/", response_model=TenantResponse)
async def create_new_tenant(
    tenant: TenantCreate,
    user=Depends(verify_admin_role),
):
    """
    Create a new tenant (admin only)
    """
    try:
        tenant_id = await create_tenant(
            name=tenant.name,
            qbo_realm_id=tenant.qboRealmId,
            qbo_tokens=tenant.qboTokens.dict() if tenant.qboTokens else None,
            created_by=user["_id"],
        )

        new_tenant = await get_tenant(tenant_id)
        return tenant_to_json(new_tenant)

    except Exception as e:
        logger.error(f"Error creating tenant: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to create tenant: {str(e)}"
        )


@router.get("/", response_model=List[TenantResponse])
async def get_tenants(
    name: Optional[str] = Query(None, description="Filter by tenant name"),
    limit: int = Query(20, ge=1, le=100),
    offset: int = Query(0, ge=0),
    user=Depends(verify_admin_role),
):
    """
    List tenants with optional filters (admin only)
    """
    try:
        filters = {}
        if name:
            filters["name"] = {
                "$regex": name,
                "$options": "i",
            }  # Case-insensitive search

        tenants = await list_tenants(
            filters=filters,
            limit=limit,
            offset=offset,
        )

        return [tenant_to_json(tenant) for tenant in tenants]

    except Exception as e:
        logger.error(f"Error listing tenants: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list tenants: {str(e)}")


@router.get("/{tenant_id}", response_model=TenantResponse)
async def get_tenant_by_id(
    tenant_id: str,
    user=Depends(get_current_user),
):
    """
    Get a specific tenant by ID (users can only access their own tenant)
    """
    try:
        # Check if user has access to this tenant
        if "admin" not in user.get("roles", []) and user.get("tenantId") != tenant_id:
            raise HTTPException(
                status_code=403, detail="Not authorized to access this tenant"
            )

        tenant = await get_tenant(tenant_id)

        if not tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")

        return tenant_to_json(tenant)

    except Exception as e:
        logger.error(f"Error getting tenant: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get tenant: {str(e)}")


@router.patch("/{tenant_id}", response_model=TenantResponse)
async def update_tenant_by_id(
    tenant_id: str,
    tenant_data: TenantUpdate,
    user=Depends(verify_admin_role),
):
    """
    Update tenant data (admin only)
    """
    try:
        # Check if tenant exists
        existing_tenant = await get_tenant(tenant_id)

        if not existing_tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")

        # Update tenant
        update_data = tenant_data.dict(exclude_unset=True)
        updated_tenant = await update_tenant(
            tenant_id=tenant_id, update_data=update_data, updated_by=user["_id"]
        )

        return tenant_to_json(updated_tenant)

    except Exception as e:
        logger.error(f"Error updating tenant: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to update tenant: {str(e)}"
        )


@router.delete("/{tenant_id}")
async def delete_tenant_by_id(
    tenant_id: str,
    user=Depends(verify_admin_role),
):
    """
    Delete a tenant (admin only)
    """
    try:
        # Check if tenant exists
        existing_tenant = await get_tenant(tenant_id)

        if not existing_tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")

        # Delete tenant
        await delete_tenant(tenant_id)

        return {"detail": "Tenant deleted successfully"}

    except Exception as e:
        logger.error(f"Error deleting tenant: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to delete tenant: {str(e)}"
        )
