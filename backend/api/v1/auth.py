import logging
from typing import Any, Dict, Optional

from fastapi import Depends, HTTPException
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from backend.models.user import get_user_by_id
from backend.services.firebase import verify_firebase_token

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize security scheme
security = HTTPBearer()


async def get_current_user(
    authorization: HTTPAuthorizationCredentials = Depends(security),
    tenant_id: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Verify the Firebase ID token in the Authorization header and return the user
    """
    if not authorization:
        raise HTTPException(
            status_code=401,
            detail="Missing authentication token",
        )

    token = authorization.credentials
    try:
        # Verify Firebase token
        decoded_token = await verify_firebase_token(token)
        user_id = decoded_token["uid"]

        # Get user from database
        user = await get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found in database")

        # Verify tenant access if tenant_id is provided
        if tenant_id and user.get("tenantId") != tenant_id:
            raise HTTPException(
                status_code=403, detail="User does not have access to this tenant"
            )

        # Add decoded token info to user
        user["firebase"] = decoded_token

        return user

    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(
            status_code=401,
            detail=f"Invalid authentication token: {str(e)}",
        )


async def verify_admin_role(
    user: Dict[str, Any] = Depends(get_current_user),
) -> Dict[str, Any]:
    """
    Verify the user has admin role
    """
    if "admin" not in user.get("roles", []):
        raise HTTPException(status_code=403, detail="Admin role required")
    return user
