import logging
import os
from typing import Any, Dict, Optional

from fastapi import Depends, HTTPException
from fastapi.security import H<PERSON><PERSON><PERSON>orizationCredentials, HTTPBearer

from backend.models.user import get_user_by_id
# Firebase import moved to function level to avoid import-time errors

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize security scheme
security = HTTPBearer()


async def get_current_user(
    authorization: HTTPAuthorizationCredentials = Depends(security),
    tenant_id: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Verify the Firebase ID token in the Authorization header and return the user
    """
    print("=== get_current_user called ===")
    logger.info("get_current_user function called")

    if not authorization:
        print("=== No authorization provided ===")
        raise HTTPException(
            status_code=401,
            detail="Missing authentication token",
        )

    token = authorization.credentials
    try:
        # Check if we're in development mode with a test token
        env = os.environ.get("ENVIRONMENT")
        print(f"=== AUTH: Environment: {env}, Token starts with test_: {token.startswith('test_')}")
        logger.debug(f"Environment: {env}, To<PERSON> starts with test_: {token.startswith('test_')}")
        if env == "development" and token.startswith("test_"):
            # For development, bypass Firebase entirely and decode test token manually
            logger.warning("Development mode: bypassing Firebase authentication")
            try:
                # Parse the test token manually
                token_parts = token[5:].split(".")  # Remove 'test_' prefix
                if len(token_parts) != 3:
                    raise ValueError("Invalid test token format")

                import base64
                import json

                # Decode the payload
                payload_b64 = token_parts[1]
                # Add padding if needed
                padding = (
                    "=" * (4 - len(payload_b64) % 4) if len(payload_b64) % 4 != 0 else ""
                )
                payload_json = base64.urlsafe_b64decode(payload_b64 + padding)
                decoded_token = json.loads(payload_json)
                user_id = decoded_token["uid"]

                # Create a default test user
                user = {
                    "_id": user_id,
                    "email": decoded_token.get("email", "<EMAIL>"),
                    "tenantId": tenant_id or "6818967d95deaef99567f56b",
                    "roles": ["admin", "user"],
                }
            except Exception as e:
                logger.error(f"Failed to decode test token: {e}")
                raise HTTPException(
                    status_code=401, detail=f"Invalid test token: {str(e)}"
                )
        else:
            # For production, use Firebase verification
            from backend.services.firebase import verify_firebase_token
            decoded_token = await verify_firebase_token(token)
            user_id = decoded_token["uid"]

            # Get user from database for real tokens
            user = await get_user_by_id(user_id)
            if not user:
                raise HTTPException(
                    status_code=404, detail="User not found in database"
                )

        # Verify tenant access if tenant_id is provided
        if (
            tenant_id
            and user.get("tenantId") != tenant_id
            and "admin" not in user.get("roles", [])
        ):
            raise HTTPException(
                status_code=403, detail="User does not have access to this tenant"
            )

        # Add decoded token info to user (if available)
        if 'decoded_token' in locals():
            user["firebase"] = decoded_token

        return user

    except Exception as e:
        import traceback
        tb = traceback.format_exc()
        logger.error(f"Authentication error: {str(e)}")
        logger.error(f"Full traceback: {tb}")

        # Print to stdout as well to ensure we see it
        print(f"AUTHENTICATION ERROR: {str(e)}")
        print(f"FULL TRACEBACK: {tb}")

        raise HTTPException(
            status_code=401,
            detail=f"Invalid authentication token: {str(e)}",
        )


async def verify_admin_role(
    user: Dict[str, Any] = Depends(get_current_user),
) -> Dict[str, Any]:
    """
    Verify the user has admin role
    """
    if "admin" not in user.get("roles", []):
        raise HTTPException(status_code=403, detail="Admin role required")
    return user
