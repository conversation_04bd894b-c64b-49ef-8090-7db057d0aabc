from fastapi import APIRouter

from .invoices import router as invoices_router
from .qbo import router as qbo_router
from .tenants import router as tenants_router
from .vendors import router as vendors_router

api_router = APIRouter(prefix="/v1")

# Include routers for different resources
api_router.include_router(
    invoices_router,
    prefix="/{tenant_id}/invoices",
    tags=["invoices"],
)

api_router.include_router(
    vendors_router,
    prefix="/{tenant_id}/vendors",
    tags=["vendors"],
)

api_router.include_router(
    tenants_router,
    prefix="/tenants",
    tags=["tenants"],
)

api_router.include_router(
    qbo_router,
    prefix="/{tenant_id}/qbo",
    tags=["quickbooks"],
)
