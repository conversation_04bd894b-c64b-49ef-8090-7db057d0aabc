import hashlib
import logging
from typing import Any, Dict, List, Optional

from .db import db, get_timestamp, objectid_to_str, str_to_objectid

# Configure logging
logger = logging.getLogger(__name__)


async def create_invoice(
    tenant_id: str,
    file_url: str,
    status: str = "PENDING",
    created_by: str = None,
    sha256: str = None,
) -> str:
    """
    Create a new invoice record
    """
    now = get_timestamp()

    # Create invoice document
    invoice = {
        "tenantId": tenant_id,
        "fileUrl": file_url,
        "sha256": sha256,
        "fields": {"validated": False},
        "status": status,
        "createdAt": now,
        "updatedAt": now,
        "createdBy": str_to_objectid(created_by) if created_by else None,
    }

    result = await db.invoices.insert_one(invoice)
    return result.inserted_id


async def get_invoice(invoice_id: str) -> Dict[str, Any]:
    """
    Get invoice by ID
    """
    object_id = str_to_objectid(invoice_id)
    invoice = await db.invoices.find_one({"_id": object_id})
    return invoice


async def list_invoices(
    tenant_id: str,
    filters: Dict[str, Any] = None,
    limit: int = 20,
    offset: int = 0,
    sort_by: str = "updatedAt",
    sort_dir: int = -1,
) -> List[Dict[str, Any]]:
    """
    List invoices with filtering, pagination and sorting
    """
    # Build query
    query = {"tenantId": tenant_id}

    # Add filters if provided
    if filters:
        query.update(filters)

    # Execute query with pagination and sorting
    cursor = db.invoices.find(query).sort(sort_by, sort_dir).skip(offset).limit(limit)

    # Convert cursor to list
    invoices = await cursor.to_list(length=limit)
    return invoices


async def update_invoice(
    invoice_id: str, update_data: Dict[str, Any], updated_by: str = None
) -> Dict[str, Any]:
    """
    Update invoice data
    """
    object_id = str_to_objectid(invoice_id)
    now = get_timestamp()

    # Prepare update document
    update_doc = {"$set": {"updatedAt": now}}

    # Add updated_by if provided
    if updated_by:
        update_doc["$set"]["updatedBy"] = str_to_objectid(updated_by)

    # Process updates
    for key, value in update_data.items():
        if key == "fields":
            # For fields, we want to update individual fields without
            # overwriting the entire object
            for field_key, field_value in value.items():
                update_doc["$set"][f"fields.{field_key}"] = field_value
        else:
            update_doc["$set"][key] = value

    # Update document
    result = await db.invoices.find_one_and_update(
        {"_id": object_id}, update_doc, return_document=True
    )

    return result


async def delete_invoice(invoice_id: str) -> bool:
    """
    Delete an invoice
    """
    object_id = str_to_objectid(invoice_id)
    result = await db.invoices.delete_one({"_id": object_id})
    return result.deleted_count > 0


async def update_ocr_results(
    invoice_id: str, ocr_raw: Dict[str, Any], extracted_fields: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Update invoice with OCR results
    """
    object_id = str_to_objectid(invoice_id)
    now = get_timestamp()

    # Prepare update document
    update_doc = {"$set": {"ocrRaw": ocr_raw, "updatedAt": now, "status": "READY"}}

    # Add extracted fields
    for key, value in extracted_fields.items():
        update_doc["$set"][f"fields.{key}"] = value

    # Update document
    result = await db.invoices.find_one_and_update(
        {"_id": object_id}, update_doc, return_document=True
    )

    return result


async def check_duplicate_invoice(
    tenant_id: str, sha256: str
) -> Optional[Dict[str, Any]]:
    """
    Check if invoice with same SHA256 hash already exists
    """
    if not sha256:
        return None

    invoice = await db.invoices.find_one({"tenantId": tenant_id, "sha256": sha256})

    return invoice


def calculate_file_hash(file_content: bytes) -> str:
    """
    Calculate SHA256 hash of file content
    """
    return hashlib.sha256(file_content).hexdigest()


def invoice_to_json(invoice: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert invoice document to JSON-serializable dict
    """
    if not invoice:
        return None

    # Create a copy to avoid modifying the original
    invoice_copy = invoice.copy()

    # Convert ObjectId to string
    invoice_copy["id"] = objectid_to_str(invoice_copy.pop("_id"))

    # Convert other ObjectIds
    if "createdBy" in invoice_copy:
        invoice_copy["createdBy"] = objectid_to_str(invoice_copy["createdBy"])

    if "updatedBy" in invoice_copy:
        invoice_copy["updatedBy"] = objectid_to_str(invoice_copy["updatedBy"])

    return invoice_copy
