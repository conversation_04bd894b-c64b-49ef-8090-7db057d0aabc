import logging
import os
from datetime import datetime

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient

# Configure logging
logger = logging.getLogger(__name__)

# Initialize MongoDB client
client = None
_db = None


def get_db():
    """Get the database connection"""
    global _db
    logger.debug(f"get_db() called, _db is: {_db}")
    if _db is None:
        logger.error("Database not initialized. Call init_db() first.")
        raise RuntimeError("Database not initialized. Call init_db() first.")
    logger.debug(f"Returning database connection: {_db}")
    return _db


# For backward compatibility
db = None


async def init_db():
    """Initialize the MongoDB connection"""
    global client, _db, db

    mongo_uri = os.environ.get("MONGO_URI", "mongodb://localhost:27017")
    db_name = os.environ.get("MONGO_DB_NAME", "aiclearbill")

    try:
        # Create AsyncIOMotorClient
        client = AsyncIOMotorClient(mongo_uri)
        _db = client[db_name]
        db = _db  # For backward compatibility

        # Simple ping to verify connection
        await _db.command("ping")
        logger.info(f"Connected to MongoDB at {mongo_uri}")

        # Create indexes
        await create_indexes()

        return _db
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        raise


async def create_indexes():
    """Create necessary indexes for collections"""
    try:
        # Tenants collection
        await _db.tenants.create_index("name", unique=True)

        # Users collection
        await _db.users.create_index("email", unique=True)
        await _db.users.create_index([("tenantId", 1), ("_id", 1)])

        # Vendors collection
        await _db.vendors.create_index([("tenantId", 1), ("name", 1)], unique=True)

        # Invoices collection
        await _db.invoices.create_index([("tenantId", 1), ("status", 1)])
        await _db.invoices.create_index([("tenantId", 1), ("sha256", 1)])
        await _db.invoices.create_index([("tenantId", 1), ("fields.vendorId", 1)])

        logger.info("Database indexes created successfully")
    except Exception as e:
        logger.error(f"Failed to create indexes: {e}")
        raise


def get_timestamp():
    """Get current timestamp for document creation/update"""
    return datetime.utcnow()


def objectid_to_str(obj_id):
    """Convert ObjectId to string"""
    if isinstance(obj_id, ObjectId):
        return str(obj_id)
    return obj_id


def str_to_objectid(id_str):
    """Convert string to ObjectId if valid"""
    try:
        if isinstance(id_str, str):
            return ObjectId(id_str)
        return id_str
    except BaseException:
        return id_str
