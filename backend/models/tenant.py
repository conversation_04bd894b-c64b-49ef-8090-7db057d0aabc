import logging
from typing import Any, Dict, List

from .db import get_timestamp, objectid_to_str, str_to_objectid


def get_db():
    """Get the database connection"""
    from .db import get_db as _get_db
    return _get_db()

# Configure logging
logger = logging.getLogger(__name__)


async def create_tenant(
    name: str,
    qbo_realm_id: str = None,
    qbo_tokens: Dict[str, Any] = None,
    created_by: str = None,
) -> str:
    """
    Create a new tenant
    """
    now = get_timestamp()

    # Create tenant document
    tenant = {
        "name": name,
        "qboRealmId": qbo_realm_id,
        "qboTokens": qbo_tokens,
        "createdAt": now,
        "updatedAt": now,
        "createdBy": str_to_objectid(created_by) if created_by else None,
    }

    db = get_db()
    result = await db.tenants.insert_one(tenant)
    return result.inserted_id


async def get_tenant(tenant_id: str) -> Dict[str, Any]:
    """
    Get tenant by ID
    """
    object_id = str_to_objectid(tenant_id)
    db = get_db()
    tenant = await db.tenants.find_one({"_id": object_id})
    return tenant


async def list_tenants(
    filters: Dict[str, Any] = None,
    limit: int = 20,
    offset: int = 0,
    sort_by: str = "name",
    sort_dir: int = 1,
) -> List[Dict[str, Any]]:
    """
    List tenants with filtering, pagination and sorting
    """
    # Build query
    query = {}

    # Add filters if provided
    if filters:
        query.update(filters)

    # Execute query with pagination and sorting
    db = get_db()
    cursor = db.tenants.find(query).sort(sort_by, sort_dir).skip(offset).limit(limit)

    # Convert cursor to list
    tenants = await cursor.to_list(length=limit)
    return tenants


async def update_tenant(
    tenant_id: str, update_data: Dict[str, Any], updated_by: str = None
) -> Dict[str, Any]:
    """
    Update tenant data
    """
    object_id = str_to_objectid(tenant_id)
    now = get_timestamp()

    # Prepare update document
    update_doc = {"$set": {"updatedAt": now}}

    # Add updated_by if provided
    if updated_by:
        update_doc["$set"]["updatedBy"] = str_to_objectid(updated_by)

    # Add update fields
    for key, value in update_data.items():
        update_doc["$set"][key] = value

    # Update document
    db = get_db()
    result = await db.tenants.find_one_and_update(
        {"_id": object_id}, update_doc, return_document=True
    )

    return result


async def delete_tenant(tenant_id: str) -> bool:
    """
    Delete a tenant
    """
    object_id = str_to_objectid(tenant_id)
    db = get_db()
    result = await db.tenants.delete_one({"_id": object_id})
    return result.deleted_count > 0


def tenant_to_json(tenant: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert tenant document to JSON-serializable dict
    """
    if not tenant:
        return None

    # Create a copy to avoid modifying the original
    tenant_copy = tenant.copy()

    # Convert ObjectId to string
    tenant_copy["id"] = objectid_to_str(tenant_copy.pop("_id"))

    # Convert other ObjectIds
    if "createdBy" in tenant_copy:
        tenant_copy["createdBy"] = objectid_to_str(tenant_copy["createdBy"])

    if "updatedBy" in tenant_copy:
        tenant_copy["updatedBy"] = objectid_to_str(tenant_copy["updatedBy"])

    return tenant_copy
