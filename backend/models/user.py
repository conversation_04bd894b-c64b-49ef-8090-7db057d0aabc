import logging
from typing import Any, Dict, List, Optional

from .db import get_timestamp, objectid_to_str, str_to_objectid


def get_db():
    """Get the database connection"""
    from .db import db
    return db

# Configure logging
logger = logging.getLogger(__name__)


async def create_user(
    user_id: str,
    email: str,
    tenant_id: str,
    roles: List[str] = None,
    created_by: str = None,
) -> str:
    """
    Create a new user
    """
    now = get_timestamp()

    # Create user document
    user = {
        "_id": user_id,  # Use Firebase UID as MongoDB ID
        "email": email,
        "tenantId": tenant_id,
        "roles": roles or ["user"],
        "createdAt": now,
        "updatedAt": now,
        "createdBy": str_to_objectid(created_by) if created_by else None,
    }

    try:
        db = get_db()
        result = await db.users.insert_one(user)
        return result.inserted_id
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        # Check if it's a duplicate key error
        if "duplicate key" in str(e):
            return user_id  # Return the user ID anyway
        raise


async def get_user_by_id(user_id: str) -> Optional[Dict[str, Any]]:
    """
    Get user by ID (Firebase UID)
    """
    db = get_db()
    user = await db.users.find_one({"_id": user_id})
    return user


async def get_user_by_email(email: str) -> Optional[Dict[str, Any]]:
    """
    Get user by email
    """
    db = get_db()
    user = await db.users.find_one({"email": email})
    return user


async def list_users(
    tenant_id: str = None,
    filters: Dict[str, Any] = None,
    limit: int = 50,
    offset: int = 0,
) -> List[Dict[str, Any]]:
    """
    List users with filtering and pagination
    """
    # Build query
    query = {}
    if tenant_id:
        query["tenantId"] = tenant_id

    # Add filters if provided
    if filters:
        query.update(filters)

    # Execute query with pagination
    db = get_db()
    cursor = db.users.find(query).skip(offset).limit(limit)

    # Convert cursor to list
    users = await cursor.to_list(length=limit)
    return users


async def update_user(
    user_id: str, update_data: Dict[str, Any], updated_by: str = None
) -> Dict[str, Any]:
    """
    Update user data
    """
    now = get_timestamp()

    # Prepare update document
    update_doc = {"$set": {"updatedAt": now}}

    # Add updated_by if provided
    if updated_by:
        update_doc["$set"]["updatedBy"] = str_to_objectid(updated_by)

    # Add update fields
    for key, value in update_data.items():
        update_doc["$set"][key] = value

    # Update document
    db = get_db()
    result = await db.users.find_one_and_update(
        {"_id": user_id}, update_doc, return_document=True
    )

    return result


async def delete_user(user_id: str) -> bool:
    """
    Delete a user
    """
    db = get_db()
    result = await db.users.delete_one({"_id": user_id})
    return result.deleted_count > 0


def user_to_json(user: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert user document to JSON-serializable dict
    """
    if not user:
        return None

    # Create a copy to avoid modifying the original
    user_copy = user.copy()

    # Convert ObjectIds
    if "createdBy" in user_copy:
        user_copy["createdBy"] = objectid_to_str(user_copy["createdBy"])

    if "updatedBy" in user_copy:
        user_copy["updatedBy"] = objectid_to_str(user_copy["updatedBy"])

    return user_copy
