import logging
from typing import Any, Dict, List

from .db import get_db, get_timestamp, objectid_to_str, str_to_objectid

# Configure logging
logger = logging.getLogger(__name__)


async def create_vendor(
    tenant_id: str,
    name: str,
    default_account: str = None,
    default_tax_code: str = None,
    created_by: str = None,
) -> str:
    """
    Create a new vendor
    """
    now = get_timestamp()

    # Create vendor document
    vendor = {
        "tenantId": tenant_id,
        "name": name,
        "defaultAccount": default_account,
        "defaultTaxCode": default_tax_code,
        "createdAt": now,
        "updatedAt": now,
        "createdBy": str_to_objectid(created_by) if created_by else None,
    }

    db = get_db()
    result = await db.vendors.insert_one(vendor)
    return result.inserted_id


async def get_vendor(vendor_id: str) -> Dict[str, Any]:
    """
    Get vendor by ID
    """
    object_id = str_to_objectid(vendor_id)
    db = get_db()
    vendor = await db.vendors.find_one({"_id": object_id})
    return vendor


async def get_vendor_by_name(tenant_id: str, name: str) -> Dict[str, Any]:
    """
    Get vendor by name (case-insensitive)
    """
    db = get_db()
    vendor = await db.vendors.find_one(
        {"tenantId": tenant_id, "name": {"$regex": f"^{name}$", "$options": "i"}}
    )
    return vendor


async def list_vendors(
    tenant_id: str,
    filters: Dict[str, Any] = None,
    limit: int = 50,
    offset: int = 0,
    sort_by: str = "name",
    sort_dir: int = 1,
) -> List[Dict[str, Any]]:
    """
    List vendors with filtering, pagination and sorting
    """
    # Build query
    query = {"tenantId": tenant_id}

    # Add filters if provided
    if filters:
        query.update(filters)

    # Execute query with pagination and sorting
    db = get_db()
    cursor = db.vendors.find(query).sort(sort_by, sort_dir).skip(offset).limit(limit)

    # Convert cursor to list
    vendors = await cursor.to_list(length=limit)
    return vendors


async def update_vendor(
    vendor_id: str, update_data: Dict[str, Any], updated_by: str = None
) -> Dict[str, Any]:
    """
    Update vendor data
    """
    object_id = str_to_objectid(vendor_id)
    now = get_timestamp()

    # Prepare update document
    update_doc = {"$set": {"updatedAt": now}}

    # Add updated_by if provided
    if updated_by:
        update_doc["$set"]["updatedBy"] = str_to_objectid(updated_by)

    # Add update fields
    for key, value in update_data.items():
        update_doc["$set"][key] = value

    # Update document
    db = get_db()
    result = await db.vendors.find_one_and_update(
        {"_id": object_id}, update_doc, return_document=True
    )

    return result


async def delete_vendor(vendor_id: str) -> bool:
    """
    Delete a vendor
    """
    object_id = str_to_objectid(vendor_id)
    db = get_db()
    result = await db.vendors.delete_one({"_id": object_id})
    return result.deleted_count > 0


def vendor_to_json(vendor: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert vendor document to JSON-serializable dict
    """
    if not vendor:
        return None

    # Create a copy to avoid modifying the original
    vendor_copy = vendor.copy()

    # Convert ObjectId to string
    vendor_copy["id"] = objectid_to_str(vendor_copy.pop("_id"))

    # Convert other ObjectIds
    if "createdBy" in vendor_copy:
        vendor_copy["createdBy"] = objectid_to_str(vendor_copy["createdBy"])

    if "updatedBy" in vendor_copy:
        vendor_copy["updatedBy"] = objectid_to_str(vendor_copy["updatedBy"])

    return vendor_copy
