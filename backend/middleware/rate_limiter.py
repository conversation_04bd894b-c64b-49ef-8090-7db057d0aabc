import time
from typing import Callable, Dict, Optional, Tuple

from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

# Simple in-memory rate limiter
class InMemoryStore:
    def __init__(self):
        self.store: Dict[str, Tuple[int, float]] = {}
    
    def get(self, key: str) -> Optional[Tuple[int, float]]:
        # Clean up expired entries
        now = time.time()
        self.store = {k: v for k, v in self.store.items() if now - v[1] < 60}
        
        return self.store.get(key)
    
    def increment(self, key: str, window: float = 60.0) -> Tuple[int, float]:
        now = time.time()
        if key in self.store:
            count, timestamp = self.store[key]
            # If the window has expired, reset the counter
            if now - timestamp >= window:
                self.store[key] = (1, now)
                return (1, now)
            else:
                self.store[key] = (count + 1, timestamp)
                return (count + 1, timestamp)
        else:
            self.store[key] = (1, now)
            return (1, now)


class RateLimiter(BaseHTTPMiddleware):
    def __init__(
        self,
        app: ASGIApp,
        rate_limit_per_minute: int = 60,
        exclude_paths: list = None,
        store: Optional[InMemoryStore] = None,
        get_key: Optional[Callable[[Request], str]] = None,
    ):
        super().__init__(app)
        self.rate_limit_per_minute = rate_limit_per_minute
        self.exclude_paths = exclude_paths or []
        self.store = store or InMemoryStore()
        self.get_key = get_key or self._default_key_func
    
    async def dispatch(self, request: Request, call_next):
        # Skip rate limiting for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
        
        # Get client key (IP address by default)
        key = self.get_key(request)
        
        # Check rate limit
        count, _ = self.store.increment(key)
        
        # Add rate limit headers
        headers = {
            "X-RateLimit-Limit": str(self.rate_limit_per_minute),
            "X-RateLimit-Remaining": str(max(0, self.rate_limit_per_minute - count)),
        }
        
        # If rate limit exceeded, return 429 Too Many Requests
        if count > self.rate_limit_per_minute:
            return JSONResponse(
                status_code=429,
                content={"detail": "Rate limit exceeded"},
                headers=headers,
            )
        
        # Process the request
        response = await call_next(request)
        
        # Add rate limit headers to the response
        for name, value in headers.items():
            response.headers[name] = value
        
        return response
    
    def _default_key_func(self, request: Request) -> str:
        # Use client IP as the default key
        forwarded = request.headers.get("X-Forwarded-For")
        if forwarded:
            return forwarded.split(",")[0].strip()
        return request.client.host or ""


def add_rate_limiter(app: FastAPI, rate_limit_per_minute: int = 60, exclude_paths: list = None):
    """Add rate limiting middleware to the FastAPI app"""
    app.add_middleware(
        RateLimiter,
        rate_limit_per_minute=rate_limit_per_minute,
        exclude_paths=exclude_paths or ["/health", "/docs", "/openapi.json"],
    )
