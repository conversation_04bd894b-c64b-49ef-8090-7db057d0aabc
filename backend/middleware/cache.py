import json
import os
from typing import Callable, List, Optional, Union

import redis
from fastapi import Fast<PERSON><PERSON>, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

# Redis client
redis_client = redis.Redis(
    host=os.environ.get("REDIS_HOST", "localhost"),
    port=int(os.environ.get("REDIS_PORT", 6379)),
    db=0,
    decode_responses=False,  # Keep as bytes for binary data
)


class CacheMiddleware(BaseHTTPMiddleware):
    def __init__(
        self,
        app: ASGIApp,
        ttl: int = 60,  # Cache TTL in seconds
        exclude_paths: List[str] = None,
        include_paths: List[str] = None,
        cache_key_builder: Optional[Callable[[Request], str]] = None,
    ):
        super().__init__(app)
        self.ttl = ttl
        self.exclude_paths = exclude_paths or []
        self.include_paths = include_paths or []
        self.cache_key_builder = cache_key_builder or self._default_cache_key_builder
    
    async def dispatch(self, request: Request, call_next):
        # Only cache GET requests
        if request.method != "GET":
            return await call_next(request)
        
        # Check if path should be cached
        path = request.url.path
        
        # Skip caching for excluded paths
        if any(path.startswith(excluded) for excluded in self.exclude_paths):
            return await call_next(request)
        
        # Only cache included paths if specified
        if self.include_paths and not any(path.startswith(included) for included in self.include_paths):
            return await call_next(request)
        
        # Build cache key
        cache_key = self.cache_key_builder(request)
        
        # Try to get from cache
        cached_response = redis_client.get(cache_key)
        if cached_response:
            # Parse cached response
            cached_data = json.loads(cached_response)
            
            # Create response from cached data
            response = Response(
                content=cached_data["content"],
                status_code=cached_data["status_code"],
                headers=cached_data["headers"],
                media_type=cached_data["media_type"],
            )
            
            # Add cache header
            response.headers["X-Cache"] = "HIT"
            
            return response
        
        # Process the request
        response = await call_next(request)
        
        # Only cache successful responses
        if 200 <= response.status_code < 400:
            # Get response content
            response_body = b""
            async for chunk in response.body_iterator:
                response_body += chunk
            
            # Create a new response with the same content
            new_response = Response(
                content=response_body,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=response.media_type,
            )
            
            # Cache the response
            cache_data = {
                "content": response_body.decode() if isinstance(response_body, bytes) else response_body,
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "media_type": response.media_type,
            }
            
            redis_client.setex(
                cache_key,
                self.ttl,
                json.dumps(cache_data),
            )
            
            # Add cache header
            new_response.headers["X-Cache"] = "MISS"
            
            return new_response
        
        return response
    
    def _default_cache_key_builder(self, request: Request) -> str:
        """Build a cache key from the request"""
        return f"cache:{request.method}:{request.url.path}:{request.query_params}"


def add_cache_middleware(
    app: FastAPI,
    ttl: int = 60,
    exclude_paths: List[str] = None,
    include_paths: List[str] = None,
):
    """Add caching middleware to the FastAPI app"""
    app.add_middleware(
        CacheMiddleware,
        ttl=ttl,
        exclude_paths=exclude_paths or ["/health", "/docs", "/openapi.json"],
        include_paths=include_paths,
    )
