from functools import wraps

from flask import g, jsonify, request

from backend.models.user import User


def require_auth(allowed_roles=None):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user_id = request.headers.get('X-User-Id')
            if not user_id:
                return jsonify({'error': 'Unauthorized'}), 401
            user = User.query.get(user_id)
            if not user:
                return jsonify({'error': 'Unauthorized'}), 401
            if allowed_roles and user.role not in allowed_roles:
                return jsonify({'error': 'Forbidden'}), 403
            g.current_user = user
            return f(*args, **kwargs)
        return decorated_function
    return decorator
