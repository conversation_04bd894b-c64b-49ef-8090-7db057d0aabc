import logging
import os

from celery import Celery

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Redis configuration
REDIS_HOST = os.environ.get("REDIS_HOST", "localhost")
REDIS_PORT = os.environ.get("REDIS_PORT", "6379")
REDIS_DB = os.environ.get("REDIS_DB", "0")
REDIS_PASSWORD = os.environ.get("REDIS_PASSWORD", "")

# Configure Celery
broker_url = f"redis://{':' + REDIS_PASSWORD + '@' if REDIS_PASSWORD else ''}{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
result_backend = broker_url

celery_app = Celery(
    "aiclearbill", broker=broker_url, backend=result_backend, include=["workers.tasks"]
)

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    worker_hijack_root_logger=False,
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_routes={"workers.tasks.process_invoice_task": {"queue": "invoice_processing"}},
)

# Add custom configuration
celery_app.conf.task_default_queue = "default"
celery_app.conf.worker_prefetch_multiplier = 1
celery_app.conf.task_time_limit = 600  # 10 minutes
celery_app.conf.task_soft_time_limit = 300  # 5 minutes

if __name__ == "__main__":
    celery_app.start()
