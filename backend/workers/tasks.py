import asyncio
import logging

from celery import Task

from backend.services.invoice_processor import process_invoice_file

from .celery_app import celery_app

# Configure logging
logger = logging.getLogger(__name__)


class AsyncTask(Task):
    """Task class that supports running async functions with event loop"""

    def run_async(self, func, *args, **kwargs):
        """Run async function in the event loop"""
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(func(*args, **kwargs))


@celery_app.task(bind=True, base=AsyncTask)
def process_invoice_task(self, invoice_id: str, tenant_id: str, file_path: str):
    """
    Celery task to process an invoice file
    """
    logger.info(f"Processing invoice {invoice_id} for tenant {tenant_id}")
    try:
        # Run the async function in the event loop
        _ = self.run_async(
            process_invoice_file,
            invoice_id=invoice_id,
            tenant_id=tenant_id,
            file_path=file_path,
        )  # Use underscore for unused result

        logger.info(f"Invoice {invoice_id} processed successfully")
        return {"status": "success", "invoice_id": invoice_id}
    except Exception as e:
        logger.error(f"Error processing invoice {invoice_id}: {str(e)}", exc_info=True)
        self.retry(exc=e, countdown=10, max_retries=3)
