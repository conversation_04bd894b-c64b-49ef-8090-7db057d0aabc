import logging
import os

import uvicorn
from backend.api.v1.router import api_router
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from backend.middleware.rate_limiter import add_rate_limiter
from backend.middleware.cache import add_cache_middleware
from backend.models.db import init_db
from backend.services.firebase import *  # Import Firebase service to initialize it

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("aiclearbill")

# Initialize FastAPI app
app = FastAPI(
    title="AIClearBill API",
    description="Multi-tenant OCR-powered invoice processing API",
    version="1.0.0",
)

# Configure CORS
origins = [
    "http://localhost:5000",
    "https://localhost:5000",
    "http://0.0.0.0:5000",
    "https://0.0.0.0:5000",
    "http://localhost:5173",  # Vite dev server
    "http://frontend:5173",  # Docker container
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add rate limiting middleware
rate_limit = int(os.environ.get("RATE_LIMIT_PER_MINUTE", 60))
add_rate_limiter(app, rate_limit_per_minute=rate_limit)

# Add caching middleware
cache_ttl = int(os.environ.get("CACHE_TTL_SECONDS", 60))
add_cache_middleware(
    app,
    ttl=cache_ttl,
    include_paths=["/api/v1"],  # Only cache API endpoints
)


# Exception handling
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Global exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"message": "Internal server error", "detail": str(exc)},
    )


# Include API routes
app.include_router(api_router, prefix="/api")


# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "ok"}


@app.on_event("startup")
async def startup_event():
    # Startup: Initialize database connection
    await init_db()
    logger.info("Database connection established")


@app.on_event("shutdown")
async def shutdown_event():
    # Shutdown: Clean up connections
    logger.info("Shutting down application")


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="debug",
    )
