from typing import Optional

from pydantic import BaseModel, EmailStr, Field


class UserBase(BaseModel):
    email: EmailStr
    role: str = Field(..., regex=r"^(partner|attorney|paralegal|staff|client)$")
    tenant_id: str


class UserCreate(UserBase):
    password: str = Field(..., min_length=6)


class UserRead(UserBase):
    id: str


class UserUpdate(BaseModel):
    email: Optional[EmailStr]
    role: Optional[str]
    tenant_id: Optional[str]
    password: Optional[str]
