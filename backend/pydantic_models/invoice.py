from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, validator


class InvoiceBase(BaseModel):
    vendor: str = Field(..., min_length=1)
    amount: float = Field(..., gt=0)
    status: str = Field(..., regex=r"^(PENDING|PROCESSING|COMPLETED|ERROR)$")
    created_at: datetime


class InvoiceCreate(InvoiceBase):
    pass


class InvoiceRead(InvoiceBase):
    id: str


class InvoiceUpdate(BaseModel):
    vendor: Optional[str]
    amount: Optional[float]
    status: Optional[str]
    # created_at is not updatable

    @validator('status')
    def status_validator(cls, v):
        if v not in ['PENDING', 'PROCESSING', 'COMPLETED', 'ERROR']:
            raise ValueError('Invalid status')
        return v
