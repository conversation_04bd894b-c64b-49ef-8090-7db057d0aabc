from typing import List, Optional

from backend import db
from backend.models.invoice import Invoice


def get_invoice_by_id(invoice_id: str) -> Optional[Invoice]:
    return Invoice.query.get(invoice_id)


def create_invoice(invoice: Invoice) -> Invoice:
    db.session.add(invoice)
    db.session.commit()
    return invoice


def update_invoice(invoice: Invoice, update_data) -> Invoice:
    # update_data is expected to be an instance of InvoiceUpdate
    # Only update fields that are not None
    if hasattr(update_data, 'vendor') and update_data.vendor is not None:
        invoice.vendor_id = update_data.vendor
    if hasattr(update_data, 'amount') and update_data.amount is not None:
        invoice.total_amount = update_data.amount
    if hasattr(update_data, 'status') and update_data.status is not None:
        invoice.status = update_data.status
    # Add more field mappings as needed
    db.session.commit()
    return invoice


def delete_invoice(invoice: Invoice) -> None:
    db.session.delete(invoice)
    db.session.commit()


def list_invoices(tenant_id: str) -> List[Invoice]:
    return Invoice.query.filter_by(tenant_id=tenant_id).all()
