import logging
import os
from typing import Any, Dict

from backend.utils.file_utils import convert_pdf_to_image, read_file
from backend.utils.invoice_parser import normalize_invoice_data

from backend.models.invoice import (
    calculate_file_hash,
    check_duplicate_invoice,
    update_ocr_results,
)
from backend.services.ocr import process_image_with_gemini

# Configure logging
logger = logging.getLogger(__name__)


async def process_invoice_file(
    invoice_id: str, tenant_id: str, file_path: str
) -> Dict[str, Any]:
    """
    Process invoice file and extract information
    """
    try:
        # Read file content
        file_content = await read_file(file_path)

        # Get file extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        # Calculate SHA256 hash for deduplication
        file_hash = calculate_file_hash(file_content)

        # Check for duplicate invoice
        duplicate = await check_duplicate_invoice(tenant_id, file_hash)
        if duplicate and str(duplicate["_id"]) != invoice_id:
            logger.warning(f"Duplicate invoice detected: {duplicate['_id']}")
            # Update with duplicate info but continue processing
            await update_ocr_results(
                invoice_id=invoice_id,
                ocr_raw={
                    "error": "Duplicate invoice detected",
                    "duplicate_id": str(duplicate["_id"]),
                },
                extracted_fields={
                    "validated": False,
                    "isDuplicate": True,
                    "duplicateId": str(duplicate["_id"]),
                },
            )

        # Process based on file type
        if ext == ".pdf":
            # Convert first page of PDF to image
            image = await convert_pdf_to_image(file_content)
            # Process image with OCR
            ocr_result = await process_image_with_gemini(image)
        elif ext in (".jpg", ".jpeg", ".png"):
            # Process image directly
            ocr_result = await process_image_with_gemini(file_content)
        else:
            raise ValueError(f"Unsupported file type: {ext}")

        # Extract structured data
        extracted_data = ocr_result["extracted"]

        # Normalize and enrich data
        invoice_fields = await normalize_invoice_data(
            tenant_id=tenant_id, extracted_data=extracted_data
        )

        # Update invoice with OCR results and extracted fields
        updated_invoice = await update_ocr_results(
            invoice_id=invoice_id, ocr_raw=ocr_result, extracted_fields=invoice_fields
        )

        return updated_invoice

    except Exception as e:
        logger.error(f"Error processing invoice file: {str(e)}", exc_info=True)

        # Update invoice with error
        await update_ocr_results(
            invoice_id=invoice_id,
            ocr_raw={"error": str(e)},
            extracted_fields={"validated": False},
        )

        raise
