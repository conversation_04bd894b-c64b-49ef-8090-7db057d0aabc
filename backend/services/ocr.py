import base64
import io
import json
import logging
import os
from typing import Any, Dict, List, Tuple

import google.generativeai as genai
import pytesseract
from PIL import Image

# Configure logging
logger = logging.getLogger(__name__)

# Configure Gemini API
GEMINI_API_KEY = os.environ.get("GEMINI_API_KEY", "")
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)


async def process_image_with_gemini(image_content: bytes) -> Dict[str, Any]:
    """
    Use Google Gemini Flash 2.0 for OCR and information extraction
    """
    try:
        if not GEMINI_API_KEY:
            raise ValueError("Gemini API key not configured")

        # Convert image to base64
        image_base64 = base64.b64encode(image_content).decode("utf-8")

        # Create Gemini model
        model = genai.GenerativeModel("gemini-flash-2")

        # Create structured prompt
        prompt = """
        Extract the following information from this invoice image:
        1. Vendor name
        2. Invoice date (in YYYY-MM-DD format)
        3. Invoice number
        4. Memo or description
        5. VAT/Tax information (amounts and rates)

        Return the data in JSON format as follows:
        {
            "vendor_name": "...",
            "invoice_date": "YYYY-MM-DD",
            "invoice_number": "...",
            "memo": "...",
            "vat_info": [
                {"amount": 123.45, "tax_code": "...", "tax_rate": 20.0}
            ]
        }

        If any field is not found, use null for its value.
        """

        # Generate content
        response = model.generate_content(
            [
                prompt,
                {"mime_type": "image/jpeg", "data": base64.b64decode(image_base64)},
            ]
        )

        # Extract JSON from response
        response_text = response.text
        # Find JSON in response (in case there's surrounding text)
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1
        if json_start >= 0 and json_end > 0:
            json_str = response_text[json_start:json_end]
            result = json.loads(json_str)
        else:
            raise ValueError("Failed to extract JSON from Gemini response")

        # Return result and raw text
        return {"extracted": result, "raw_text": response_text, "source": "gemini"}

    except Exception as e:
        logger.error(f"Error processing image with Gemini: {str(e)}")
        logger.info("Falling back to Tesseract OCR")
        # Fall back to Tesseract
        return await process_image_with_tesseract(image_content)


async def process_image_with_tesseract(image_content: bytes) -> Dict[str, Any]:
    """
    Use Tesseract OCR as fallback for text extraction
    """
    try:
        # Load image with PIL
        image = Image.open(io.BytesIO(image_content))

        # Perform OCR
        text = pytesseract.image_to_string(image)

        # Extract information from text
        vendor_name, invoice_date, invoice_number, memo, vat_info = (
            extract_info_from_text(text)
        )

        result = {
            "vendor_name": vendor_name,
            "invoice_date": invoice_date,
            "invoice_number": invoice_number,
            "memo": memo,
            "vat_info": vat_info,
        }

        # Return result and raw text
        return {"extracted": result, "raw_text": text, "source": "tesseract"}

    except Exception as e:
        logger.error(f"Error processing image with Tesseract: {str(e)}")
        return {
            "extracted": {
                "vendor_name": None,
                "invoice_date": None,
                "invoice_number": None,
                "memo": None,
                "vat_info": [],
            },
            "raw_text": f"Error processing image: {str(e)}",
            "source": "error",
        }


def extract_info_from_text(
    text: str,
) -> Tuple[str, str, str, str, List[Dict[str, Any]]]:
    """
    Extract structured information from OCR text
    This is a basic implementation that would need refinement
    """
    import re
    from datetime import datetime

    # Vendor name: Assume it's in the first few lines
    lines = text.strip().split("\n")
    vendor_name = lines[0] if lines else None

    # Invoice date: Look for common date formats
    date_pattern = r"(?:invoice|date|dated).*?(\d{1,2}[-/\.]\d{1,2}[-/\.]\d{2,4}|\d{4}[-/\.]\d{1,2}[-/\.]\d{1,2})"
    date_match = re.search(date_pattern, text.lower())

    invoice_date = None
    if date_match:
        # Convert to YYYY-MM-DD
        date_str = date_match.group(1)
        try:
            # Try different formats
            for fmt in (
                "%d/%m/%Y",
                "%d-%m-%Y",
                "%d.%m.%Y",
                "%Y/%m/%d",
                "%Y-%m-%d",
                "%Y.%m.%d",
            ):
                try:
                    date_obj = datetime.strptime(date_str, fmt)
                    invoice_date = date_obj.strftime("%Y-%m-%d")
                    break
                except ValueError:
                    continue
        except Exception:
            pass

    # Invoice number: Look for common invoice number patterns
    number_pattern = r"(?:invoice|inv)[^\d]*?[#:]?\s*([A-Z0-9][-A-Z0-9]*)"
    number_match = re.search(number_pattern, text.lower())
    invoice_number = number_match.group(1) if number_match else None

    # Memo: Use a summary of the text or any description field
    memo_lines = []
    for line in lines:
        if re.search(r"description|details|summary", line.lower()):
            memo_lines.append(line)
    memo = " ".join(memo_lines) if memo_lines else None

    # VAT information: Look for tax-related information
    vat_info = []
    # Look for VAT percentages and amounts
    vat_pattern = r"(?:vat|tax)[^\d]*?(\d+(?:\.\d+)?)%.*?(\d+(?:\.\d+)?)"
    vat_matches = re.finditer(vat_pattern, text.lower())

    for match in vat_matches:
        try:
            rate = float(match.group(1))
            amount = float(match.group(2))
            vat_info.append(
                {"amount": amount, "tax_code": f"VAT{int(rate)}", "tax_rate": rate}
            )
        except (ValueError, IndexError):
            continue

    return vendor_name, invoice_date, invoice_number, memo, vat_info
