import json
import logging
import os
from datetime import datetime, timedelta
from typing import Any, Dict, List

import requests

# Configure logging
logger = logging.getLogger(__name__)

# QBO API configuration
QBO_CLIENT_ID = os.environ.get("QBO_CLIENT_ID", "")
QBO_CLIENT_SECRET = os.environ.get("QBO_CLIENT_SECRET", "")
QBO_REDIRECT_URI = os.environ.get("QBO_REDIRECT_URI", "")
QBO_ENVIRONMENT = os.environ.get("QBO_ENVIRONMENT", "sandbox")  # or "production"

# QBO API endpoints
if QBO_ENVIRONMENT == "sandbox":
    QBO_AUTH_URL = "https://appcenter.intuit.com/connect/oauth2"
    QBO_TOKEN_URL = "https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer"
    QBO_API_BASE = "https://sandbox-quickbooks.api.intuit.com/v3/company"
else:
    QBO_AUTH_URL = "https://appcenter.intuit.com/connect/oauth2"
    QBO_TOKEN_URL = "https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer"
    QBO_API_BASE = "https://quickbooks.api.intuit.com/v3/company"


def get_qbo_auth_url(tenant_id: str) -> str:
    """
    Generate the QBO OAuth authorization URL
    """
    if not QBO_CLIENT_ID or not QBO_REDIRECT_URI:
        raise ValueError("QBO client ID or redirect URI not configured")

    # Build authorization URL
    auth_url = f"{QBO_AUTH_URL}?client_id={QBO_CLIENT_ID}&response_type=code&scope=com.intuit.quickbooks.accounting&redirect_uri={QBO_REDIRECT_URI}&state={tenant_id}"

    return auth_url


async def exchange_code_for_tokens(code: str, realm_id: str) -> Dict[str, Any]:
    """
    Exchange authorization code for access and refresh tokens
    """
    if not QBO_CLIENT_ID or not QBO_CLIENT_SECRET or not QBO_REDIRECT_URI:
        raise ValueError("QBO client credentials not configured")

    # Prepare request
    headers = {"Content-Type": "application/x-www-form-urlencoded"}

    data = {
        "code": code,
        "redirect_uri": QBO_REDIRECT_URI,
        "grant_type": "authorization_code",
        "client_id": QBO_CLIENT_ID,
        "client_secret": QBO_CLIENT_SECRET,
    }

    # Make request
    response = requests.post(QBO_TOKEN_URL, headers=headers, data=data)

    if response.status_code != 200:
        logger.error(f"QBO token exchange failed: {response.text}")
        raise ValueError(
            f"Failed to exchange code for tokens: {response.status_code} {response.text}"
        )

    # Parse response
    token_data = response.json()

    # Calculate expiry time
    expires_in = token_data.get("expires_in", 3600)
    expires_at = datetime.utcnow() + timedelta(seconds=expires_in)

    # Create tokens dict
    tokens = {
        "access_token": token_data["access_token"],
        "refresh_token": token_data["refresh_token"],
        "expires_at": expires_at,
    }

    return tokens


async def refresh_qbo_tokens(refresh_token: str, realm_id: str) -> Dict[str, Any]:
    """
    Refresh QBO access token using refresh token
    """
    if not QBO_CLIENT_ID or not QBO_CLIENT_SECRET:
        raise ValueError("QBO client credentials not configured")

    # Prepare request
    headers = {"Content-Type": "application/x-www-form-urlencoded"}

    data = {
        "refresh_token": refresh_token,
        "grant_type": "refresh_token",
        "client_id": QBO_CLIENT_ID,
        "client_secret": QBO_CLIENT_SECRET,
    }

    # Make request
    response = requests.post(QBO_TOKEN_URL, headers=headers, data=data)

    if response.status_code != 200:
        logger.error(f"QBO token refresh failed: {response.text}")
        raise ValueError(
            f"Failed to refresh tokens: {response.status_code} {response.text}"
        )

    # Parse response
    token_data = response.json()

    # Calculate expiry time
    expires_in = token_data.get("expires_in", 3600)
    expires_at = datetime.utcnow() + timedelta(seconds=expires_in)

    # Create tokens dict
    tokens = {
        "access_token": token_data["access_token"],
        "refresh_token": token_data["refresh_token"],
        "expires_at": expires_at,
    }

    return tokens


async def push_invoice_to_qbo(
    invoice: Dict[str, Any], qbo_tokens: Dict[str, Any], realm_id: str
) -> Dict[str, Any]:
    """
    Push invoice data to QuickBooks Online as a purchase transaction
    """
    # First, check if tokens need to be refreshed
    if datetime.utcnow() >= datetime.fromisoformat(str(qbo_tokens["expires_at"])):
        qbo_tokens = await refresh_qbo_tokens(qbo_tokens["refresh_token"], realm_id)

    # Extract invoice fields
    fields = invoice.get("fields", {})
    vendor_id = fields.get("vendorId")
    date = fields.get("date")
    number = fields.get("number")
    memo = fields.get("memo")
    vat_lines = fields.get("vatLines", [])

    if not vendor_id:
        raise ValueError("Vendor ID is required")

    # Prepare request
    headers = {
        "Authorization": f"Bearer {qbo_tokens['access_token']}",
        "Content-Type": "application/json",
        "Accept": "application/json",
    }

    # Build transaction data
    txn_data = {
        "DocNumber": number,
        "TxnDate": date,
        "PrivateNote": memo,
        "EntityRef": {"value": vendor_id, "type": "Vendor"},
        "Line": [],
    }

    # Add line items
    for i, vat_line in enumerate(vat_lines):
        line_item = {
            "Id": str(i + 1),
            "DetailType": "AccountBasedExpenseLineDetail",
            "Amount": vat_line.get("amount", 0),
            "AccountBasedExpenseLineDetail": {
                "AccountRef": {
                    "value": "1"  # Default account, should be overridden by vendor's defaultAccount
                },
                "TaxCodeRef": {"value": vat_line.get("taxCode", "NON")},
                "BillableStatus": "NotBillable",
            },
        }
        txn_data["Line"].append(line_item)

    # Make request
    url = f"{QBO_API_BASE}/{realm_id}/purchase"
    response = requests.post(url, headers=headers, data=json.dumps(txn_data))

    if response.status_code not in (200, 201):
        logger.error(f"QBO purchase creation failed: {response.text}")
        raise ValueError(
            f"Failed to create purchase: {response.status_code} {response.text}"
        )

    # Parse response
    result = response.json()

    return {"txn_id": result["Purchase"]["Id"], "status": "success"}


async def get_qbo_chart_of_accounts(
    access_token: str, realm_id: str
) -> List[Dict[str, Any]]:
    """
    Get chart of accounts from QuickBooks Online
    """
    # Prepare request
    headers = {"Authorization": f"Bearer {access_token}", "Accept": "application/json"}

    # Make request
    url = f"{QBO_API_BASE}/{realm_id}/query?query=select * from Account where Active = true"
    response = requests.get(url, headers=headers)

    if response.status_code != 200:
        logger.error(f"QBO accounts query failed: {response.text}")
        raise ValueError(
            f"Failed to get accounts: {response.status_code} {response.text}"
        )

    # Parse response
    result = response.json()
    accounts = result.get("QueryResponse", {}).get("Account", [])

    # Format account data
    formatted_accounts = []
    for account in accounts:
        formatted_accounts.append(
            {
                "id": account["Id"],
                "name": account["Name"],
                "account_type": account["AccountType"],
                "fully_qualified_name": account.get(
                    "FullyQualifiedName", account["Name"]
                ),
            }
        )

    return formatted_accounts


async def get_qbo_tax_codes(access_token: str, realm_id: str) -> List[Dict[str, Any]]:
    """
    Get tax codes from QuickBooks Online
    """
    # Prepare request
    headers = {"Authorization": f"Bearer {access_token}", "Accept": "application/json"}

    # Make request
    url = f"{QBO_API_BASE}/{realm_id}/query?query=select * from TaxCode"
    response = requests.get(url, headers=headers)

    if response.status_code != 200:
        logger.error(f"QBO tax codes query failed: {response.text}")
        raise ValueError(
            f"Failed to get tax codes: {response.status_code} {response.text}"
        )

    # Parse response
    result = response.json()
    tax_codes = result.get("QueryResponse", {}).get("TaxCode", [])

    # Format tax code data
    formatted_tax_codes = []
    for tax_code in tax_codes:
        tax_rate = None
        # Try to extract tax rate
        if tax_code.get("SalesTaxRateList", {}).get("TaxRateDetail"):
            rate_details = tax_code["SalesTaxRateList"]["TaxRateDetail"]
            if isinstance(rate_details, list) and len(rate_details) > 0:
                # Use first rate if multiple
                tax_rate = float(rate_details[0].get("TaxRate", 0))
            elif isinstance(rate_details, dict):
                tax_rate = float(rate_details.get("TaxRate", 0))

        formatted_tax_codes.append(
            {
                "id": tax_code["Id"],
                "name": tax_code["Name"],
                "description": tax_code.get("Description"),
                "active": tax_code.get("Active", True),
                "tax_rate": tax_rate,
            }
        )

    return formatted_tax_codes


async def get_qbo_vendors(access_token: str, realm_id: str) -> List[Dict[str, Any]]:
    """
    Get vendors from QuickBooks Online
    """
    # Prepare request
    headers = {"Authorization": f"Bearer {access_token}", "Accept": "application/json"}

    # Make request
    url = f"{QBO_API_BASE}/{realm_id}/query?query=select * from Vendor where Active = true"
    response = requests.get(url, headers=headers)

    if response.status_code != 200:
        logger.error(f"QBO vendors query failed: {response.text}")
        raise ValueError(
            f"Failed to get vendors: {response.status_code} {response.text}"
        )

    # Parse response
    result = response.json()
    vendors = result.get("QueryResponse", {}).get("Vendor", [])

    # Format vendor data
    formatted_vendors = []
    for vendor in vendors:
        formatted_vendors.append(
            {
                "id": vendor["Id"],
                "name": vendor["DisplayName"],
                "company_name": vendor.get("CompanyName"),
                "email": vendor.get("PrimaryEmailAddr", {}).get("Address"),
            }
        )

    return formatted_vendors
