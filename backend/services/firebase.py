import logging
import os
from typing import Any, Dict, Optional

import firebase_admin
from firebase_admin import auth, credentials, storage

# Configure logging
logger = logging.getLogger(__name__)

# Firebase project configuration
FIREBASE_PROJECT_ID = os.environ.get("FIREBASE_PROJECT_ID")
FIREBASE_STORAGE_BUCKET = os.environ.get("FIREBASE_STORAGE_BUCKET")

# Initialize Firebase Admin
try:
    # Check if already initialized
    firebase_admin.get_app()
    logger.info("Firebase Admin already initialized")
except ValueError:
    # Initialize with default credentials
    try:
        logger.info("Initializing Firebase Admin...")
        # If service account JSON is provided as environment variable
        cred_json = os.environ.get("FIREBASE_CREDENTIALS")
        if cred_json:
            import json
            logger.info("Using Firebase credentials from environment variable")
            cred_dict = json.loads(cred_json)
            cred = credentials.Certificate(cred_dict)
        else:
            # Otherwise, use application default credentials
            logger.info("Using Firebase application default credentials")
            cred = credentials.ApplicationDefault()

        firebase_admin.initialize_app(
            cred,
            {
                "projectId": FIREBASE_PROJECT_ID,
                "storageBucket": FIREBASE_STORAGE_BUCKET,
            },
        )
        logger.info("Firebase Admin initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize Firebase Admin: {e}", exc_info=True)


async def verify_firebase_token(id_token: str) -> Dict[str, Any]:
    """
    Verify the Firebase ID token and return the decoded token
    """
    # Check if we're in development mode
    env = os.environ.get("ENVIRONMENT")
    logger.debug(f"Environment: {env}, Token starts with test_: {id_token.startswith('test_')}")

    if env == "development" and id_token.startswith("test_"):
        # For development, accept test tokens
        try:
            logger.debug(f"Processing test token: {id_token}")
            # Parse the token manually
            token_parts = id_token[5:].split(".")  # Remove 'test_' prefix
            logger.debug(f"Token parts: {len(token_parts)}")
            if len(token_parts) != 3:
                raise ValueError("Invalid token format")

            import base64
            import json

            # Decode the payload
            payload_b64 = token_parts[1]
            logger.debug(f"Payload base64: {payload_b64}")
            # Add padding if needed
            padding = (
                "=" * (4 - len(payload_b64) % 4) if len(payload_b64) % 4 != 0 else ""
            )
            payload_json = base64.urlsafe_b64decode(payload_b64 + padding)
            decoded_token = json.loads(payload_json)
            logger.debug(f"Decoded token: {decoded_token}")

            logger.warning("Using test token in development mode")
            return decoded_token
        except Exception as e:
            logger.error(f"Failed to decode test token: {e}", exc_info=True)
            raise ValueError(f"Invalid test token: {str(e)}")

    # For production, use Firebase verification
    try:
        logger.debug(f"Firebase auth module: {auth}")
        if auth is None:
            logger.error("Firebase auth module is None - Firebase not properly initialized")
            raise ValueError("Firebase not properly initialized")

        logger.debug(f"Attempting to verify token with Firebase auth")
        decoded_token = auth.verify_id_token(id_token)
        logger.debug(f"Firebase token verification successful")
        return decoded_token
    except Exception as e:
        logger.error(f"Failed to verify Firebase token: {e}", exc_info=True)
        raise ValueError(f"Invalid token: {str(e)}")


async def get_user_by_uid(uid: str) -> Optional[Dict[str, Any]]:
    """
    Get Firebase user by UID
    """
    try:
        user = auth.get_user(uid)
        return {
            "uid": user.uid,
            "email": user.email,
            "display_name": user.display_name,
            "email_verified": user.email_verified,
            "provider_data": [
                {"provider_id": p.provider_id} for p in user.provider_data
            ],
        }
    except Exception as e:
        logger.error(f"Failed to get Firebase user: {e}")
        return None


async def create_firebase_user(email: str, password: str) -> Dict[str, Any]:
    """
    Create a new Firebase user
    """
    try:
        user = auth.create_user(email=email, password=password, email_verified=False)
        return {"uid": user.uid, "email": user.email}
    except Exception as e:
        logger.error(f"Failed to create Firebase user: {e}")
        raise ValueError(f"Failed to create user: {str(e)}")


def get_storage_bucket():
    """
    Get Firebase Storage bucket
    """
    return storage.bucket()


async def upload_to_firebase_storage(
    file_content: bytes, tenant_id: str, invoice_id: str, file_name: str
) -> str:
    """
    Upload a file to Firebase Storage
    """
    try:
        bucket = get_storage_bucket()

        # Generate storage path
        storage_path = f"invoices/{tenant_id}/{invoice_id}/{file_name}"

        # Create blob and upload
        blob = bucket.blob(storage_path)
        blob.upload_from_string(file_content, content_type="application/octet-stream")

        # Make file publicly accessible (adjust as needed)
        blob.make_public()

        return blob.public_url
    except Exception as e:
        logger.error(f"Failed to upload to Firebase Storage: {e}")
        raise ValueError(f"Failed to upload file: {str(e)}")


async def delete_from_firebase_storage(storage_path: str) -> bool:
    """
    Delete a file from Firebase Storage
    """
    try:
        bucket = get_storage_bucket()
        blob = bucket.blob(storage_path)
        blob.delete()
        return True
    except Exception as e:
        logger.error(f"Failed to delete from Firebase Storage: {e}")
        return False
