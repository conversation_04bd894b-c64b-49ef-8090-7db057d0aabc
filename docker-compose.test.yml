version: '3.8'

services:
  # Backend test service
  backend-test:
    build:
      context: .
      dockerfile: Dockerfile
    command: sh -c "cd backend && pytest tests/ --cov=. --cov-report=xml && cp coverage.xml /app/"
    environment:
      - MONGO_URI=mongodb://mongo:27017
      - MONGO_DB_NAME=test_aiclearbill
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID:-test-project}
      - RATE_LIMIT_PER_MINUTE=100
      - CACHE_TTL_SECONDS=30
    depends_on:
      - mongo
      - redis
    networks:
      - app-network

  # Frontend test service
  frontend-test:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    command: sh -c "npm test -- --coverage"
    environment:
      - VITE_API_BASE_URL=http://backend:8000
    networks:
      - app-network

  # E2E test service
  e2e-test:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    command: sh -c "npm install && npx playwright install --with-deps && npm run build && npx playwright test"
    environment:
      - VITE_API_BASE_URL=http://backend:8000
    depends_on:
      - backend
      - mongo
      - redis
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
