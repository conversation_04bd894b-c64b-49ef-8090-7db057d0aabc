#!/usr/bin/env python3
import time
import json
import base64

# This is a simple script to generate a test JWT token for development purposes
# In a real application, you would use Firebase Admin SDK to generate tokens

# Configuration
FIREBASE_PROJECT_ID = "test-project"
USER_ID = "test_admin_user"
EMAIL = "<EMAIL>"

# Create a payload that mimics a Firebase ID token
payload = {
    "iss": f"https://securetoken.google.com/{FIREBASE_PROJECT_ID}",
    "aud": FIREBASE_PROJECT_ID,
    "auth_time": int(time.time()),
    "sub": USER_ID,
    "iat": int(time.time()),
    "exp": int(time.time()) + 3600,  # Token expires in 1 hour
    "uid": USER_ID,
    "email": EMAIL,
    "email_verified": True,
}

# Create a simple test token
header = {"alg": "none", "typ": "JWT"}
header_json = json.dumps(header).encode()
header_b64 = base64.urlsafe_b64encode(header_json).decode().rstrip("=")

payload_json = json.dumps(payload).encode()
payload_b64 = base64.urlsafe_b64encode(payload_json).decode().rstrip("=")

# For test tokens, we don't need a real signature
signature = ""

# Generate the token
token = f"test_{header_b64}.{payload_b64}.{signature}"

print(f"Test JWT Token: {token}")
print("\nUse this token in your Authorization header:")
print(f"Authorization: Bearer {token}")
