import logging
import os
from datetime import datetime, timedelta
from functools import wraps

import firebase_admin
from firebase_admin import auth, credentials
from flask import abort, g, jsonify, redirect, request, session, url_for

logger = logging.getLogger(__name__)

# Firebase app instance
firebase_app = None


def initialize_firebase():
    """Initialize Firebase Admin SDK for server-side verification"""
    global firebase_app

    # Check if already initialized
    if firebase_app:
        return firebase_app

    # Check for Firebase configuration
    try:
        # In a production environment, these would be environment variables
        # For development, we'll use Firebase API key from frontend
        firebase_project_id = os.environ.get("FIREBASE_PROJECT_ID")

        if firebase_project_id:
            # Initialize with service account if available
            if os.path.exists("firebase-service-account.json"):
                cred = credentials.Certificate("firebase-service-account.json")
                firebase_app = firebase_admin.initialize_app(cred)
                logger.info("Firebase initialized with service account file")
            else:
                cred = credentials.Certificate(
                    {
                        "type": "service_account",
                        "project_id": firebase_project_id,
                        # Add other service account fields if available
                    }
                )
                firebase_app = firebase_admin.initialize_app(cred)
                logger.info("Firebase initialized with service account")
        else:
            # Initialize without credentials for token verification only
            firebase_app = firebase_admin.initialize_app()
            logger.info("Firebase initialized without service account")

        return firebase_app

    except Exception as e:
        logger.error(f"Error initializing Firebase: {str(e)}")
        return None


def verify_id_token(id_token):
    """
    Verify the Firebase ID token

    Args:
        id_token (str): The Firebase ID token to verify

    Returns:
        dict: The decoded token if valid, None otherwise
    """
    if not id_token:
        logger.warning("Empty token provided for verification")
        return None

    try:
        # Initialize Firebase if not already done
        if not firebase_app:
            initialize_firebase()

        # Verify the ID token
        decoded_token = auth.verify_id_token(id_token)

        # Check if token is expired
        exp = decoded_token.get("exp")
        if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
            logger.warning(f"Token expired: {decoded_token.get('uid')}")
            return None

        return decoded_token

    except Exception as e:
        logger.error(f"Error verifying Firebase token: {str(e)}")
        return None


def get_user_from_session():
    """
    Get the current user from the session

    This function also checks for session expiration and refreshes Firebase token
    if needed for a seamless user experience.

    Returns:
        dict: User data from session or None if not authenticated
    """
    user_data = session.get("user_data")

    if not user_data:
        # Check for API token in Authorization header
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.split(" ")[1]
            decoded_token = verify_id_token(token)

            if decoded_token:
                from api.models.user import User

                user = User.find_by_firebase_uid(decoded_token.get("uid"))
                if user:
                    user_data = user.to_dict()
                    g.user = user_data
                    g.tenant_id = user.tenant_id
                    return user_data
                else:
                    # Set temporary user data for this request only (not in session)
                    g.user = {
                        "id": decoded_token.get("uid"),
                        "email": decoded_token.get("email"),
                    }
                    return g.user
        return None

    # Check for session expiration and refresh if needed
    last_refresh = session.get("last_refresh")
    now = datetime.utcnow()

    if last_refresh:
        try:
            refresh_time = datetime.fromisoformat(last_refresh)
            if (now - refresh_time) > timedelta(days=1):
                # Token is older than 1 day, refresh it
                session["last_refresh"] = now.isoformat()
        except (ValueError, TypeError):
            # Invalid timestamp format, reset it
            session["last_refresh"] = now.isoformat()
    else:
        # No refresh timestamp, set it
        session["last_refresh"] = now.isoformat()

    # Set user data in g for easy access
    g.user = user_data
    g.tenant_id = session.get("current_tenant_id")

    return user_data


def login_required(f):
    """
    Decorator to require login for routes

    This decorator ensures the user is authenticated before accessing protected routes.
    It handles both API and web routes appropriately.
    """

    @wraps(f)
    def decorated_function(*args, **kwargs):
        user_data = get_user_from_session()

        if not user_data:
            # For API routes, return 401 Unauthorized
            if request.path.startswith("/api/"):
                return jsonify({"error": "Authentication required"}), 401

            # For web routes, redirect to login page with 'next' parameter
            return redirect(url_for("login", next=request.url))

        # Continue to the protected route
        return f(*args, **kwargs)

    return decorated_function


def tenant_required(f):
    """
    Decorator to require a tenant selection

    This decorator ensures the user has selected a tenant before accessing
    tenant-specific routes. It handles both API and web routes appropriately.
    """

    @wraps(f)
    def decorated_function(*args, **kwargs):
        # First ensure user is logged in
        user_data = get_user_from_session()
        if not user_data:
            if request.path.startswith("/api/"):
                return jsonify({"error": "Authentication required"}), 401
            return redirect(url_for("login", next=request.url))

        # Then check tenant selection
        tenant_id = session.get("current_tenant_id")
        if not tenant_id:
            # For API routes, return 403 Forbidden
            if request.path.startswith("/api/"):
                return jsonify({"error": "Tenant selection required"}), 403

            # For web routes, redirect to tenant selection page with 'next' parameter
            return redirect(url_for("select_tenant", next=request.url))

        # Set tenant ID in g for easy access
        g.tenant_id = tenant_id

        # Continue to the protected route
        return f(*args, **kwargs)

    return decorated_function


def admin_required(f):
    """
    Decorator to require admin role

    This decorator ensures the user has admin privileges before accessing
    admin-only routes. It handles both API and web routes appropriately.
    """

    @wraps(f)
    def decorated_function(*args, **kwargs):
        # First ensure user is logged in
        user_data = get_user_from_session()
        if not user_data:
            if request.path.startswith("/api/"):
                return jsonify({"error": "Authentication required"}), 401
            return redirect(url_for("login", next=request.url))

        # Check if user is admin
        roles = user_data.get("roles", "")
        if isinstance(roles, str):
            roles = roles.split(",")

        if "admin" not in roles:
            # For API routes, return 403 Forbidden
            if request.path.startswith("/api/"):
                return jsonify({"error": "Admin access required"}), 403

            # For web routes, return 403 Forbidden page
            abort(403)

        # Continue to the protected route
        return f(*args, **kwargs)

    return decorated_function


def rate_limit(limit=100, period=60):
    """
    Decorator to apply rate limiting to routes

    Args:
        limit (int): Maximum number of requests allowed in the period
        period (int): Time period in seconds

    Note: This is a simple in-memory rate limiter for demonstration.
          In production, use Redis or similar for distributed rate limiting.
    """
    request_counts = {}

    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            now = datetime.utcnow()

            # Get client IP
            client_ip = request.remote_addr

            # Clean up expired entries
            for ip in list(request_counts.keys()):
                if (now - request_counts[ip]["timestamp"]).total_seconds() > period:
                    del request_counts[ip]

            # Check rate limit
            if client_ip in request_counts:
                count_data = request_counts[client_ip]
                if count_data["count"] >= limit:
                    return jsonify({"error": "Rate limit exceeded"}), 429
                count_data["count"] += 1
            else:
                request_counts[client_ip] = {"count": 1, "timestamp": now}

            return f(*args, **kwargs)

        return decorated_function

    return decorator
