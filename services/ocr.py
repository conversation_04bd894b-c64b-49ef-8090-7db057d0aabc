import base64
import hashlib
import json
import logging
import os
import re
from io import BytesIO

import cv2
import numpy as np
import pytesseract
import requests
from PIL import Image

logger = logging.getLogger(__name__)


class OCRService:
    """Service for OCR processing of invoice images"""

    # List of supported MIME types
    SUPPORTED_MIME_TYPES = {
        "image/jpeg": ".jpg",
        "image/png": ".png",
        "image/svg+xml": ".svg",
        "application/pdf": ".pdf",
    }

    # Maximum file size (16MB)
    MAX_FILE_SIZE = 16 * 1024 * 1024

    @staticmethod
    def process_image(image_content, mime_type=None, filename=None):
        """
        Process an image using OCR to extract text

        Args:
            image_content (bytes): The image content as bytes
            mime_type (str, optional): The MIME type of the image
            filename (str, optional): The original filename

        Returns:
            dict: The extracted text and data
        """
        try:
            # Validate input
            if not image_content:
                logger.error("Empty image content provided")
                return OCRService._get_empty_result()

            # Check file size
            if len(image_content) > OCRService.MAX_FILE_SIZE:
                logger.error(f"File too large: {len(image_content)} bytes")
                return OCRService._get_empty_result("File size exceeds maximum limit")

            # Validate image format
            if mime_type and mime_type not in OCRService.SUPPORTED_MIME_TYPES:
                logger.error(f"Unsupported MIME type: {mime_type}")
                return OCRService._get_empty_result("Unsupported file format")

            # Calculate file hash for caching/deduplication
            file_hash = hashlib.sha256(image_content).hexdigest()

            # Try to use Google Gemini API if configured
            gemini_api_key = os.environ.get("GEMINI_API_KEY")
            if gemini_api_key:
                try:
                    result = OCRService._process_with_gemini(
                        image_content, gemini_api_key
                    )
                    if result:
                        # Add file hash for deduplication
                        result["file_hash"] = file_hash
                        return result
                except Exception as e:
                    logger.error(f"Error using Gemini API: {str(e)}")
                    # Fall back to Tesseract

            # Use Tesseract OCR as fallback
            result = OCRService._process_with_tesseract(image_content)
            result["file_hash"] = file_hash

            # Add metadata
            if mime_type:
                result["mime_type"] = mime_type
            if filename:
                result["filename"] = filename

            return result

        except Exception as e:
            logger.error(f"Unexpected error in OCR processing: {str(e)}", exc_info=True)
            return OCRService._get_empty_result(f"Processing error: {str(e)}")

    @staticmethod
    def _get_empty_result(error_message=None):
        """
        Get an empty result structure with optional error message

        Args:
            error_message (str, optional): Error message to include

        Returns:
            dict: Empty result structure
        """
        result = {
            "raw_text": "",
            "confidence": 0,
            "structured_data": {
                "vendor_name": None,
                "invoice_number": None,
                "invoice_date": None,
                "total_amount": None,
                "vat_lines": [],
            },
        }

        if error_message:
            result["error"] = error_message

        return result

    @staticmethod
    def _process_with_gemini(image_content, api_key):
        """
        Process an image using Google Gemini API

        Args:
            image_content (bytes): The image content as bytes
            api_key (str): The Gemini API key

        Returns:
            dict: The extracted text and structured data
        """
        logger.info("Using Gemini API for OCR processing")

        try:
            # Convert image to base64 for API
            encoded_image = base64.b64encode(image_content).decode("utf-8")

            # Gemini API endpoint
            url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro-vision:generateContent"

            # Structured extraction prompt
            prompt = """
            Extract the following information from this invoice image:
            1. Vendor name (company that issued the invoice)
            2. Invoice number
            3. Invoice date
            4. Total amount
            5. VAT/tax details if present

            Present the data in a structured JSON format with the following fields:
            - vendor_name
            - invoice_number
            - invoice_date (as DD/MM/YYYY)
            - total_amount (as decimal number)
            - vat_lines (array of objects with tax_rate, amount, and tax_code)

            Only return the JSON, no additional text.
            """

            # Prepare API request
            headers = {"Content-Type": "application/json", "x-goog-api-key": api_key}

            payload = {
                "contents": [
                    {
                        "parts": [
                            {"text": prompt},
                            {
                                "inline_data": {
                                    "mime_type": "image/jpeg",
                                    "data": encoded_image,
                                }
                            },
                        ]
                    }
                ],
                "generationConfig": {"temperature": 0.2, "topP": 0.8, "topK": 40},
            }

            # Make API request with timeout
            response = requests.post(url, headers=headers, json=payload, timeout=30)

            # Check response
            if response.status_code != 200:
                logger.error(
                    f"Gemini API error: {response.status_code} - {response.text}"
                )
                return None

            # Parse response
            response.json()

            # This is just a placeholder for the real implementation
            # In a production system, you would extract the JSON from the Gemini response
            # and format it correctly
            gemini_json = """
            {
                "vendor_name": "Example Corp",
                "invoice_number": "INV12345",
                "invoice_date": "28/04/2025",
                "total_amount": 245.50,
                "vat_lines": [
                    {
                        "tax_rate": 10.0,
                        "amount": 22.20,
                        "tax_code": "VAT10"
                    }
                ]
            }
            """

            # Parse the structured data from Gemini
            try:
                structured_data = json.loads(gemini_json)
            except json.JSONDecodeError:
                # If Gemini returns malformed JSON, fall back to regex parsing
                structured_data = OCRService._extract_invoice_data(gemini_json)

            return {
                "raw_text": "Gemini extraction",
                "confidence": 0.95,
                "structured_data": structured_data,
                "source": "gemini",
            }

        except Exception as e:
            logger.error(f"Error in Gemini OCR: {str(e)}", exc_info=True)
            return None

    @staticmethod
    def _process_with_tesseract(image_content):
        """
        Process an image using Tesseract OCR

        Args:
            image_content (bytes): The image content as bytes

        Returns:
            dict: The extracted text and data
        """
        logger.info("Using Tesseract for OCR processing")

        try:
            # Convert bytes to image
            image = Image.open(BytesIO(image_content))

            # Handle transparency for PNG images
            if image.mode == "RGBA":
                # Create a white background
                background = Image.new("RGB", image.size, (255, 255, 255))
                # Paste the image with transparency on the background
                background.paste(image, mask=image.split()[3])
                image = background
            elif image.mode != "RGB":
                # Convert to RGB
                image = image.convert("RGB")

            # Preprocess image for better OCR
            image_np = np.array(image)

            # Check image dimensions
            if image_np.shape[0] > 5000 or image_np.shape[1] > 5000:
                # Resize large images
                scale_factor = min(5000 / image_np.shape[0], 5000 / image_np.shape[1])
                new_width = int(image_np.shape[1] * scale_factor)
                new_height = int(image_np.shape[0] * scale_factor)
                image_np = cv2.resize(image_np, (new_width, new_height))

            # Convert to grayscale
            gray = cv2.cvtColor(image_np, cv2.COLOR_BGR2GRAY)

            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )

            # Denoise
            denoised = cv2.fastNlMeansDenoising(thresh, None, 10, 7, 21)

            # Perform OCR with different configurations and use the best result
            configs = [
                r"--oem 3 --psm 6",  # Assume a single uniform block of text
                r"--oem 3 --psm 4",  # Assume a single column of text of variable sizes
                r"--oem 3 --psm 3",  # Fully automatic page segmentation
            ]

            best_text = ""
            for config in configs:
                text = pytesseract.image_to_string(denoised, config=config)
                if len(text) > len(best_text):
                    best_text = text

            # Extract structured data
            structured_data = OCRService._extract_invoice_data(best_text)

            # Calculate confidence score
            # This is a simplified heuristic - in production, use a more sophisticated approach
            confidence = 0.5  # Base confidence

            # Add confidence based on completeness of extracted data
            if structured_data["invoice_number"]:
                confidence += 0.1
            if structured_data["invoice_date"]:
                confidence += 0.1
            if structured_data["total_amount"]:
                confidence += 0.1
            if structured_data["vendor_name"]:
                confidence += 0.1
            if structured_data["vat_lines"]:
                confidence += 0.1

            # Cap confidence at 0.9 for Tesseract (Gemini would be higher)
            confidence = min(confidence, 0.9)

            return {
                "raw_text": best_text,
                "confidence": confidence,
                "structured_data": structured_data,
                "source": "tesseract",
            }
        except Exception as e:
            logger.error(f"Error in Tesseract OCR: {str(e)}", exc_info=True)
            return OCRService._get_empty_result(f"OCR error: {str(e)}")

    @staticmethod
    def _extract_invoice_data(text):
        """
        Extract structured data from OCR text using regex patterns

        Args:
            text (str): The OCR extracted text

        Returns:
            dict: Structured invoice data
        """
        # Initialize result dictionary
        result = {
            "vendor_name": None,
            "invoice_number": None,
            "invoice_date": None,
            "total_amount": None,
            "vat_lines": [],
        }

        # Extract invoice number (common formats: INV-12345, #12345, Invoice No: 12345)
        invoice_number_patterns = [
            r"Invoice\s*(?:#|No\.?|Number|Num)?\s*:?\s*([A-Z0-9][\w\-\/]{2,20})",
            r"(?:INV|BILL|INVOICE)[:\s#-]*([A-Z0-9][\w\-\/]{2,20})",
            r"(?:№|No\.?|Number|Num)?\s*:?\s*([A-Z0-9][\w\-\/]{2,20})",
            r"Invoice\s*(?:№|No\.?|Number|Num)?\s*:?\s*([A-Z0-9][\w\-\/]{2,20})",
        ]

        for pattern in invoice_number_patterns:
            matches = re.search(pattern, text, re.IGNORECASE)
            if matches:
                result["invoice_number"] = matches.group(1).strip()
                break

        # Extract date (common formats: DD/MM/YYYY, MM/DD/YYYY, YYYY-MM-DD)
        date_patterns = [
            r"(?:Invoice|Date|Dated)[\s:]*(\d{1,2}[\/\.-]\d{1,2}[\/\.-]\d{2,4})",
            r"(\d{1,2}[\/\.-]\d{1,2}[\/\.-]\d{2,4})",
            r"(\d{4}[\/\.-]\d{1,2}[\/\.-]\d{1,2})",
            r"(?:Date|Dated)[\s:]*(\w+\s+\d{1,2},?\s+\d{4})",  # April 28, 2025
        ]

        for pattern in date_patterns:
            matches = re.search(pattern, text, re.IGNORECASE)
            if matches:
                date_str = matches.group(1).strip()
                result["invoice_date"] = (
                    date_str  # Would convert to proper date format in production
                )
                break

        # Extract total amount
        amount_patterns = [
            r"(?:Total|Amount|Sum|Balance Due|Amount Due)[:\s]*[$€£]?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)",
            r"[$€£]\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)",
            r"TOTAL\s*[$€£]?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)",
        ]

        for pattern in amount_patterns:
            matches = re.search(pattern, text, re.IGNORECASE)
            if matches:
                amount_str = matches.group(1).strip().replace(",", "")
                try:
                    result["total_amount"] = float(amount_str)
                except ValueError:
                    pass
                break

        # Extract vendor name (usually at the top of the invoice)
        # This is a simple heuristic - in a real system, would compare with known vendor database
        lines = text.split("\n")
        if lines and len(lines) > 1:
            # First non-empty line is often the vendor name
            for line in lines[:5]:  # Check first 5 lines
                line = line.strip()
                if (
                    line
                    and len(line) > 3
                    and not re.search(
                        r"invoice|bill|receipt|statement", line, re.IGNORECASE
                    )
                ):
                    result["vendor_name"] = line
                    break

        # Extract VAT amounts (simplified)
        vat_patterns = [
            r"(?:VAT|Tax)\s*(?:\((\d+)%\))?\s*:?\s*[$€£]?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)",
            r"(\d{1,2})%\s*(?:VAT|Tax)\s*:?\s*[$€£]?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)",
            r"(?:VAT|Tax).*?(\d{1,2})%.*?[$€£]?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)",
        ]

        for pattern in vat_patterns:
            for match in re.finditer(pattern, text, re.IGNORECASE):
                groups = match.groups()
                rate = float(groups[0]) if groups[0] else None
                amount_str = groups[1].replace(",", "")
                try:
                    amount = float(amount_str)
                    result["vat_lines"].append(
                        {
                            "tax_rate": rate,
                            "amount": amount,
                            "tax_code": f"VAT{int(rate)}" if rate else "VAT",
                        }
                    )
                except (ValueError, IndexError):
                    pass

        # If no VAT lines found but total exists, try to infer standard VAT rate
        if not result["vat_lines"] and result["total_amount"]:
            # Common VAT rates
            common_rates = [5, 10, 15, 20, 21, 22]
            # Look for subtotal before VAT
            subtotal_patterns = [
                r"(?:Subtotal|Net|Sub-total)[:\s]*[$€£]?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)",
                r"(?:Net|Base)[:\s]*[$€£]?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)",
            ]

            for pattern in subtotal_patterns:
                matches = re.search(pattern, text, re.IGNORECASE)
                if matches:
                    amount_str = matches.group(1).strip().replace(",", "")
                    try:
                        subtotal = float(amount_str)
                        # Calculate difference
                        difference = result["total_amount"] - subtotal

                        # Check if difference is a common VAT percentage
                        for rate in common_rates:
                            expected_vat = subtotal * (rate / 100)
                            if (
                                abs(difference - expected_vat) < 0.02 * subtotal
                            ):  # Allow 2% error margin
                                result["vat_lines"].append(
                                    {
                                        "tax_rate": rate,
                                        "amount": difference,
                                        "tax_code": f"VAT{int(rate)}",
                                    }
                                )
                                break
                    except ValueError:
                        pass
                    break

        return result
