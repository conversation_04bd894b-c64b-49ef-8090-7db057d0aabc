# =============================
# Backend Environment Variables
# =============================

# Flask
FLASK_ENV=development
FLASK_DEBUG=1
SECRET_KEY=your-secret-key

# Gemini
GEMINI_API_KEY=AIzaSyDdGCvkmah6c6E2dqgV4Qu_hWR002kd71Y
GEMINI_MODEL="gemini-2.0-flash-001"

# MongoDB

MONGO_DBNAME=billsnapp
MONGO_URI=mongodb+srv://jkayobotsi:ShakaSenghor189!@cluster0billsnapp.ma3dfyh.mongodb.net/

# Optionally specify collection names if your app needs them
# MONGO_COLLECTION_INVOICES=invoices
# MONGO_COLLECTION_USERS=users

# Firebase Admin SDK (Backend)
FIREBASE_PROJECT_ID="billsnapp-b3bc2"
FIREBASE_PRIVATE_KEY_ID="7cec70b1c7ace7834bf9c55104a3127206d331f4"
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=101614238825615922979
FIREBASE_AUTH_URI="https://accounts.google.com/o/oauth2/auth"
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>
FIREBASE_DATABASE_URL=https://your-firebase-project-id.firebaseio.com
FIREBASE_STORAGE_BUCKET="billsnapp-b3bc2.firebasestorage.app"
NEXT_PUBLIC_FIREBASE_APP_ID="1:************:web:ed93e8f71596393b254d61"
# Path to service account file (optional if using above vars)
FIREBASE_SERVICE_ACCOUNT_PATH=/Users/<USER>/Documents/GitHub/AiClearBill/billsnapp-b3bc2-firebase-adminsdk-fbsvc-7cec70b1c7.json


















# =============================
# Frontend Environment Variables
# =============================

# Firebase JS SDK (Frontend)
NEXT_PUBLIC_FIREBASE_API_KEY=your-frontend-firebase-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-firebase-project-id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-firebase-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-firebase-project-id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your-measurement-id

# API URL (Frontend -> Backend)
NEXT_PUBLIC_API_URL=http://localhost:5000

# =============================
# Other Optional/3rd Party Vars
# =============================
# (Add as needed for integrations)
# QBO_CLIENT_ID=your-qbo-client-id
# QBO_CLIENT_SECRET=your-qbo-client-secret
# SENTRY_DSN=your-sentry-dsn
