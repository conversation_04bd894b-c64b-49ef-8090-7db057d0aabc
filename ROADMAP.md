# Billsnapp Project Roadmap

*Last Updated: May 30, 2025*

This document tracks the development progress of the Billsnapp project, integrating the roles, dashboard, and email-ingestion features with the existing OCR and QuickBooks core.

## 1. Core Infrastructure (100% Complete)
| Task | Status | Details |
| --- | --- | --- |
| Firebase Auth Setup | ✅ Complete | Implemented with multiple providers (Email link, Google, Apple, Intuit) |
| MongoDB Atlas Integration | ✅ Complete | Main operational database configured for complex data |
| Firestore Configuration | ✅ Complete | For lightweight, real-time data with multi-tenant isolation |
| TypeScript Configuration | ✅ Complete | Base configs in place with specialized settings for web and API |
| Core Domain Models | ✅ Complete | Invoice, Vendor, User, Tenant models implemented |

## 2. Frontend Base (95% Complete)
| Task | Status | Details |
| --- | --- | --- |
| React + TypeScript Architecture | ✅ Complete | Clean architecture with domain models, data mappers, services |
| Data Mapper Pattern | ✅ Complete | Translates between API DTOs and domain models |
| PWA & Offline Support | ✅ Complete | Service-worker, manifest, IndexedDB caching implemented |
| Mobile & Desktop UI | ✅ Complete | Responsive design with Tailwind CSS and shadcn/ui |
| TypeScript Migration | ✅ Complete | All components converted, type-checking errors resolved |
| Unit Tests | ⚠️ Partial | Some components need additional test coverage |

## 3. OCR & AI Pipeline (100% Complete)
| Task | Status | Details |
| --- | --- | --- |
| Tier 1 Extraction | ✅ Complete | Using `google/gemma-3-4b-it` on Cloud Run GPU |
| Tier 2 Edge Case Handler | ✅ Complete | Using `gemini-2.0-flash-001` via Vertex AI |
| Tier 3 Rescue Parser | ✅ Complete | Using Vertex Document AI “Invoice Parser” |
| Processing Flow | ✅ Complete | Camera capture, quality checks, storage, processing |
| ML Fine-tuning | ✅ Complete | Weekly retraining on validated invoices |

## 4. User Roles & RBAC System (100% Complete)
| Task | Status | Details |
| --- | --- | --- |
| Role Definitions | ✅ Complete | Implemented Snapper, Checker, Approver (Builder-in-Chief) roles |
| Custom Firebase Claims | ✅ Complete | Added `role`, `tenantId`, `emailAlias`, `permissions`, `pushLimit` |
| Firestore Rules Update | ✅ Complete | Comprehensive multi-tenant security rules |
| UI Role Adaptation | ✅ Complete | RoleGuard component with exhaustive tests |
| Role-based Navigation | ✅ Complete | Sidebar adapts per-role |
| Type-Safe Role Mapping | ✅ Complete | String-enum mapping resolved via TS Map |
| Documentation | ✅ Complete | Detailed RBAC docs |
| Testing | ✅ Complete | Security-rule test suite |

## 5. Dashboard Implementation (30% Complete)
| Task | Status | Details |
| --- | --- | --- |
| Invoice Table Component | ✅ Complete | Data grid with status, amount, vendor, snapper |
| Column Controls | ⚠️ Partial | Resize/reorder pending |
| Bulk Actions | ✅ Complete | Multi-select approve/delete/account change |
| Detail Drawer | ✅ Complete | Image/PDF view, OCR JSON, edit form, log history |
| Edit Logging | ✅ Complete | Field-level audit trail |
| Logs View | ✅ Complete | Centralised audit page (Approver/Admin) |
| Preferences Storage | ✅ Complete | User-level UI settings persistence |

## 6. Email Ingestion Service (0% Complete)
| Task | Status | Details |
| --- | --- | --- |
| Email Alias Generation | ❌ Not Started | Auto-generate `<tenant>.<user>@billsnap.ai` |
| Email Webhook Handler | ❌ Not Started | Process incoming emails with attachments |
| Attachment Processing | ❌ Not Started | Extract PDFs/images, enqueue for OCR |
| Email Client Integration | ❌ Not Started | “Forward-to-alias” in mobile app |
| Security Measures | ❌ Not Started | HMAC verification, virus scanning |

## 7. QuickBooks Integration Enhancements (10% Complete)
| Task | Status | Details |
| --- | --- | --- |
| Token Management | ✅ Complete | Basic OAuth flow |
| Chart-of-Accounts Sync | ❌ Not Started | Nightly refresh |
| Vendor List Sync | ❌ Not Started | On-demand refresh |
| Push Queue System | ❌ Not Started | Retry-safe Cloud Tasks push |
| Error Handling & Reporting | ❌ Not Started | Surface errors in dashboard |

## 8. ML-based Account Suggestion (0% Complete)
| Task | Status | Details |
| --- | --- | --- |
| ML Model Design | ❌ Not Started | Lightweight suggestion model |
| Training Sample Collection | ❌ Not Started | Export verified data |
| Trainer Job Implementation | ❌ Not Started | Daily retraining job |
| Suggestion Service | ❌ Not Started | Callable function with confidence scores |
| UI Integration | ❌ Not Started | Display suggestions in UI |

## 9. Logging & Audit System (5% Complete)
| Task | Status | Details |
| --- | --- | --- |
| Log Schema Design | ✅ Complete | Base schema created |
| Immutable Event Logging | ❌ Not Started | Capture edits, pushes, approvals |
| Log Timeline UI | ❌ Not Started | Filterable dashboard view |
| Export Functionality | ❌ Not Started | SOC-2 compliant export |
| Retention Policy | ❌ Not Started | 5-year retention window |

## 10. Deployment & CI/CD (80% Complete)
| Task | Status | Details |
| --- | --- | --- |
| GitHub Actions Workflow | ✅ Complete | Lint, test, deploy pipelines |
| Firebase Hosting | ✅ Complete | Production deployment |
| Cloud Functions Deployment | ✅ Complete | Automated function deploy |
| Preview Channels | ✅ Complete | Staging previews per PR |
| Monitoring & Alerting | ❌ Not Started | Custom metrics & alerts |

## 11. Architecture Migration (P1-05)
| Task | Status | Details |
| ---- | ------ | ------- |
| **Dual-Backend Migration Strategy** | ✅ Complete | Single source-of-truth plan authored in **[docs/DUAL_BACKEND_MIGRATION.md](docs/DUAL_BACKEND_MIGRATION.md)**.<br>Defines phased deprecation of the legacy Python FastAPI OCR service, consolidating all orchestration into the TypeScript Fastify backend.<br>Completion of this plan **unblocks roadmap items CE-01 & CE-02** for implementation. |

---

*This roadmap will be updated as development progresses.*
